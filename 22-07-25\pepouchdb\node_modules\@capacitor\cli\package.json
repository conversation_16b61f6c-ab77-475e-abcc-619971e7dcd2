{"name": "@capacitor/cli", "version": "7.4.2", "description": "Capacitor: Cross-platform apps with JavaScript and the web", "homepage": "https://capacitorjs.com", "author": "Ionic Team <<EMAIL>> (https://ionic.io)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ionic-team/capacitor.git"}, "bugs": {"url": "https://github.com/ionic-team/capacitor/issues"}, "files": ["assets/", "bin/", "dist/**/*.js", "dist/declarations.d.ts"], "keywords": ["ionic", "ionic framework", "capacitor", "universal app", "progressive web apps", "cross platform"], "engines": {"node": ">=20.0.0"}, "main": "dist/index.js", "types": "dist/declarations.d.ts", "bin": {"capacitor": "./bin/capacitor", "cap": "./bin/capacitor"}, "scripts": {"build": "npm run clean && npm run assets && tsc", "clean": "rimraf ./dist", "assets": "node ../scripts/pack-cli-assets.mjs", "prepublishOnly": "npm run build", "test": "jest -i", "watch": "npm run assets && tsc -w"}, "dependencies": {"@ionic/cli-framework-output": "^2.2.8", "@ionic/utils-subprocess": "^3.0.1", "@ionic/utils-terminal": "^2.3.5", "commander": "^12.1.0", "debug": "^4.4.0", "env-paths": "^2.2.0", "fs-extra": "^11.2.0", "kleur": "^4.1.5", "native-run": "^2.0.1", "open": "^8.4.0", "plist": "^3.1.0", "prompts": "^2.4.2", "rimraf": "^6.0.1", "semver": "^7.6.3", "tar": "^6.1.11", "tslib": "^2.8.1", "xml2js": "^0.6.2"}, "devDependencies": {"@types/debug": "^4.1.12", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/plist": "^3.0.5", "@types/prompts": "^2.4.9", "@types/semver": "^7.5.8", "@types/tar": "^6.1.1", "@types/tmp": "^0.2.6", "@types/xml2js": "0.4.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-jasmine2": "^29.7.0", "tmp": "^0.2.3", "ts-jest": "^29.0.5", "typescript": "~5.0.2"}, "jest": {"preset": "ts-jest", "testRunner": "jest-jasmine2"}, "publishConfig": {"access": "public"}}