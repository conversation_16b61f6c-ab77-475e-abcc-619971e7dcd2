{"name": "@angular/router", "version": "20.1.3", "description": "Angular - the routing library", "keywords": ["angular", "router"], "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git", "directory": "packages/router"}, "author": "angular", "license": "MIT", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "homepage": "https://github.com/angular/angular/tree/main/packages/router", "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": "20.1.3", "@angular/common": "20.1.3", "@angular/platform-browser": "20.1.3", "rxjs": "^6.5.3 || ^7.4.0"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": false, "module": "./fesm2022/router.mjs", "typings": "./index.d.ts", "type": "module", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/router.mjs"}, "./testing": {"types": "./testing/index.d.ts", "default": "./fesm2022/testing.mjs"}, "./upgrade": {"types": "./upgrade/index.d.ts", "default": "./fesm2022/upgrade.mjs"}}}