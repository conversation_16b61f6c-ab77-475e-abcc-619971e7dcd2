import{b as tc,c as nc,d as rc,e as oc,f as ic}from"./chunk-B7SFH74S.js";import{g as vf,h as yf}from"./chunk-UOV5QIVR.js";import{a as Sf,d as Mf}from"./chunk-GIIU5PV3.js";import{a as sc}from"./chunk-M2X7KQLB.js";import{a as Ot,d as wf,e as _f,f as bf,h as ec}from"./chunk-XTVTS2NW.js";import{a as Me,b as Df,c as Cf,d as Ef,e as ci,f as If}from"./chunk-C5RQ2IC2.js";import{b as xt}from"./chunk-42C7ZIID.js";import{a as g,b as A,d as mf,g as ae}from"./chunk-2R6CW7ES.js";function Pn(e){let t=e(r=>{Error.call(r),r.stack=new Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var He=Pn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Yy(e,n){let t=typeof n=="object";return new Promise((r,o)=>{let i=!1,s;e.subscribe({next:a=>{s=a,i=!0},error:o,complete:()=>{i?r(s):t?r(n.defaultValue):o(new He)}})})}function w(e){return typeof e=="function"}var ui=Pn(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:
${t.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=t});function $r(e,n){if(e){let t=e.indexOf(n);0<=t&&e.splice(t,1)}}var K=class e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;let{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(let i of t)i.remove(this);else t.remove(this);let{initialTeardown:r}=this;if(w(r))try{r()}catch(i){n=i instanceof ui?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Tf(i)}catch(s){n=n??[],s instanceof ui?n=[...n,...s.errors]:n.push(s)}}if(n)throw new ui(n)}}add(n){var t;if(n&&n!==this)if(this.closed)Tf(n);else{if(n instanceof e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(t=this._finalizers)!==null&&t!==void 0?t:[]).push(n)}}_hasParent(n){let{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){let{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){let{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&$r(t,n)}remove(n){let{_finalizers:t}=this;t&&$r(t,n),n instanceof e&&n._removeParent(this)}};K.EMPTY=(()=>{let e=new K;return e.closed=!0,e})();var ac=K.EMPTY;function li(e){return e instanceof K||e&&"closed"in e&&w(e.remove)&&w(e.add)&&w(e.unsubscribe)}function Tf(e){w(e)?e():e.unsubscribe()}var Ue={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Ln={setTimeout(e,n,...t){let{delegate:r}=Ln;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){let{delegate:n}=Ln;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function di(e){Ln.setTimeout(()=>{let{onUnhandledError:n}=Ue;if(n)n(e);else throw e})}function zr(){}var Af=cc("C",void 0,void 0);function Nf(e){return cc("E",void 0,e)}function Rf(e){return cc("N",e,void 0)}function cc(e,n,t){return{kind:e,value:n,error:t}}var rn=null;function Vn(e){if(Ue.useDeprecatedSynchronousErrorHandling){let n=!rn;if(n&&(rn={errorThrown:!1,error:null}),e(),n){let{errorThrown:t,error:r}=rn;if(rn=null,t)throw r}}else e()}function xf(e){Ue.useDeprecatedSynchronousErrorHandling&&rn&&(rn.errorThrown=!0,rn.error=e)}var on=class extends K{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,li(n)&&n.add(this)):this.destination=Jy}static create(n,t,r){return new jn(n,t,r)}next(n){this.isStopped?lc(Rf(n),this):this._next(n)}error(n){this.isStopped?lc(Nf(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?lc(Af,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Qy=Function.prototype.bind;function uc(e,n){return Qy.call(e,n)}var dc=class{constructor(n){this.partialObserver=n}next(n){let{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){fi(r)}}error(n){let{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){fi(r)}else fi(n)}complete(){let{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){fi(t)}}},jn=class extends on{constructor(n,t,r){super();let o;if(w(n)||!n)o={next:n??void 0,error:t??void 0,complete:r??void 0};else{let i;this&&Ue.useDeprecatedNextContext?(i=Object.create(n),i.unsubscribe=()=>this.unsubscribe(),o={next:n.next&&uc(n.next,i),error:n.error&&uc(n.error,i),complete:n.complete&&uc(n.complete,i)}):o=n}this.destination=new dc(o)}};function fi(e){Ue.useDeprecatedSynchronousErrorHandling?xf(e):di(e)}function Ky(e){throw e}function lc(e,n){let{onStoppedNotification:t}=Ue;t&&Ln.setTimeout(()=>t(e,n))}var Jy={closed:!0,next:zr,error:Ky,complete:zr};var Bn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function pe(e){return e}function fc(...e){return hc(e)}function hc(e){return e.length===0?pe:e.length===1?e[0]:function(t){return e.reduce((r,o)=>o(r),t)}}var k=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){let r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,o){let i=eD(t)?t:new jn(t,r,o);return Vn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return r=Of(r),new r((o,i)=>{let s=new jn({next:a=>{try{t(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)}[Bn](){return this}pipe(...t){return hc(t)(this)}toPromise(t){return t=Of(t),new t((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=n=>new e(n),e})();function Of(e){var n;return(n=e??Ue.Promise)!==null&&n!==void 0?n:Promise}function Xy(e){return e&&w(e.next)&&w(e.error)&&w(e.complete)}function eD(e){return e&&e instanceof on||Xy(e)&&li(e)}function pc(e){return w(e?.lift)}function P(e){return n=>{if(pc(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function x(e,n,t,r,o){return new gc(e,n,t,r,o)}var gc=class extends on{constructor(n,t,r,o,i,s){super(n),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(c){n.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){n.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:t}=this;super.unsubscribe(),!t&&((n=this.onFinalize)===null||n===void 0||n.call(this))}}};function Hn(){return P((e,n)=>{let t=null;e._refCount++;let r=x(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){t=null;return}let o=e._connection,i=t;t=null,o&&(!i||o===i)&&o.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}var Un=class extends k{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,pc(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){let n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new K;let t=this.getSubject();n.add(this.source.subscribe(x(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=K.EMPTY)}return n}refCount(){return Hn()(this)}};var kf=Pn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var U=(()=>{class e extends k{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){let r=new hi(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new kf}next(t){Vn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(t)}})}error(t){Vn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;let{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){Vn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return((t=this.observers)===null||t===void 0?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){let{hasError:r,isStopped:o,observers:i}=this;return r||o?ac:(this.currentObservers=null,i.push(t),new K(()=>{this.currentObservers=null,$r(i,t)}))}_checkFinalizedStatuses(t){let{hasError:r,thrownError:o,isStopped:i}=this;r?t.error(o):i&&t.complete()}asObservable(){let t=new k;return t.source=this,t}}return e.create=(n,t)=>new hi(n,t),e})(),hi=class extends U{constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.next)===null||r===void 0||r.call(t,n)}error(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.error)===null||r===void 0||r.call(t,n)}complete(){var n,t;(t=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||t===void 0||t.call(n)}_subscribe(n){var t,r;return(r=(t=this.source)===null||t===void 0?void 0:t.subscribe(n))!==null&&r!==void 0?r:ac}};var J=class extends U{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){let t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){let{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}};var Ee=new k(e=>e.complete());function Ff(e){return e&&w(e.schedule)}function Pf(e){return e[e.length-1]}function pi(e){return w(Pf(e))?e.pop():void 0}function kt(e){return Ff(Pf(e))?e.pop():void 0}function Gr(e,n,t,r){var o=arguments.length,i=o<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,t):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(e,n,t,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(i=(o<3?s(i):o>3?s(n,t,i):s(n,t))||i);return o>3&&i&&Object.defineProperty(n,t,i),i}function Vf(e,n,t,r){function o(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,n||[])).next())})}function Lf(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function sn(e){return this instanceof sn?(this.v=e,this):new sn(e)}function jf(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(e,n||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(v){return Promise.resolve(v).then(f,d)}}function a(f,v){r[f]&&(o[f]=function(E){return new Promise(function(T,R){i.push([f,E,T,R])>1||c(f,E)})},v&&(o[f]=v(o[f])))}function c(f,v){try{u(r[f](v))}catch(E){h(i[0][3],E)}}function u(f){f.value instanceof sn?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,v){f(v),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Bf(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof Lf=="function"?Lf(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(i){t[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var $n=e=>e&&typeof e.length=="number"&&typeof e!="function";function gi(e){return w(e?.then)}function mi(e){return w(e[Bn])}function vi(e){return Symbol.asyncIterator&&w(e?.[Symbol.asyncIterator])}function yi(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function tD(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Di=tD();function Ci(e){return w(e?.[Di])}function Ei(e){return jf(this,arguments,function*(){let t=e.getReader();try{for(;;){let{value:r,done:o}=yield sn(t.read());if(o)return yield sn(void 0);yield yield sn(r)}}finally{t.releaseLock()}})}function Ii(e){return w(e?.getReader)}function Y(e){if(e instanceof k)return e;if(e!=null){if(mi(e))return nD(e);if($n(e))return rD(e);if(gi(e))return oD(e);if(vi(e))return Hf(e);if(Ci(e))return iD(e);if(Ii(e))return sD(e)}throw yi(e)}function nD(e){return new k(n=>{let t=e[Bn]();if(w(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function rD(e){return new k(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}function oD(e){return new k(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,di)})}function iD(e){return new k(n=>{for(let t of e)if(n.next(t),n.closed)return;n.complete()})}function Hf(e){return new k(n=>{aD(e,n).catch(t=>n.error(t))})}function sD(e){return Hf(Ei(e))}function aD(e,n){var t,r,o,i;return Vf(this,void 0,void 0,function*(){try{for(t=Bf(e);r=yield t.next(),!r.done;){let s=r.value;if(n.next(s),n.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=t.return)&&(yield i.call(t))}finally{if(o)throw o.error}}n.complete()})}function Ie(e,n,t,r=0,o=!1){let i=n.schedule(function(){t(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function wi(e,n=0){return P((t,r)=>{t.subscribe(x(r,o=>Ie(r,e,()=>r.next(o),n),()=>Ie(r,e,()=>r.complete(),n),o=>Ie(r,e,()=>r.error(o),n)))})}function _i(e,n=0){return P((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function Uf(e,n){return Y(e).pipe(_i(n),wi(n))}function $f(e,n){return Y(e).pipe(_i(n),wi(n))}function zf(e,n){return new k(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}function Gf(e,n){return new k(t=>{let r;return Ie(t,n,()=>{r=e[Di](),Ie(t,n,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){t.error(s);return}i?t.complete():t.next(o)},0,!0)}),()=>w(r?.return)&&r.return()})}function bi(e,n){if(!e)throw new Error("Iterable cannot be null");return new k(t=>{Ie(t,n,()=>{let r=e[Symbol.asyncIterator]();Ie(t,n,()=>{r.next().then(o=>{o.done?t.complete():t.next(o.value)})},0,!0)})})}function Wf(e,n){return bi(Ei(e),n)}function qf(e,n){if(e!=null){if(mi(e))return Uf(e,n);if($n(e))return zf(e,n);if(gi(e))return $f(e,n);if(vi(e))return bi(e,n);if(Ci(e))return Gf(e,n);if(Ii(e))return Wf(e,n)}throw yi(e)}function W(e,n){return n?qf(e,n):Y(e)}function _(...e){let n=kt(e);return W(e,n)}function zn(e,n){let t=w(e)?e:()=>e,r=o=>o.error(t());return new k(n?o=>n.schedule(r,0,o):r)}function mc(e){return!!e&&(e instanceof k||w(e.lift)&&w(e.subscribe))}function L(e,n){return P((t,r)=>{let o=0;t.subscribe(x(r,i=>{r.next(e.call(n,i,o++))}))})}var{isArray:cD}=Array;function uD(e,n){return cD(n)?e(...n):e(n)}function Gn(e){return L(n=>uD(e,n))}var{isArray:lD}=Array,{getPrototypeOf:dD,prototype:fD,keys:hD}=Object;function Si(e){if(e.length===1){let n=e[0];if(lD(n))return{args:n,keys:null};if(pD(n)){let t=hD(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}function pD(e){return e&&typeof e=="object"&&dD(e)===fD}function Mi(e,n){return e.reduce((t,r,o)=>(t[r]=n[o],t),{})}function Wn(...e){let n=kt(e),t=pi(e),{args:r,keys:o}=Si(e);if(r.length===0)return W([],n);let i=new k(gD(r,n,o?s=>Mi(o,s):pe));return t?i.pipe(Gn(t)):i}function gD(e,n,t=pe){return r=>{Zf(n,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Zf(n,()=>{let u=W(e[c],n),l=!1;u.subscribe(x(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(t(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Zf(e,n,t){e?Ie(t,e,n):n()}function Yf(e,n,t,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&n.complete()},f=E=>u<r?v(E):c.push(E),v=E=>{i&&n.next(E),u++;let T=!1;Y(t(E,l++)).subscribe(x(n,R=>{o?.(R),i?f(R):n.next(R)},()=>{T=!0},void 0,()=>{if(T)try{for(u--;c.length&&u<r;){let R=c.shift();s?Ie(n,s,()=>v(R)):v(R)}h()}catch(R){n.error(R)}}))};return e.subscribe(x(n,f,()=>{d=!0,h()})),()=>{a?.()}}function q(e,n,t=1/0){return w(n)?q((r,o)=>L((i,s)=>n(r,i,o,s))(Y(e(r,o))),t):(typeof n=="number"&&(t=n),P((r,o)=>Yf(r,o,e,t)))}function qn(e=1/0){return q(pe,e)}function Qf(){return qn(1)}function Zn(...e){return Qf()(W(e,kt(e)))}function Wr(e){return new k(n=>{Y(e()).subscribe(n)})}function vc(...e){let n=pi(e),{args:t,keys:r}=Si(e),o=new k(i=>{let{length:s}=t;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;Y(t[l]).subscribe(x(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?Mi(r,a):a),i.complete())}))}});return n?o.pipe(Gn(n)):o}var mD=["addListener","removeListener"],vD=["addEventListener","removeEventListener"],yD=["on","off"];function qr(e,n,t,r){if(w(t)&&(r=t,t=void 0),r)return qr(e,n,t).pipe(Gn(r));let[o,i]=ED(e)?vD.map(s=>a=>e[s](n,a,t)):DD(e)?mD.map(Kf(e,n)):CD(e)?yD.map(Kf(e,n)):[];if(!o&&$n(e))return q(s=>qr(s,n,t))(Y(e));if(!o)throw new TypeError("Invalid event target");return new k(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function Kf(e,n){return t=>r=>e[t](n,r)}function DD(e){return w(e.addListener)&&w(e.removeListener)}function CD(e){return w(e.on)&&w(e.off)}function ED(e){return w(e.addEventListener)&&w(e.removeEventListener)}function ce(e,n){return P((t,r)=>{let o=0;t.subscribe(x(r,i=>e.call(n,i,o++)&&r.next(i)))})}function tt(e){return P((n,t)=>{let r=null,o=!1,i;r=n.subscribe(x(t,void 0,void 0,s=>{i=Y(e(s,tt(e)(n))),r?(r.unsubscribe(),r=null,i.subscribe(t)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(t))})}function Jf(e,n,t,r,o){return(i,s)=>{let a=t,c=n,u=0;i.subscribe(x(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function Ft(e,n){return w(n)?q(e,n,1):q(e,1)}function Pt(e){return P((n,t)=>{let r=!1;n.subscribe(x(t,o=>{r=!0,t.next(o)},()=>{r||t.next(e),t.complete()}))})}function vt(e){return e<=0?()=>Ee:P((n,t)=>{let r=0;n.subscribe(x(t,o=>{++r<=e&&(t.next(o),e<=r&&t.complete())}))})}function yc(e,n=pe){return e=e??ID,P((t,r)=>{let o,i=!0;t.subscribe(x(r,s=>{let a=n(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function ID(e,n){return e===n}function Ti(e=wD){return P((n,t)=>{let r=!1;n.subscribe(x(t,o=>{r=!0,t.next(o)},()=>r?t.complete():t.error(e())))})}function wD(){return new He}function Zr(e){return P((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function yt(e,n){let t=arguments.length>=2;return r=>r.pipe(e?ce((o,i)=>e(o,i,r)):pe,vt(1),t?Pt(n):Ti(()=>new He))}function Yn(e){return e<=0?()=>Ee:P((n,t)=>{let r=[];n.subscribe(x(t,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)t.next(o);t.complete()},void 0,()=>{r=null}))})}function Dc(e,n){let t=arguments.length>=2;return r=>r.pipe(e?ce((o,i)=>e(o,i,r)):pe,Yn(1),t?Pt(n):Ti(()=>new He))}function Cc(e,n){return P(Jf(e,n,arguments.length>=2,!0))}function Ec(...e){let n=kt(e);return P((t,r)=>{(n?Zn(e,t,n):Zn(e,t)).subscribe(r)})}function de(e,n){return P((t,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();t.subscribe(x(r,c=>{o?.unsubscribe();let u=0,l=i++;Y(e(c,l)).subscribe(o=x(r,d=>r.next(n?n(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Ai(e){return P((n,t)=>{Y(e).subscribe(x(t,()=>t.complete(),zr)),!t.closed&&n.subscribe(t)})}function te(e,n,t){let r=w(e)||n||t?{next:e,error:n,complete:t}:e;return r?P((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(x(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):pe}var Ic;function Ni(){return Ic}function nt(e){let n=Ic;return Ic=e,n}var Xf=Symbol("NotFound");function Qn(e){return e===Xf||e?.name==="\u0275NotFound"}function Fi(e,n){return Object.is(e,n)}var X=null,Ri=!1,wc=1,_D=null,ge=Symbol("SIGNAL");function N(e){let n=X;return X=e,n}function Pi(){return X}var an={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Kn(e){if(Ri)throw new Error("");if(X===null)return;X.consumerOnSignalRead(e);let n=X.nextProducerIndex++;if(Bi(X),n<X.producerNode.length&&X.producerNode[n]!==e&&Qr(X)){let t=X.producerNode[n];ji(t,X.producerIndexOfThis[n])}X.producerNode[n]!==e&&(X.producerNode[n]=e,X.producerIndexOfThis[n]=Qr(X)?th(e,X,n):0),X.producerLastReadVersion[n]=e.version}function eh(){wc++}function Li(e){if(!(Qr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===wc)){if(!e.producerMustRecompute(e)&&!Kr(e)){ki(e);return}e.producerRecomputeValue(e),ki(e)}}function _c(e){if(e.liveConsumerNode===void 0)return;let n=Ri;Ri=!0;try{for(let t of e.liveConsumerNode)t.dirty||bD(t)}finally{Ri=n}}function bc(){return X?.consumerAllowSignalWrites!==!1}function bD(e){e.dirty=!0,_c(e),e.consumerMarkedDirty?.(e)}function ki(e){e.dirty=!1,e.lastCleanEpoch=wc}function cn(e){return e&&(e.nextProducerIndex=0),N(e)}function Jn(e,n){if(N(n),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Qr(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)ji(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Kr(e){Bi(e);for(let n=0;n<e.producerNode.length;n++){let t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(Li(t),r!==t.version))return!0}return!1}function Vi(e){if(Bi(e),Qr(e))for(let n=0;n<e.producerNode.length;n++)ji(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function th(e,n,t){if(nh(e),e.liveConsumerNode.length===0&&rh(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=th(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function ji(e,n){if(nh(e),e.liveConsumerNode.length===1&&rh(e))for(let r=0;r<e.producerNode.length;r++)ji(e.producerNode[r],e.producerIndexOfThis[r]);let t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[n],o=e.liveConsumerNode[n];Bi(o),o.producerIndexOfThis[r]=n}}function Qr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Bi(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function nh(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function rh(e){return e.producerNode!==void 0}function Hi(e){_D?.(e)}function Ui(e,n){let t=Object.create(SD);t.computation=e,n!==void 0&&(t.equal=n);let r=()=>{if(Li(t),Kn(t),t.value===Yr)throw t.error;return t.value};return r[ge]=t,Hi(t),r}var xi=Symbol("UNSET"),Oi=Symbol("COMPUTING"),Yr=Symbol("ERRORED"),SD=A(g({},an),{value:xi,dirty:!0,error:null,equal:Fi,kind:"computed",producerMustRecompute(e){return e.value===xi||e.value===Oi},producerRecomputeValue(e){if(e.value===Oi)throw new Error("");let n=e.value;e.value=Oi;let t=cn(e),r,o=!1;try{r=e.computation(),N(null),o=n!==xi&&n!==Yr&&r!==Yr&&e.equal(n,r)}catch(i){r=Yr,e.error=i}finally{Jn(e,t)}if(o){e.value=n;return}e.value=r,e.version++}});function MD(){throw new Error}var oh=MD;function ih(e){oh(e)}function Sc(e){oh=e}var TD=null;function Mc(e,n){let t=Object.create($i);t.value=e,n!==void 0&&(t.equal=n);let r=()=>sh(t);return r[ge]=t,Hi(t),[r,s=>Xn(t,s),s=>Tc(t,s)]}function sh(e){return Kn(e),e.value}function Xn(e,n){bc()||ih(e),e.equal(e.value,n)||(e.value=n,AD(e))}function Tc(e,n){bc()||ih(e),Xn(e,n(e.value))}var $i=A(g({},an),{equal:Fi,value:void 0,kind:"signal"});function AD(e){e.version++,eh(),_c(e),TD?.(e)}function ah(e){let n=N(null);try{return e()}finally{N(n)}}var Zi="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",y=class extends Error{code;constructor(n,t){super(no(n,t)),this.code=n}};function ND(e){return`NG0${Math.abs(e)}`}function no(e,n){return`${ND(e)}${n?": "+n:""}`}var ro=globalThis;function B(e){for(let n in e)if(e[n]===B)return n;throw Error("")}function lh(e,n){for(let t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function Ct(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Ct).join(", ")}]`;if(e==null)return""+e;let n=e.overriddenName||e.name;if(n)return`${n}`;let t=e.toString();if(t==null)return""+t;let r=t.indexOf(`
`);return r>=0?t.slice(0,r):t}function Hc(e,n){return e?n?`${e} ${n}`:e:n||""}var RD=B({__forward_ref__:B});function we(e){return e.__forward_ref__=we,e.toString=function(){return Ct(this())},e}function ue(e){return Uc(e)?e():e}function Uc(e){return typeof e=="function"&&e.hasOwnProperty(RD)&&e.__forward_ref__===we}function dh(e,n){e==null&&$c(n,e,null,"!=")}function $c(e,n,t,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${t} ${r} ${n} <=Actual]`))}function D(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function rt(e){return{providers:e.providers||[],imports:e.imports||[]}}function oo(e){return xD(e,Yi)}function zc(e){return oo(e)!==null}function xD(e,n){return e.hasOwnProperty(n)&&e[n]||null}function OD(e){let n=e?.[Yi]??null;return n||null}function Nc(e){return e&&e.hasOwnProperty(Gi)?e[Gi]:null}var Yi=B({\u0275prov:B}),Gi=B({\u0275inj:B}),C=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(n,t){this._desc=n,this.\u0275prov=void 0,typeof t=="number"?this.__NG_ELEMENT_ID__=t:t!==void 0&&(this.\u0275prov=D({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Gc(e){return e&&!!e.\u0275providers}var Wc=B({\u0275cmp:B}),qc=B({\u0275dir:B}),Zc=B({\u0275pipe:B}),Yc=B({\u0275mod:B}),eo=B({\u0275fac:B}),pn=B({__NG_ELEMENT_ID__:B}),ch=B({__NG_ENV_ID__:B});function gn(e){return typeof e=="string"?e:e==null?"":String(e)}function Wi(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():gn(e)}var Qc=B({ngErrorCode:B}),fh=B({ngErrorMessage:B}),Xr=B({ngTokenPath:B});function Kc(e,n){return hh("",-200,n)}function Qi(e,n){throw new y(-201,!1)}function kD(e,n){e[Xr]??=[];let t=e[Xr],r;typeof n=="object"&&"multi"in n&&n?.multi===!0?(dh(n.provide,"Token with multi: true should have a provide property"),r=Wi(n.provide)):r=Wi(n),t[0]!==r&&e[Xr].unshift(r)}function FD(e,n){let t=e[Xr],r=e[Qc],o=e[fh]||e.message;return e.message=LD(o,r,t,n),e}function hh(e,n,t){let r=new y(n,e);return r[Qc]=n,r[fh]=e,t&&(r[Xr]=t),r}function PD(e){return e[Qc]}function LD(e,n,t=[],r=null){let o="";t&&t.length>1&&(o=` Path: ${t.join(" -> ")}.`);let i=r?` Source: ${r}.`:"";return no(n,`${e}${i}${o}`)}var Rc;function ph(){return Rc}function Te(e){let n=Rc;return Rc=e,n}function Jc(e,n,t){let r=oo(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(t&8)return null;if(n!==void 0)return n;Qi(e,"Injector")}var VD={},un=VD,xc="__NG_DI_FLAG__",Oc=class{injector;constructor(n){this.injector=n}retrieve(n,t){let r=ln(t)||0;try{return this.injector.get(n,r&8?null:un,r)}catch(o){if(Qn(o))return o;throw o}}};function jD(e,n=0){let t=Ni();if(t===void 0)throw new y(-203,!1);if(t===null)return Jc(e,void 0,n);{let r=BD(n),o=t.retrieve(e,r);if(Qn(o)){if(r.optional)return null;throw o}return o}}function I(e,n=0){return(ph()||jD)(ue(e),n)}function p(e,n){return I(e,ln(n))}function ln(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function BD(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function kc(e){let n=[];for(let t=0;t<e.length;t++){let r=ue(e[t]);if(Array.isArray(r)){if(r.length===0)throw new y(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=HD(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}n.push(I(o,i))}else n.push(I(r))}return n}function Xc(e,n){return e[xc]=n,e.prototype[xc]=n,e}function HD(e){return e[xc]}function dn(e,n){let t=e.hasOwnProperty(eo);return t?e[eo]:null}function gh(e,n,t){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=n[r];if(t&&(o=t(o),i=t(i)),i!==o)return!1}return!0}function mh(e){return e.flat(Number.POSITIVE_INFINITY)}function Ki(e,n){e.forEach(t=>Array.isArray(t)?Ki(t,n):n(t))}function eu(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function io(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function vh(e,n){let t=[];for(let r=0;r<e;r++)t.push(n);return t}function yh(e,n,t,r){let o=e.length;if(o==n)e.push(t,r);else if(o===1)e.push(r,e[0]),e[0]=t;else{for(o--,e.push(e[o-1],e[o]);o>n;){let i=o-2;e[o]=e[i],o--}e[n]=t,e[n+1]=r}}function Dh(e,n,t){let r=tr(e,n);return r>=0?e[r|1]=t:(r=~r,yh(e,r,n,t)),r}function Ji(e,n){let t=tr(e,n);if(t>=0)return e[t|1]}function tr(e,n){return UD(e,n,1)}function UD(e,n,t){let r=0,o=e.length>>t;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<t];if(n===s)return i<<t;s>n?o=i:r=i+1}return~(o<<t)}var jt={},Ae=[],Bt=new C(""),tu=new C("",-1),nu=new C(""),to=class{get(n,t=un){if(t===un){let o=hh("",-201);throw o.name="\u0275NotFound",o}return t}};function ru(e){return e[Yc]||null}function ot(e){return e[Wc]||null}function ou(e){return e[qc]||null}function Ch(e){return e[Zc]||null}function Xi(e){return{\u0275providers:e}}function Eh(...e){return{\u0275providers:iu(!0,e),\u0275fromNgModule:!0}}function iu(e,...n){let t=[],r=new Set,o,i=s=>{t.push(s)};return Ki(n,s=>{let a=s;qi(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Ih(o,i),t}function Ih(e,n){for(let t=0;t<e.length;t++){let{ngModule:r,providers:o}=e[t];su(o,i=>{n(i,r)})}}function qi(e,n,t,r){if(e=ue(e),!e)return!1;let o=null,i=Nc(e),s=!i&&ot(e);if(!i&&!s){let c=e.ngModule;if(i=Nc(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)qi(u,n,t,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{Ki(i.imports,l=>{qi(l,n,t,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&Ih(u,n)}if(!a){let u=dn(o)||(()=>new o);n({provide:o,useFactory:u,deps:Ae},o),n({provide:nu,useValue:o,multi:!0},o),n({provide:Bt,useValue:()=>I(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;su(c,l=>{n(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function su(e,n){for(let t of e)Gc(t)&&(t=t.\u0275providers),Array.isArray(t)?su(t,n):n(t)}var $D=B({provide:String,useValue:B});function wh(e){return e!==null&&typeof e=="object"&&$D in e}function zD(e){return!!(e&&e.useExisting)}function GD(e){return!!(e&&e.useFactory)}function fn(e){return typeof e=="function"}function _h(e){return!!e.useClass}var so=new C(""),zi={},uh={},Ac;function nr(){return Ac===void 0&&(Ac=new to),Ac}var Z=class{},hn=class extends Z{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(n,t,r,o){super(),this.parent=t,this.source=r,this.scopes=o,Pc(n,s=>this.processProvider(s)),this.records.set(tu,er(void 0,this)),o.has("environment")&&this.records.set(Z,er(void 0,this));let i=this.records.get(so);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(nu,Ae,{self:!0}))}retrieve(n,t){let r=ln(t)||0;try{return this.get(n,un,r)}catch(o){if(Qn(o))return o;throw o}}destroy(){Jr(this),this._destroyed=!0;let n=N(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let t=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),N(n)}}onDestroy(n){return Jr(this),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){Jr(this);let t=nt(this),r=Te(void 0),o;try{return n()}finally{nt(t),Te(r)}}get(n,t=un,r){if(Jr(this),n.hasOwnProperty(ch))return n[ch](this);let o=ln(r),i,s=nt(this),a=Te(void 0);try{if(!(o&4)){let u=this.records.get(n);if(u===void 0){let l=QD(n)&&oo(n);l&&this.injectableDefInScope(l)?u=er(Fc(n),zi):u=null,this.records.set(n,u)}if(u!=null)return this.hydrate(n,u,o)}let c=o&2?nr():this.parent;return t=o&8&&t===un?null:t,c.get(n,t)}catch(c){let u=PD(c);throw u===-200||u===-201?new y(u,null):c}finally{Te(a),nt(s)}}resolveInjectorInitializers(){let n=N(null),t=nt(this),r=Te(void 0),o;try{let i=this.get(Bt,Ae,{self:!0});for(let s of i)s()}finally{nt(t),Te(r),N(n)}}toString(){let n=[],t=this.records;for(let r of t.keys())n.push(Ct(r));return`R3Injector[${n.join(", ")}]`}processProvider(n){n=ue(n);let t=fn(n)?n:ue(n&&n.provide),r=qD(n);if(!fn(n)&&n.multi===!0){let o=this.records.get(t);o||(o=er(void 0,zi,!0),o.factory=()=>kc(o.multi),this.records.set(t,o)),t=n,o.multi.push(n)}this.records.set(t,r)}hydrate(n,t,r){let o=N(null);try{if(t.value===uh)throw Kc(Ct(n));return t.value===zi&&(t.value=uh,t.value=t.factory(void 0,r)),typeof t.value=="object"&&t.value&&YD(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{N(o)}}injectableDefInScope(n){if(!n.providedIn)return!1;let t=ue(n.providedIn);return typeof t=="string"?t==="any"||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){let t=this._onDestroyHooks.indexOf(n);t!==-1&&this._onDestroyHooks.splice(t,1)}};function Fc(e){let n=oo(e),t=n!==null?n.factory:dn(e);if(t!==null)return t;if(e instanceof C)throw new y(204,!1);if(e instanceof Function)return WD(e);throw new y(204,!1)}function WD(e){if(e.length>0)throw new y(204,!1);let t=OD(e);return t!==null?()=>t.factory(e):()=>new e}function qD(e){if(wh(e))return er(void 0,e.useValue);{let n=au(e);return er(n,zi)}}function au(e,n,t){let r;if(fn(e)){let o=ue(e);return dn(o)||Fc(o)}else if(wh(e))r=()=>ue(e.useValue);else if(GD(e))r=()=>e.useFactory(...kc(e.deps||[]));else if(zD(e))r=(o,i)=>I(ue(e.useExisting),i!==void 0&&i&8?8:void 0);else{let o=ue(e&&(e.useClass||e.provide));if(ZD(e))r=()=>new o(...kc(e.deps));else return dn(o)||Fc(o)}return r}function Jr(e){if(e.destroyed)throw new y(205,!1)}function er(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function ZD(e){return!!e.deps}function YD(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function QD(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Pc(e,n){for(let t of e)Array.isArray(t)?Pc(t,n):t&&Gc(t)?Pc(t.\u0275providers,n):n(t)}function me(e,n){let t;e instanceof hn?(Jr(e),t=e):t=new Oc(e);let r,o=nt(t),i=Te(void 0);try{return n()}finally{nt(o),Te(i)}}function bh(){return ph()!==void 0||Ni()!=null}var ze=0,b=1,S=2,ne=3,Fe=4,ve=5,rr=6,or=7,fe=8,mn=9,it=10,Q=11,ir=12,cu=13,vn=14,_e=15,Ht=16,yn=17,st=18,ao=19,uu=20,Dt=21,es=22,co=23,Ne=24,Dn=25,oe=26,Sh=1;var Ut=7,uo=8,Cn=9,ye=10;function at(e){return Array.isArray(e)&&typeof e[Sh]=="object"}function Ge(e){return Array.isArray(e)&&e[Sh]===!0}function lu(e){return(e.flags&4)!==0}function $t(e){return e.componentOffset>-1}function sr(e){return(e.flags&1)===1}function ct(e){return!!e.template}function ar(e){return(e[S]&512)!==0}function En(e){return(e[S]&256)===256}var Mh="svg",Th="math";function Pe(e){for(;Array.isArray(e);)e=e[ze];return e}function du(e,n){return Pe(n[e])}function We(e,n){return Pe(n[e.index])}function ts(e,n){return e.data[n]}function Le(e,n){let t=n[e];return at(t)?t:t[ze]}function Ah(e){return(e[S]&4)===4}function ns(e){return(e[S]&128)===128}function Nh(e){return Ge(e[ne])}function zt(e,n){return n==null?null:e[n]}function fu(e){e[yn]=0}function hu(e){e[S]&1024||(e[S]|=1024,ns(e)&&cr(e))}function Rh(e,n){for(;e>0;)n=n[vn],e--;return n}function lo(e){return!!(e[S]&9216||e[Ne]?.dirty)}function rs(e){e[it].changeDetectionScheduler?.notify(8),e[S]&64&&(e[S]|=1024),lo(e)&&cr(e)}function cr(e){e[it].changeDetectionScheduler?.notify(0);let n=Lt(e);for(;n!==null&&!(n[S]&8192||(n[S]|=8192,!ns(n)));)n=Lt(n)}function pu(e,n){if(En(e))throw new y(911,!1);e[Dt]===null&&(e[Dt]=[]),e[Dt].push(n)}function xh(e,n){if(e[Dt]===null)return;let t=e[Dt].indexOf(n);t!==-1&&e[Dt].splice(t,1)}function Lt(e){let n=e[ne];return Ge(n)?n[ne]:n}function gu(e){return e[or]??=[]}function mu(e){return e.cleanup??=[]}function Oh(e,n,t,r){let o=gu(n);o.push(t),e.firstCreatePass&&mu(e).push(r,o.length-1)}var O={lFrame:Yh(null),bindingsEnabled:!0,skipHydrationRootTNode:null},fo=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(fo||{}),KD=0,Lc=!1;function kh(){return O.lFrame.elementDepthCount}function Fh(){O.lFrame.elementDepthCount++}function Ph(){O.lFrame.elementDepthCount--}function os(){return O.bindingsEnabled}function vu(){return O.skipHydrationRootTNode!==null}function Lh(e){return O.skipHydrationRootTNode===e}function Vh(){O.skipHydrationRootTNode=null}function V(){return O.lFrame.lView}function he(){return O.lFrame.tView}function jh(e){return O.lFrame.contextLView=e,e[fe]}function Bh(e){return O.lFrame.contextLView=null,e}function ie(){let e=yu();for(;e!==null&&e.type===64;)e=e.parent;return e}function yu(){return O.lFrame.currentTNode}function Hh(){let e=O.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}function ur(e,n){let t=O.lFrame;t.currentTNode=e,t.isParent=n}function Du(){return O.lFrame.isParent}function Cu(){O.lFrame.isParent=!1}function Eu(e){$c("Must never be called in production mode"),KD=e}function Iu(){return Lc}function wu(e){let n=Lc;return Lc=e,n}function Uh(){return O.lFrame.bindingIndex}function $h(e){return O.lFrame.bindingIndex=e}function is(){return O.lFrame.bindingIndex++}function _u(e){let n=O.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function zh(){return O.lFrame.inI18n}function Gh(e,n){let t=O.lFrame;t.bindingIndex=t.bindingRootIndex=e,ss(n)}function Wh(){return O.lFrame.currentDirectiveIndex}function ss(e){O.lFrame.currentDirectiveIndex=e}function qh(e){let n=O.lFrame.currentDirectiveIndex;return n===-1?null:e[n]}function bu(){return O.lFrame.currentQueryIndex}function as(e){O.lFrame.currentQueryIndex=e}function JD(e){let n=e[b];return n.type===2?n.declTNode:n.type===1?e[ve]:null}function Su(e,n,t){if(t&4){let o=n,i=e;for(;o=o.parent,o===null&&!(t&1);)if(o=JD(i),o===null||(i=i[vn],o.type&10))break;if(o===null)return!1;n=o,e=i}let r=O.lFrame=Zh();return r.currentTNode=n,r.lView=e,!0}function cs(e){let n=Zh(),t=e[b];O.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function Zh(){let e=O.lFrame,n=e===null?null:e.child;return n===null?Yh(e):n}function Yh(e){let n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=n),n}function Qh(){let e=O.lFrame;return O.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Mu=Qh;function us(){let e=Qh();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Kh(e){return(O.lFrame.contextLView=Rh(e,O.lFrame.contextLView))[fe]}function Gt(){return O.lFrame.selectedIndex}function Wt(e){O.lFrame.selectedIndex=e}function Tu(){let e=O.lFrame;return ts(e.tView,e.selectedIndex)}function Jh(){return O.lFrame.currentNamespace}var Xh=!0;function ls(){return Xh}function ho(e){Xh=e}function Vc(e,n=null,t=null,r){let o=Au(e,n,t,r);return o.resolveInjectorInitializers(),o}function Au(e,n=null,t=null,r,o=new Set){let i=[t||Ae,Eh(e)];return r=r||(typeof e=="object"?void 0:Ct(e)),new hn(i,n||nr(),r||null,o)}var re=class e{static THROW_IF_NOT_FOUND=un;static NULL=new to;static create(n,t){if(Array.isArray(n))return Vc({name:""},t,n,"");{let r=n.name??"";return Vc({name:r},n.parent,n.providers,r)}}static \u0275prov=D({token:e,providedIn:"any",factory:()=>I(tu)});static __NG_ELEMENT_ID__=-1},ee=new C(""),ut=(()=>{class e{static __NG_ELEMENT_ID__=XD;static __NG_ENV_ID__=t=>t}return e})(),jc=class extends ut{_lView;constructor(n){super(),this._lView=n}get destroyed(){return En(this._lView)}onDestroy(n){let t=this._lView;return pu(t,n),()=>xh(t,n)}};function XD(){return new jc(V())}var $e=class{_console=console;handleError(n){this._console.error("ERROR",n)}},Re=new C("",{providedIn:"root",factory:()=>{let e=p(Z),n;return t=>{e.destroyed&&!n?setTimeout(()=>{throw t}):(n??=e.get($e),n.handleError(t))}}}),ep={provide:Bt,useValue:()=>void p($e),multi:!0};function Et(e,n){let[t,r,o]=Mc(e,n?.equal),i=t,s=i[ge];return i.set=r,i.update=o,i.asReadonly=tp.bind(i),i}function tp(){let e=this[ge];if(e.readonlyFn===void 0){let n=()=>this();n[ge]=e,e.readonlyFn=n}return e.readonlyFn}var Vt=class{},ds=new C("",{providedIn:"root",factory:()=>!1});var Nu=new C(""),Ru=new C("");var fs=(()=>{class e{view;node;constructor(t,r){this.view=t,this.node=r}static __NG_ELEMENT_ID__=eC}return e})();function eC(){return new fs(V(),ie())}var It=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new J(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new k(t=>{t.next(!1),t.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let t=this.taskId++;return this.pendingTasks.add(t),t}has(t){return this.pendingTasks.has(t)}remove(t){this.pendingTasks.delete(t),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})();function po(...e){}var xu=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>new Bc})}return e})(),Bc=class{dirtyEffectCount=0;queues=new Map;add(n){this.enqueue(n),this.schedule(n)}schedule(n){n.dirty&&this.dirtyEffectCount++}remove(n){let t=n.zone,r=this.queues.get(t);r.has(n)&&(r.delete(n),n.dirty&&this.dirtyEffectCount--)}enqueue(n){let t=n.zone;this.queues.has(t)||this.queues.set(t,new Set);let r=this.queues.get(t);r.has(n)||r.add(n)}flush(){for(;this.dirtyEffectCount>0;){let n=!1;for(let[t,r]of this.queues)t===null?n||=this.flushQueue(r):n||=t.run(()=>this.flushQueue(r));n||(this.dirtyEffectCount=0)}}flushQueue(n){let t=!1;for(let r of n)r.dirty&&(this.dirtyEffectCount--,t=!0,r.run());return t}};function mr(e){return{toString:e}.toString()}var hs="__parameters__";function dC(e){return function(...t){if(e){let r=e(...t);for(let o in r)this[o]=r[o]}}}function Np(e,n,t){return mr(()=>{let r=dC(n);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(hs)?c[hs]:Object.defineProperty(c,hs,{value:[]})[hs];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Rp=Xc(Np("Optional"),8);var xp=Xc(Np("SkipSelf"),4);function fC(e){return typeof e=="function"}var Cs=class{previousValue;currentValue;firstChange;constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}};function Op(e,n,t,r){n!==null?n.applyValueToInputSignal(n,r):e[t]=r}var be=(()=>{let e=()=>kp;return e.ngInherit=!0,e})();function kp(e){return e.type.prototype.ngOnChanges&&(e.setInput=pC),hC}function hC(){let e=Pp(this),n=e?.current;if(n){let t=e.previous;if(t===jt)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function pC(e,n,t,r,o){let i=this.declaredInputs[r],s=Pp(e)||gC(e,{previous:jt,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new Cs(u&&u.currentValue,t,c===jt),Op(e,n,o,t)}var Fp="__ngSimpleChanges__";function Pp(e){return e[Fp]||null}function gC(e,n){return e[Fp]=n}var np=[];var H=function(e,n=null,t){for(let r=0;r<np.length;r++){let o=np[r];o(e,n,t)}};function mC(e,n,t){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=n.type.prototype;if(r){let s=kp(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}o&&(t.preOrderHooks??=[]).push(0-e,o),i&&((t.preOrderHooks??=[]).push(e,i),(t.preOrderCheckHooks??=[]).push(e,i))}function Lp(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){let i=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),c&&(e.viewHooks??=[]).push(-t,c),u&&((e.viewHooks??=[]).push(t,u),(e.viewCheckHooks??=[]).push(t,u)),l!=null&&(e.destroyHooks??=[]).push(t,l)}}function ms(e,n,t){Vp(e,n,3,t)}function vs(e,n,t,r){(e[S]&3)===t&&Vp(e,n,t,r)}function Ou(e,n){let t=e[S];(t&3)===n&&(t&=16383,t+=1,e[S]=t)}function Vp(e,n,t,r){let o=r!==void 0?e[yn]&65535:0,i=r??-1,s=n.length-1,a=0;for(let c=o;c<s;c++)if(typeof n[c+1]=="number"){if(a=n[c],r!=null&&a>=r)break}else n[c]<0&&(e[yn]+=65536),(a<i||i==-1)&&(vC(e,t,n,c),e[yn]=(e[yn]&**********)+c+2),c++}function rp(e,n){H(4,e,n);let t=N(null);try{n.call(e)}finally{N(t),H(5,e,n)}}function vC(e,n,t,r){let o=t[r]<0,i=t[r+1],s=o?-t[r]:t[r],a=e[s];o?e[S]>>14<e[yn]>>16&&(e[S]&3)===n&&(e[S]+=16384,rp(a,i)):rp(a,i)}var dr=-1,wn=class{factory;name;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(n,t,r,o){this.factory=n,this.name=o,this.canSeeViewProviders=t,this.injectImpl=r}};function yC(e){return(e.flags&8)!==0}function DC(e){return(e.flags&16)!==0}function CC(e,n,t){let r=0;for(;r<t.length;){let o=t[r];if(typeof o=="number"){if(o!==0)break;r++;let i=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,i)}else{let i=o,s=t[++r];EC(i)?e.setProperty(n,i,s):e.setAttribute(n,i,s),r++}}return r}function jp(e){return e===3||e===4||e===6}function EC(e){return e.charCodeAt(0)===64}function fr(e,n){if(!(n===null||n.length===0))if(e===null||e.length===0)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){let o=n[r];typeof o=="number"?t=o:t===0||(t===-1||t===2?op(e,t,o,null,n[++r]):op(e,t,o,null,null))}}return e}function op(e,n,t,r,o){let i=0,s=e.length;if(n===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===n){s=-1;break}else if(a>n){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===t){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,n),i=s+1),e.splice(i++,0,t),o!==null&&e.splice(i++,0,o)}function Bp(e){return e!==dr}function Es(e){return e&32767}function IC(e){return e>>16}function Is(e,n){let t=IC(e),r=n;for(;t>0;)r=r[vn],t--;return r}var $u=!0;function ip(e){let n=$u;return $u=e,n}var wC=256,Hp=wC-1,Up=5,_C=0,lt={};function bC(e,n,t){let r;typeof t=="string"?r=t.charCodeAt(0)||0:t.hasOwnProperty(pn)&&(r=t[pn]),r==null&&(r=t[pn]=_C++);let o=r&Hp,i=1<<o;n.data[e+(o>>Up)]|=i}function ws(e,n){let t=$p(e,n);if(t!==-1)return t;let r=n[b];r.firstCreatePass&&(e.injectorIndex=n.length,ku(r.data,e),ku(n,null),ku(r.blueprint,null));let o=gl(e,n),i=e.injectorIndex;if(Bp(o)){let s=Es(o),a=Is(o,n),c=a[b].data;for(let u=0;u<8;u++)n[i+u]=a[s+u]|c[s+u]}return n[i+8]=o,i}function ku(e,n){e.push(0,0,0,0,0,0,0,0,n)}function $p(e,n){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||n[e.injectorIndex+8]===null?-1:e.injectorIndex}function gl(e,n){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let t=0,r=null,o=n;for(;o!==null;){if(r=Zp(o),r===null)return dr;if(t++,o=o[vn],r.injectorIndex!==-1)return r.injectorIndex|t<<16}return dr}function zu(e,n,t){bC(e,n,t)}function SC(e,n){if(n==="class")return e.classes;if(n==="style")return e.styles;let t=e.attrs;if(t){let r=t.length,o=0;for(;o<r;){let i=t[o];if(jp(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof t[o]=="string";)o++;else{if(i===n)return t[o+1];o=o+2}}}return null}function zp(e,n,t){if(t&8||e!==void 0)return e;Qi(n,"NodeInjector")}function Gp(e,n,t,r){if(t&8&&r===void 0&&(r=null),(t&3)===0){let o=e[mn],i=Te(void 0);try{return o?o.get(n,r,t&8):Jc(n,r,t&8)}finally{Te(i)}}return zp(r,n,t)}function Wp(e,n,t,r=0,o){if(e!==null){if(n[S]&2048&&!(r&2)){let s=NC(e,n,t,r,lt);if(s!==lt)return s}let i=qp(e,n,t,r,lt);if(i!==lt)return i}return Gp(n,t,r,o)}function qp(e,n,t,r,o){let i=TC(t);if(typeof i=="function"){if(!Su(n,e,r))return r&1?zp(o,t,r):Gp(n,t,r,o);try{let s;if(s=i(r),s==null&&!(r&8))Qi(t);else return s}finally{Mu()}}else if(typeof i=="number"){let s=null,a=$p(e,n),c=dr,u=r&1?n[_e][ve]:null;for((a===-1||r&4)&&(c=a===-1?gl(e,n):n[a+8],c===dr||!ap(r,!1)?a=-1:(s=n[b],a=Es(c),n=Is(c,n)));a!==-1;){let l=n[b];if(sp(i,a,l.data)){let d=MC(a,n,t,s,r,u);if(d!==lt)return d}c=n[a+8],c!==dr&&ap(r,n[b].data[a+8]===u)&&sp(i,a,n)?(s=l,a=Es(c),n=Is(c,n)):a=-1}}return o}function MC(e,n,t,r,o,i){let s=n[b],a=s.data[e+8],c=r==null?$t(a)&&$u:r!=s&&(a.type&3)!==0,u=o&1&&i===a,l=ys(a,s,t,c,u);return l!==null?vo(n,s,l,a,o):lt}function ys(e,n,t,r,o){let i=e.providerIndexes,s=n.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let v=s[f];if(f<c&&t===v||f>=c&&v.type===t)return f}if(o){let f=s[c];if(f&&ct(f)&&f.type===t)return c}return null}function vo(e,n,t,r,o){let i=e[t],s=n.data;if(i instanceof wn){let a=i;if(a.resolving){let f=Wi(s[t]);throw Kc(f)}let c=ip(a.canSeeViewProviders);a.resolving=!0;let u=s[t].type||s[t],l,d=a.injectImpl?Te(a.injectImpl):null,h=Su(e,r,0);try{i=e[t]=a.factory(void 0,o,s,e,r),n.firstCreatePass&&t>=r.directiveStart&&mC(t,s[t],n)}finally{d!==null&&Te(d),ip(c),a.resolving=!1,Mu()}}return i}function TC(e){if(typeof e=="string")return e.charCodeAt(0)||0;let n=e.hasOwnProperty(pn)?e[pn]:void 0;return typeof n=="number"?n>=0?n&Hp:AC:n}function sp(e,n,t){let r=1<<e;return!!(t[n+(e>>Up)]&r)}function ap(e,n){return!(e&2)&&!(e&1&&n)}var In=class{_tNode;_lView;constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return Wp(this._tNode,this._lView,n,ln(r),t)}};function AC(){return new In(ie(),V())}function Ye(e){return mr(()=>{let n=e.prototype.constructor,t=n[eo]||Gu(n),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[eo]||Gu(o);if(i&&i!==t)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Gu(e){return Uc(e)?()=>{let n=Gu(ue(e));return n&&n()}:dn(e)}function NC(e,n,t,r,o){let i=e,s=n;for(;i!==null&&s!==null&&s[S]&2048&&!ar(s);){let a=qp(i,s,t,r|2,lt);if(a!==lt)return a;let c=i.parent;if(!c){let u=s[uu];if(u){let l=u.get(t,lt,r);if(l!==lt)return l}c=Zp(s),s=s[vn]}i=c}return o}function Zp(e){let n=e[b],t=n.type;return t===2?n.declTNode:t===1?e[ve]:null}function Yt(e){return SC(ie(),e)}function RC(){return vr(ie(),V())}function vr(e,n){return new G(We(e,n))}var G=(()=>{class e{nativeElement;constructor(t){this.nativeElement=t}static __NG_ELEMENT_ID__=RC}return e})();function xC(e){return e instanceof G?e.nativeElement:e}function OC(){return this._results[Symbol.iterator]()}var _s=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new U}constructor(n=!1){this._emitDistinctChangesOnly=n}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;let r=mh(n);(this._changesDetected=!gh(this._results,r,t))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=OC};function Yp(e){return(e.flags&128)===128}var ml=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(ml||{}),Qp=new Map,kC=0;function FC(){return kC++}function PC(e){Qp.set(e[ao],e)}function Wu(e){Qp.delete(e[ao])}var cp="__ngContext__";function hr(e,n){at(n)?(e[cp]=n[ao],PC(n)):e[cp]=n}function Kp(e){return Xp(e[ir])}function Jp(e){return Xp(e[Fe])}function Xp(e){for(;e!==null&&!Ge(e);)e=e[Fe];return e}var qu;function vl(e){qu=e}function eg(){if(qu!==void 0)return qu;if(typeof document<"u")return document;throw new y(210,!1)}var Vs=new C("",{providedIn:"root",factory:()=>LC}),LC="ng",js=new C(""),yr=new C("",{providedIn:"platform",factory:()=>"unknown"});var Bs=new C("",{providedIn:"root",factory:()=>eg().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var VC="h",jC="b";var tg=!1,ng=new C("",{providedIn:"root",factory:()=>tg});var BC=(e,n,t,r)=>{};function HC(e,n,t,r){BC(e,n,t,r)}function Hs(e){return(e.flags&32)===32}var UC=()=>null;function rg(e,n,t=!1){return UC(e,n,t)}function og(e,n){let t=e.contentQueries;if(t!==null){let r=N(null);try{for(let o=0;o<t.length;o+=2){let i=t[o],s=t[o+1];if(s!==-1){let a=e.data[s];as(i),a.contentQueries(2,n[s],s)}}}finally{N(r)}}}function Zu(e,n,t){as(0);let r=N(null);try{n(e,t)}finally{N(r)}}function yl(e,n,t){if(lu(n)){let r=N(null);try{let o=n.directiveStart,i=n.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=t[s];a.contentQueries(1,c,s)}}}finally{N(r)}}}var wt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(wt||{});var ps;function $C(){if(ps===void 0&&(ps=null,ro.trustedTypes))try{ps=ro.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ps}function up(e){return $C()?.createScriptURL(e)||e}var bs=class{changingThisBreaksApplicationSecurity;constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Zi})`}};function Us(e){return e instanceof bs?e.changingThisBreaksApplicationSecurity:e}function Dl(e,n){let t=ig(e);if(t!=null&&t!==n){if(t==="ResourceURL"&&n==="URL")return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${Zi})`)}return t===n}function ig(e){return e instanceof bs&&e.getTypeName()||null}var zC=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function sg(e){return e=String(e),e.match(zC)?e:"unsafe:"+e}var $s=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}($s||{});function ag(e){let n=ug();return n?n.sanitize($s.URL,e)||"":Dl(e,"URL")?Us(e):sg(gn(e))}function cg(e){let n=ug();if(n)return up(n.sanitize($s.RESOURCE_URL,e)||"");if(Dl(e,"ResourceURL"))return up(Us(e));throw new y(904,!1)}function GC(e,n){return n==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||n==="href"&&(e==="base"||e==="link")?cg:ag}function Cl(e,n,t){return GC(n,t)(e)}function ug(){let e=V();return e&&e[it].sanitizer}var WC=/^>|^->|<!--|-->|--!>|<!-$/g,qC=/(<|>)/g,ZC="\u200B$1\u200B";function YC(e){return e.replace(WC,n=>n.replace(qC,ZC))}function lg(e){return e instanceof Function?e():e}function QC(e,n,t){let r=e.length;for(;;){let o=e.indexOf(n,t);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=n.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}t=o+1}}var dg="ng-template";function KC(e,n,t,r){let o=0;if(r){for(;o<n.length&&typeof n[o]=="string";o+=2)if(n[o]==="class"&&QC(n[o+1].toLowerCase(),t,0)!==-1)return!0}else if(El(e))return!1;if(o=n.indexOf(1,o),o>-1){let i;for(;++o<n.length&&typeof(i=n[o])=="string";)if(i.toLowerCase()===t)return!0}return!1}function El(e){return e.type===4&&e.value!==dg}function JC(e,n,t){let r=e.type===4&&!t?dg:e.value;return n===r}function XC(e,n,t){let r=4,o=e.attrs,i=o!==null?nE(o):0,s=!1;for(let a=0;a<n.length;a++){let c=n[a];if(typeof c=="number"){if(!s&&!qe(r)&&!qe(c))return!1;if(s&&qe(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!JC(e,c,t)||c===""&&n.length===1){if(qe(r))return!1;s=!0}}else if(r&8){if(o===null||!KC(e,o,c,t)){if(qe(r))return!1;s=!0}}else{let u=n[++a],l=eE(c,o,El(e),t);if(l===-1){if(qe(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(qe(r))return!1;s=!0}}}}return qe(r)||s}function qe(e){return(e&1)===0}function eE(e,n,t,r){if(n===null)return-1;let o=0;if(r||!t){let i=!1;for(;o<n.length;){let s=n[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=n[++o];for(;typeof a=="string";)a=n[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return rE(n,e)}function fg(e,n,t=!1){for(let r=0;r<n.length;r++)if(XC(e,n[r],t))return!0;return!1}function tE(e){let n=e.attrs;if(n!=null){let t=n.indexOf(5);if((t&1)===0)return n[t+1]}return null}function nE(e){for(let n=0;n<e.length;n++){let t=e[n];if(jp(t))return n}return e.length}function rE(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){let r=e[t];if(typeof r=="number")return-1;if(r===n)return t;t++}return-1}function oE(e,n){e:for(let t=0;t<n.length;t++){let r=n[t];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function lp(e,n){return e?":not("+n.trim()+")":n}function iE(e){let n=e[0],t=1,r=2,o="",i=!1;for(;t<e.length;){let s=e[t];if(typeof s=="string")if(r&2){let a=e[++t];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!qe(s)&&(n+=lp(i,o),o=""),r=s,i=i||!qe(r);t++}return o!==""&&(n+=lp(i,o)),n}function sE(e){return e.map(iE).join(",")}function aE(e){let n=[],t=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&n.push(i,e[++r]):o===8&&t.push(i);else{if(!qe(o))break;o=i}r++}return t.length&&n.push(1,...t),n}var ft={};function cE(e,n){return e.createText(n)}function uE(e,n,t){e.setValue(n,t)}function lE(e,n){return e.createComment(YC(n))}function hg(e,n,t){return e.createElement(n,t)}function Ss(e,n,t,r,o){e.insertBefore(n,t,r,o)}function pg(e,n,t){e.appendChild(n,t)}function dp(e,n,t,r,o){r!==null?Ss(e,n,t,r,o):pg(e,n,t)}function dE(e,n,t){e.removeChild(null,n,t)}function fE(e,n,t){e.setAttribute(n,"style",t)}function hE(e,n,t){t===""?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function gg(e,n,t){let{mergedAttrs:r,classes:o,styles:i}=t;r!==null&&CC(e,n,r),o!==null&&hE(e,n,o),i!==null&&fE(e,n,i)}function Il(e,n,t,r,o,i,s,a,c,u,l){let d=oe+r,h=d+o,f=pE(d,h),v=typeof u=="function"?u():u;return f[b]={type:e,blueprint:f,template:t,queries:null,viewQuery:a,declTNode:n,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:v,incompleteFirstPass:!1,ssrId:l}}function pE(e,n){let t=[];for(let r=0;r<n;r++)t.push(r<e?null:ft);return t}function gE(e){let n=e.tView;return n===null||n.incompleteFirstPass?e.tView=Il(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function wl(e,n,t,r,o,i,s,a,c,u,l){let d=n.blueprint.slice();return d[ze]=o,d[S]=r|4|128|8|64|1024,(u!==null||e&&e[S]&2048)&&(d[S]|=2048),fu(d),d[ne]=d[vn]=e,d[fe]=t,d[it]=s||e&&e[it],d[Q]=a||e&&e[Q],d[mn]=c||e&&e[mn]||null,d[ve]=i,d[ao]=FC(),d[rr]=l,d[uu]=u,d[_e]=n.type==2?e[_e]:d,d}function mE(e,n,t){let r=We(n,e),o=gE(t),i=e[it].rendererFactory,s=_l(e,wl(e,o,null,mg(t),r,n,null,i.createRenderer(r,t),null,null,null));return e[n.index]=s}function mg(e){let n=16;return e.signals?n=4096:e.onPush&&(n=64),n}function vg(e,n,t,r){if(t===0)return-1;let o=n.length;for(let i=0;i<t;i++)n.push(r),e.blueprint.push(r),e.data.push(null);return o}function _l(e,n){return e[ir]?e[cu][Fe]=n:e[ir]=n,e[cu]=n,n}function vE(e=1){yg(he(),V(),Gt()+e,!1)}function yg(e,n,t,r){if(!r)if((n[S]&3)===3){let i=e.preOrderCheckHooks;i!==null&&ms(n,i,t)}else{let i=e.preOrderHooks;i!==null&&vs(n,i,0,t)}Wt(t)}var zs=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(zs||{});function Yu(e,n,t,r){let o=N(null);try{let[i,s,a]=e.inputs[t],c=null;(s&zs.SignalBased)!==0&&(c=n[i][ge]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(n,r)),e.setInput!==null?e.setInput(n,c,r,t,i):Op(n,c,i,r)}finally{N(o)}}var dt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(dt||{}),yE;function bl(e,n){return yE(e,n)}function lr(e,n,t,r,o){if(r!=null){let i,s=!1;Ge(r)?i=r:at(r)&&(s=!0,r=r[ze]);let a=Pe(r);e===0&&t!==null?o==null?pg(n,t,a):Ss(n,t,a,o||null,!0):e===1&&t!==null?Ss(n,t,a,o||null,!0):e===2?dE(n,a,s):e===3&&n.destroyNode(a),i!=null&&TE(n,e,i,t,o)}}function DE(e,n){Dg(e,n),n[ze]=null,n[ve]=null}function CE(e,n,t,r,o,i){r[ze]=o,r[ve]=n,Gs(e,r,t,1,o,i)}function Dg(e,n){n[it].changeDetectionScheduler?.notify(9),Gs(e,n,n[Q],2,null,null)}function EE(e){let n=e[ir];if(!n)return Fu(e[b],e);for(;n;){let t=null;if(at(n))t=n[ir];else{let r=n[ye];r&&(t=r)}if(!t){for(;n&&!n[Fe]&&n!==e;)at(n)&&Fu(n[b],n),n=n[ne];n===null&&(n=e),at(n)&&Fu(n[b],n),t=n&&n[Fe]}n=t}}function Sl(e,n){let t=e[Cn],r=t.indexOf(n);t.splice(r,1)}function Cg(e,n){if(En(n))return;let t=n[Q];t.destroyNode&&Gs(e,n,t,3,null,null),EE(n)}function Fu(e,n){if(En(n))return;let t=N(null);try{n[S]&=-129,n[S]|=256,n[Ne]&&Vi(n[Ne]),wE(e,n),IE(e,n),n[b].type===1&&n[Q].destroy();let r=n[Ht];if(r!==null&&Ge(n[ne])){r!==n[ne]&&Sl(r,n);let o=n[st];o!==null&&o.detachView(e)}Wu(n)}finally{N(t)}}function IE(e,n){let t=e.cleanup,r=n[or];if(t!==null)for(let s=0;s<t.length-1;s+=2)if(typeof t[s]=="string"){let a=t[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[t[s+1]];t[s].call(a)}r!==null&&(n[or]=null);let o=n[Dt];if(o!==null){n[Dt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=n[co];if(i!==null){n[co]=null;for(let s of i)s.destroy()}}function wE(e,n){let t;if(e!=null&&(t=e.destroyHooks)!=null)for(let r=0;r<t.length;r+=2){let o=n[t[r]];if(!(o instanceof wn)){let i=t[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];H(4,a,c);try{c.call(a)}finally{H(5,a,c)}}else{H(4,o,i);try{i.call(o)}finally{H(5,o,i)}}}}}function Eg(e,n,t){return _E(e,n.parent,t)}function _E(e,n,t){let r=n;for(;r!==null&&r.type&168;)n=r,r=n.parent;if(r===null)return t[ze];if($t(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===wt.None||o===wt.Emulated)return null}return We(r,t)}function Ig(e,n,t){return SE(e,n,t)}function bE(e,n,t){return e.type&40?We(e,t):null}var SE=bE,fp;function Ml(e,n,t,r){let o=Eg(e,r,n),i=n[Q],s=r.parent||n[ve],a=Ig(s,r,n);if(o!=null)if(Array.isArray(t))for(let c=0;c<t.length;c++)dp(i,o,t[c],a,!1);else dp(i,o,t,a,!1);fp!==void 0&&fp(i,r,n,t,o)}function go(e,n){if(n!==null){let t=n.type;if(t&3)return We(n,e);if(t&4)return Qu(-1,e[n.index]);if(t&8){let r=n.child;if(r!==null)return go(e,r);{let o=e[n.index];return Ge(o)?Qu(-1,o):Pe(o)}}else{if(t&128)return go(e,n.next);if(t&32)return bl(n,e)()||Pe(e[n.index]);{let r=wg(e,n);if(r!==null){if(Array.isArray(r))return r[0];let o=Lt(e[_e]);return go(o,r)}else return go(e,n.next)}}}return null}function wg(e,n){if(n!==null){let r=e[_e][ve],o=n.projection;return r.projection[o]}return null}function Qu(e,n){let t=ye+e+1;if(t<n.length){let r=n[t],o=r[b].firstChild;if(o!==null)return go(r,o)}return n[Ut]}function Tl(e,n,t,r,o,i,s){for(;t!=null;){if(t.type===128){t=t.next;continue}let a=r[t.index],c=t.type;if(s&&n===0&&(a&&hr(Pe(a),r),t.flags|=2),!Hs(t))if(c&8)Tl(e,n,t.child,r,o,i,!1),lr(n,e,o,a,i);else if(c&32){let u=bl(t,r),l;for(;l=u();)lr(n,e,o,l,i);lr(n,e,o,a,i)}else c&16?_g(e,n,r,t,o,i):lr(n,e,o,a,i);t=s?t.projectionNext:t.next}}function Gs(e,n,t,r,o,i){Tl(t,r,e.firstChild,n,o,i,!1)}function ME(e,n,t){let r=n[Q],o=Eg(e,t,n),i=t.parent||n[ve],s=Ig(i,t,n);_g(r,0,n,t,o,s)}function _g(e,n,t,r,o,i){let s=t[_e],c=s[ve].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];lr(n,e,o,l,i)}else{let u=c,l=s[ne];Yp(r)&&(u.flags|=128),Tl(e,n,u,l,o,i,!0)}}function TE(e,n,t,r,o){let i=t[Ut],s=Pe(t);i!==s&&lr(n,e,r,i,o);for(let a=ye;a<t.length;a++){let c=t[a];Gs(c[b],c,e,n,r,i)}}function AE(e,n,t,r,o){if(n)o?e.addClass(t,r):e.removeClass(t,r);else{let i=r.indexOf("-")===-1?void 0:dt.DashCase;o==null?e.removeStyle(t,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=dt.Important),e.setStyle(t,r,o,i))}}function bg(e,n,t,r,o){let i=Gt(),s=r&2;try{Wt(-1),s&&n.length>oe&&yg(e,n,oe,!1),H(s?2:0,o,t),t(r,o)}finally{Wt(i),H(s?3:1,o,t)}}function Ws(e,n,t){LE(e,n,t),(t.flags&64)===64&&VE(e,n,t)}function Eo(e,n,t=We){let r=n.localNames;if(r!==null){let o=n.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?t(n,e):e[s];e[o++]=a}}}function NE(e,n,t,r){let i=r.get(ng,tg)||t===wt.ShadowDom,s=e.selectRootElement(n,i);return RE(s),s}function RE(e){xE(e)}var xE=()=>null;function OE(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function kE(e,n,t,r,o,i){let s=n[b];if(xl(e,s,n,t,r)){$t(e)&&PE(n,e.index);return}e.type&3&&(t=OE(t)),FE(e,n,t,r,o,i)}function FE(e,n,t,r,o,i){if(e.type&3){let s=We(e,n);r=i!=null?i(r,e.value||"",t):r,o.setProperty(s,t,r)}else e.type&12}function PE(e,n){let t=Le(n,e);t[S]&16||(t[S]|=64)}function LE(e,n,t){let r=t.directiveStart,o=t.directiveEnd;$t(t)&&mE(n,t,e.data[r+t.componentOffset]),e.firstCreatePass||ws(t,n);let i=t.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=vo(n,e,s,t);if(hr(c,n),i!==null&&UE(n,s-r,c,a,t,i),ct(a)){let u=Le(t.index,n);u[fe]=vo(n,e,s,t)}}}function VE(e,n,t){let r=t.directiveStart,o=t.directiveEnd,i=t.index,s=Wh();try{Wt(i);for(let a=r;a<o;a++){let c=e.data[a],u=n[a];ss(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&jE(c,u)}}finally{Wt(-1),ss(s)}}function jE(e,n){e.hostBindings!==null&&e.hostBindings(1,n)}function Al(e,n){let t=e.directiveRegistry,r=null;if(t)for(let o=0;o<t.length;o++){let i=t[o];fg(n,i.selectors,!1)&&(r??=[],ct(i)?r.unshift(i):r.push(i))}return r}function BE(e,n,t,r,o,i){let s=We(e,n);HE(n[Q],s,i,e.value,t,r,o)}function HE(e,n,t,r,o,i,s){if(i==null)e.removeAttribute(n,o,t);else{let a=s==null?gn(i):s(i,r||"",o);e.setAttribute(n,o,a,t)}}function UE(e,n,t,r,o,i){let s=i[n];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];Yu(r,t,c,u)}}function Nl(e,n,t,r,o){let i=oe+t,s=n[b],a=o(s,n,e,r,t);n[i]=a,ur(e,!0);let c=e.type===2;return c?(gg(n[Q],a,e),(kh()===0||sr(e))&&hr(a,n),Fh()):hr(a,n),ls()&&(!c||!Hs(e))&&Ml(s,n,a,e),e}function Rl(e){let n=e;return Du()?Cu():(n=n.parent,ur(n,!1)),n}function $E(e,n){let t=e[mn];if(!t)return;t.get(Re,null)?.(n)}function xl(e,n,t,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=n.data[u];Yu(d,t[u],l,o),a=!0}if(i)for(let c of i){let u=t[c],l=n.data[c];Yu(l,u,r,o),a=!0}return a}function zE(e,n){let t=Le(n,e),r=t[b];GE(r,t);let o=t[ze];o!==null&&t[rr]===null&&(t[rr]=rg(o,t[mn])),H(18),Ol(r,t,t[fe]),H(19,t[fe])}function GE(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}function Ol(e,n,t){cs(n);try{let r=e.viewQuery;r!==null&&Zu(1,r,t);let o=e.template;o!==null&&bg(e,n,o,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[st]?.finishViewCreation(e),e.staticContentQueries&&og(e,n),e.staticViewQueries&&Zu(2,e.viewQuery,t);let i=e.components;i!==null&&WE(n,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[S]&=-5,us()}}function WE(e,n){for(let t=0;t<n.length;t++)zE(e,n[t])}function Sg(e,n,t,r){let o=N(null);try{let i=n.tView,a=e[S]&4096?4096:16,c=wl(e,i,t,a,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[n.index];c[Ht]=u;let l=e[st];return l!==null&&(c[st]=l.createEmbeddedView(i)),Ol(i,c,t),c}finally{N(o)}}function Ku(e,n){return!n||n.firstChild===null||Yp(e)}var hp=!1,qE=new C("");function yo(e,n,t,r,o=!1){for(;t!==null;){if(t.type===128){t=o?t.projectionNext:t.next;continue}let i=n[t.index];i!==null&&r.push(Pe(i)),Ge(i)&&Mg(i,r);let s=t.type;if(s&8)yo(e,n,t.child,r);else if(s&32){let a=bl(t,n),c;for(;c=a();)r.push(c)}else if(s&16){let a=wg(n,t);if(Array.isArray(a))r.push(...a);else{let c=Lt(n[_e]);yo(c[b],c,a,r,!0)}}t=o?t.projectionNext:t.next}return r}function Mg(e,n){for(let t=ye;t<e.length;t++){let r=e[t],o=r[b].firstChild;o!==null&&yo(r[b],r,o,n)}e[Ut]!==e[ze]&&n.push(e[Ut])}function Tg(e){if(e[Dn]!==null){for(let n of e[Dn])n.impl.addSequence(n);e[Dn].length=0}}var Ag=[];function ZE(e){return e[Ne]??YE(e)}function YE(e){let n=Ag.pop()??Object.create(KE);return n.lView=e,n}function QE(e){e.lView[Ne]!==e&&(e.lView=null,Ag.push(e))}var KE=A(g({},an),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{cr(e.lView)},consumerOnSignalRead(){this.lView[Ne]=this}});function JE(e){let n=e[Ne]??Object.create(XE);return n.lView=e,n}var XE=A(g({},an),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let n=Lt(e.lView);for(;n&&!Ng(n[b]);)n=Lt(n);n&&hu(n)},consumerOnSignalRead(){this.lView[Ne]=this}});function Ng(e){return e.type!==2}function Rg(e){if(e[co]===null)return;let n=!0;for(;n;){let t=!1;for(let r of e[co])r.dirty&&(t=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));n=t&&!!(e[S]&8192)}}var eI=100;function kl(e,n=0){let r=e[it].rendererFactory,o=!1;o||r.begin?.();try{tI(e,n)}finally{o||r.end?.()}}function tI(e,n){let t=Iu();try{wu(!0),Ju(e,n);let r=0;for(;lo(e);){if(r===eI)throw new y(103,!1);r++,Ju(e,1)}}finally{wu(t)}}function xg(e,n){Eu(n?fo.Exhaustive:fo.OnlyDirtyViews);try{kl(e)}finally{Eu(fo.Off)}}function nI(e,n,t,r){if(En(n))return;let o=n[S],i=!1,s=!1;cs(n);let a=!0,c=null,u=null;i||(Ng(e)?(u=ZE(n),c=cn(u)):Pi()===null?(a=!1,u=JE(n),c=cn(u)):n[Ne]&&(Vi(n[Ne]),n[Ne]=null));try{fu(n),$h(e.bindingStartIndex),t!==null&&bg(e,n,t,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&ms(n,f,null)}else{let f=e.preOrderHooks;f!==null&&vs(n,f,0,null),Ou(n,0)}if(s||rI(n),Rg(n),Og(n,0),e.contentQueries!==null&&og(e,n),!i)if(l){let f=e.contentCheckHooks;f!==null&&ms(n,f)}else{let f=e.contentHooks;f!==null&&vs(n,f,1),Ou(n,1)}iI(e,n);let d=e.components;d!==null&&Fg(n,d,0);let h=e.viewQuery;if(h!==null&&Zu(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&ms(n,f)}else{let f=e.viewHooks;f!==null&&vs(n,f,2),Ou(n,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),n[es]){for(let f of n[es])f();n[es]=null}i||(Tg(n),n[S]&=-73)}catch(l){throw i||cr(n),l}finally{u!==null&&(Jn(u,c),a&&QE(u)),us()}}function Og(e,n){for(let t=Kp(e);t!==null;t=Jp(t))for(let r=ye;r<t.length;r++){let o=t[r];kg(o,n)}}function rI(e){for(let n=Kp(e);n!==null;n=Jp(n)){if(!(n[S]&2))continue;let t=n[Cn];for(let r=0;r<t.length;r++){let o=t[r];hu(o)}}}function oI(e,n,t){H(18);let r=Le(n,e);kg(r,t),H(19,r[fe])}function kg(e,n){ns(e)&&Ju(e,n)}function Ju(e,n){let r=e[b],o=e[S],i=e[Ne],s=!!(n===0&&o&16);if(s||=!!(o&64&&n===0),s||=!!(o&1024),s||=!!(i?.dirty&&Kr(i)),s||=!1,i&&(i.dirty=!1),e[S]&=-9217,s)nI(r,e,r.template,e[fe]);else if(o&8192){let a=N(null);try{Rg(e),Og(e,1);let c=r.components;c!==null&&Fg(e,c,1),Tg(e)}finally{N(a)}}}function Fg(e,n,t){for(let r=0;r<n.length;r++)oI(e,n[r],t)}function iI(e,n){let t=e.hostBindingOpCodes;if(t!==null)try{for(let r=0;r<t.length;r++){let o=t[r];if(o<0)Wt(~o);else{let i=o,s=t[++r],a=t[++r];Gh(s,i);let c=n[i];H(24,c),a(2,c),H(25,c)}}}finally{Wt(-1)}}function Fl(e,n){let t=Iu()?64:1088;for(e[it].changeDetectionScheduler?.notify(n);e;){e[S]|=t;let r=Lt(e);if(ar(e)&&!r)return e;e=r}return null}function Pg(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function Lg(e,n,t,r=!0){let o=n[b];if(sI(o,n,e,t),r){let s=Qu(t,e),a=n[Q],c=a.parentNode(e[Ut]);c!==null&&CE(o,e[ve],a,n,c,s)}let i=n[rr];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Xu(e,n){if(e.length<=ye)return;let t=ye+n,r=e[t];if(r){let o=r[Ht];o!==null&&o!==e&&Sl(o,r),n>0&&(e[t-1][Fe]=r[Fe]);let i=io(e,ye+n);DE(r[b],r);let s=i[st];s!==null&&s.detachView(i[b]),r[ne]=null,r[Fe]=null,r[S]&=-129}return r}function sI(e,n,t,r){let o=ye+r,i=t.length;r>0&&(t[o-1][Fe]=n),r<i-ye?(n[Fe]=t[o],eu(t,ye+r,n)):(t.push(n),n[Fe]=null),n[ne]=t;let s=n[Ht];s!==null&&t!==s&&Vg(s,n);let a=n[st];a!==null&&a.insertView(e),rs(n),n[S]|=128}function Vg(e,n){let t=e[Cn],r=n[ne];if(at(r))e[S]|=2;else{let o=r[ne][_e];n[_e]!==o&&(e[S]|=2)}t===null?e[Cn]=[n]:t.push(n)}var qt=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let n=this._lView,t=n[b];return yo(t,n,t.firstChild,[])}constructor(n,t){this._lView=n,this._cdRefInjectingView=t}get context(){return this._lView[fe]}set context(n){this._lView[fe]=n}get destroyed(){return En(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let n=this._lView[ne];if(Ge(n)){let t=n[uo],r=t?t.indexOf(this):-1;r>-1&&(Xu(n,r),io(t,r))}this._attachedToViewContainer=!1}Cg(this._lView[b],this._lView)}onDestroy(n){pu(this._lView,n)}markForCheck(){Fl(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[S]&=-129}reattach(){rs(this._lView),this._lView[S]|=128}detectChanges(){this._lView[S]|=1024,kl(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[mn].get(qE,hp)}catch{this.exhaustive=hp}}attachToViewContainerRef(){if(this._appRef)throw new y(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let n=ar(this._lView),t=this._lView[Ht];t!==null&&!n&&Sl(t,this._lView),Dg(this._lView[b],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new y(902,!1);this._appRef=n;let t=ar(this._lView),r=this._lView[Ht];r!==null&&!t&&Vg(r,this._lView),rs(this._lView)}};var Ze=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=aI;constructor(t,r,o){this._declarationLView=t,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,r){return this.createEmbeddedViewImpl(t,r)}createEmbeddedViewImpl(t,r,o){let i=Sg(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:r,dehydratedView:o});return new qt(i)}}return e})();function aI(){return Pl(ie(),V())}function Pl(e,n){return e.type&4?new Ze(n,e,vr(e,n)):null}function Dr(e,n,t,r,o){let i=e.data[n];if(i===null)i=cI(e,n,t,r,o),zh()&&(i.flags|=32);else if(i.type&64){i.type=t,i.value=r,i.attrs=o;let s=Hh();i.injectorIndex=s===null?-1:s.injectorIndex}return ur(i,!0),i}function cI(e,n,t,r,o){let i=yu(),s=Du(),a=s?i:i&&i.parent,c=e.data[n]=lI(e,a,t,n,r,o);return uI(e,c,i,s),c}function uI(e,n,t,r){e.firstChild===null&&(e.firstChild=n),t!==null&&(r?t.child==null&&n.parent!==null&&(t.child=n):t.next===null&&(t.next=n,n.prev=t))}function lI(e,n,t,r,o,i){let s=n?n.injectorIndex:-1,a=0;return vu()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var hF=new RegExp(`^(\\d+)*(${jC}|${VC})*(.*)`);var dI=()=>null;function el(e,n){return dI(e,n)}var jg=class{},qs=class{},tl=class{resolveComponentFactory(n){throw new y(917,!1)}},Io=class{static NULL=new tl},_n=class{},ht=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>fI()}return e})();function fI(){let e=V(),n=ie(),t=Le(n.index,e);return(at(t)?t:e)[Q]}var Bg=(()=>{class e{static \u0275prov=D({token:e,providedIn:"root",factory:()=>null})}return e})();var Ds={},nl=class{injector;parentInjector;constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){let o=this.injector.get(n,Ds,r);return o!==Ds||t===Ds?o:this.parentInjector.get(n,t,r)}};function Ms(e,n,t){let r=t?e.styles:null,o=t?e.classes:null,i=0;if(n!==null)for(let s=0;s<n.length;s++){let a=n[s];if(typeof a=="number")i=a;else if(i==1)o=Hc(o,a);else if(i==2){let c=a,u=n[++s];r=Hc(r,c+": "+u+";")}}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=o:e.classesWithoutHost=o}function m(e,n=0){let t=V();if(t===null)return I(e,n);let r=ie();return Wp(r,t,ue(e),n)}function Hg(e,n,t,r,o){let i=r===null?null:{"":-1},s=o(e,t);if(s!==null){let a=s,c=null,u=null;for(let l of s)if(l.resolveHostDirectives!==null){[a,c,u]=l.resolveHostDirectives(s);break}gI(e,n,t,a,i,c,u)}i!==null&&r!==null&&hI(t,r,i)}function hI(e,n,t){let r=e.localNames=[];for(let o=0;o<n.length;o+=2){let i=t[n[o+1]];if(i==null)throw new y(-301,!1);r.push(n[o],i)}}function pI(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function gI(e,n,t,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&ct(f)&&(c=!0,pI(e,t,h)),zu(ws(t,n),e,f.type)}EI(t,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=vg(e,n,a,null);a>0&&(t.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(t.mergedAttrs=fr(t.mergedAttrs,f.hostAttrs),vI(e,t,n,d,f),CI(d,f,o),s!==null&&s.has(f)){let[E,T]=s.get(f);t.directiveToIndex.set(f.type,[d,E+t.directiveStart,T+t.directiveStart])}else(i===null||!i.has(f))&&t.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(t.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(t.flags|=64);let v=f.type.prototype;!u&&(v.ngOnChanges||v.ngOnInit||v.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),u=!0),!l&&(v.ngOnChanges||v.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),l=!0),d++}mI(e,t,i)}function mI(e,n,t){for(let r=n.directiveStart;r<n.directiveEnd;r++){let o=e.data[r];if(t===null||!t.has(o))pp(0,n,o,r),pp(1,n,o,r),mp(n,r,!1);else{let i=t.get(o);gp(0,n,i,r),gp(1,n,i,r),mp(n,r,!0)}}}function pp(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=n.inputs??={}:s=n.outputs??={},s[i]??=[],s[i].push(r),Ug(n,i)}}function gp(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=n.hostDirectiveInputs??={}:a=n.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Ug(n,s)}}function Ug(e,n){n==="class"?e.flags|=8:n==="style"&&(e.flags|=16)}function mp(e,n,t){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!t&&o===null||t&&i===null||El(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!t&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===n){s??=[],s.push(c,r[a+1]);break}}else if(t&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===n){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function vI(e,n,t,r,o){e.data[r]=o;let i=o.factory||(o.factory=dn(o.type,!0)),s=new wn(i,ct(o),m,null);e.blueprint[r]=s,t[r]=s,yI(e,n,r,vg(e,t,o.hostVars,ft),o)}function yI(e,n,t,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~n.index;DI(s)!=a&&s.push(a),s.push(t,r,i)}}function DI(e){let n=e.length;for(;n>0;){let t=e[--n];if(typeof t=="number"&&t<0)return t}return 0}function CI(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;ct(n)&&(t[""]=e)}}function EI(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}function Ll(e,n,t,r,o,i,s,a){let c=n[b],u=c.consts,l=zt(u,s),d=Dr(c,e,t,r,l);return i&&Hg(c,n,d,zt(u,a),o),d.mergedAttrs=fr(d.mergedAttrs,d.attrs),d.attrs!==null&&Ms(d,d.attrs,!1),d.mergedAttrs!==null&&Ms(d,d.mergedAttrs,!0),c.queries!==null&&c.queries.elementStart(c,d),d}function Vl(e,n){Lp(e,n),lu(n)&&e.queries.elementEnd(n)}function II(e,n,t,r,o,i){let s=n.consts,a=zt(s,o),c=Dr(n,e,t,r,a);if(c.mergedAttrs=fr(c.mergedAttrs,c.attrs),i!=null){let u=zt(s,i);c.localNames=[];for(let l=0;l<u.length;l+=2)c.localNames.push(u[l],-1)}return c.attrs!==null&&Ms(c,c.attrs,!1),c.mergedAttrs!==null&&Ms(c,c.mergedAttrs,!0),n.queries!==null&&n.queries.elementStart(n,c),c}function jl(e){return zg(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function $g(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{let t=e[Symbol.iterator](),r;for(;!(r=t.next()).done;)n(r.value)}}function zg(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function pr(e,n,t){if(t===ft)return!1;let r=e[n];return Object.is(r,t)?!1:(e[n]=t,!0)}function wI(e,n,t,r){let o=pr(e,n,t);return pr(e,n+1,r)||o}function Pu(e,n,t){return function r(o){let i=$t(e)?Le(e.index,n):n;Fl(i,5);let s=n[fe],a=vp(n,s,t,o),c=r.__ngNextListenerFn__;for(;c;)a=vp(n,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function vp(e,n,t,r){let o=N(null);try{return H(6,n,t),t(r)!==!1}catch(i){return $E(e,i),!1}finally{H(7,n,t),N(o)}}function _I(e,n,t,r,o,i,s,a){let c=sr(e),u=!1,l=null;if(!r&&c&&(l=bI(n,t,i,e.index)),l!==null){let d=l.__ngLastListenerFn__||l;d.__ngNextListenerFn__=s,l.__ngLastListenerFn__=s,u=!0}else{let d=We(e,t),h=r?r(d):d;HC(t,h,i,a);let f=o.listen(h,i,a),v=r?E=>r(Pe(E[e.index])):e.index;Gg(v,n,t,i,a,f,!1)}return u}function bI(e,n,t,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===t&&o[i+1]===r){let a=n[or],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Gg(e,n,t,r,o,i,s){let a=n.firstCreatePass?mu(n):null,c=gu(t),u=c.length;c.push(o,i),a&&a.push(r,e,u,(u+1)*(s?-1:1))}function yp(e,n,t,r,o,i){let s=n[t],a=n[b],u=a.data[t].outputs[r],d=s[u].subscribe(i);Gg(e.index,a,n,o,i,d,!0)}var rl=Symbol("BINDING");var Ts=class extends Io{ngModule;constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){let t=ot(n);return new Zt(t,this.ngModule)}};function SI(e){return Object.keys(e).map(n=>{let[t,r,o]=e[n],i={propName:t,templateName:n,isSignal:(r&zs.SignalBased)!==0};return o&&(i.transform=o),i})}function MI(e){return Object.keys(e).map(n=>({propName:e[n],templateName:n}))}function TI(e,n,t){let r=n instanceof Z?n:n?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new nl(t,r):t}function AI(e){let n=e.get(_n,null);if(n===null)throw new y(407,!1);let t=e.get(Bg,null),r=e.get(Vt,null);return{rendererFactory:n,sanitizer:t,changeDetectionScheduler:r,ngReflect:!1}}function NI(e,n){let t=(e.selectors[0][0]||"div").toLowerCase();return hg(n,t,t==="svg"?Mh:t==="math"?Th:null)}var Zt=class extends qs{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=SI(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=MI(this.componentDef.outputs),this.cachedOutputs}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=sE(n.selectors),this.ngContentSelectors=n.ngContentSelectors??[],this.isBoundToModule=!!t}create(n,t,r,o,i,s){H(22);let a=N(null);try{let c=this.componentDef,u=RI(r,c,s,i),l=TI(c,o||this.ngModule,n),d=AI(l),h=d.rendererFactory.createRenderer(null,c),f=r?NE(h,r,c.encapsulation,l):NI(c,h),v=s?.some(Dp)||i?.some(R=>typeof R!="function"&&R.bindings.some(Dp)),E=wl(null,u,null,512|mg(c),null,null,d,h,l,null,rg(f,l,!0));E[oe]=f,cs(E);let T=null;try{let R=Ll(oe,E,2,"#host",()=>u.directiveRegistry,!0,0);f&&(gg(h,f,R),hr(f,E)),Ws(u,E,R),yl(u,R,E),Vl(u,R),t!==void 0&&OI(R,this.ngContentSelectors,t),T=Le(R.index,E),E[fe]=T[fe],Ol(u,E,null)}catch(R){throw T!==null&&Wu(T),Wu(E),R}finally{H(23),us()}return new As(this.componentType,E,!!v)}finally{N(a)}}};function RI(e,n,t,r){let o=e?["ng-version","20.1.3"]:aE(n.selectors[0]),i=null,s=null,a=0;if(t)for(let l of t)a+=l[rl].requiredVars,l.create&&(l.targetIdx=0,(i??=[]).push(l)),l.update&&(l.targetIdx=0,(s??=[]).push(l));if(r)for(let l=0;l<r.length;l++){let d=r[l];if(typeof d!="function")for(let h of d.bindings){a+=h[rl].requiredVars;let f=l+1;h.create&&(h.targetIdx=f,(i??=[]).push(h)),h.update&&(h.targetIdx=f,(s??=[]).push(h))}}let c=[n];if(r)for(let l of r){let d=typeof l=="function"?l:l.type,h=ou(d);c.push(h)}return Il(0,null,xI(i,s),1,a,c,null,null,null,[o],null)}function xI(e,n){return!e&&!n?null:t=>{if(t&1&&e)for(let r of e)r.create();if(t&2&&n)for(let r of n)r.update()}}function Dp(e){let n=e[rl].kind;return n==="input"||n==="twoWay"}var As=class extends jg{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(n,t,r){super(),this._rootLView=t,this._hasInputBindings=r,this._tNode=ts(t[b],oe),this.location=vr(this._tNode,t),this.instance=Le(this._tNode.index,t)[fe],this.hostView=this.changeDetectorRef=new qt(t,void 0),this.componentType=n}setInput(n,t){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;let o=this._rootLView,i=xl(r,o[b],o,n,t);this.previousInputValues.set(n,t);let s=Le(r.index,o);Fl(s,1)}get injector(){return new In(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}};function OI(e,n,t){let r=e.projection=[];for(let o=0;o<n.length;o++){let i=t[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Ve=(()=>{class e{static __NG_ELEMENT_ID__=kI}return e})();function kI(){let e=ie();return qg(e,V())}var FI=Ve,Wg=class extends FI{_lContainer;_hostTNode;_hostLView;constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return vr(this._hostTNode,this._hostLView)}get injector(){return new In(this._hostTNode,this._hostLView)}get parentInjector(){let n=gl(this._hostTNode,this._hostLView);if(Bp(n)){let t=Is(n,this._hostLView),r=Es(n),o=t[b].data[r+8];return new In(o,t)}else return new In(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){let t=Cp(this._lContainer);return t!==null&&t[n]||null}get length(){return this._lContainer.length-ye}createEmbeddedView(n,t,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=el(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},i,s);return this.insertImpl(a,o,Ku(this._hostTNode,s)),a}createComponent(n,t,r,o,i,s,a){let c=n&&!fC(n),u;if(c)u=t;else{let T=t||{};u=T.index,r=T.injector,o=T.projectableNodes,i=T.environmentInjector||T.ngModuleRef,s=T.directives,a=T.bindings}let l=c?n:new Zt(ot(n)),d=r||this.parentInjector;if(!i&&l.ngModule==null){let R=(c?d:this.parentInjector).get(Z,null);R&&(i=R)}let h=ot(l.componentType??{}),f=el(this._lContainer,h?.id??null),v=f?.firstChild??null,E=l.create(d,o,v,i,s,a);return this.insertImpl(E.hostView,u,Ku(this._hostTNode,f)),E}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){let o=n._lView;if(Nh(o)){let a=this.indexOf(n);if(a!==-1)this.detach(a);else{let c=o[ne],u=new Wg(c,c[ve],c[ne]);u.detach(u.indexOf(n))}}let i=this._adjustIndex(t),s=this._lContainer;return Lg(s,o,i,r),n.attachToViewContainerRef(),eu(Lu(s),i,n),n}move(n,t){return this.insert(n,t)}indexOf(n){let t=Cp(this._lContainer);return t!==null?t.indexOf(n):-1}remove(n){let t=this._adjustIndex(n,-1),r=Xu(this._lContainer,t);r&&(io(Lu(this._lContainer),t),Cg(r[b],r))}detach(n){let t=this._adjustIndex(n,-1),r=Xu(this._lContainer,t);return r&&io(Lu(this._lContainer),t)!=null?new qt(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function Cp(e){return e[uo]}function Lu(e){return e[uo]||(e[uo]=[])}function qg(e,n){let t,r=n[e.index];return Ge(r)?t=r:(t=Pg(r,n,null,e),n[e.index]=t,_l(n,t)),LI(t,n,e,r),new Wg(t,e,n)}function PI(e,n){let t=e[Q],r=t.createComment(""),o=We(n,e),i=t.parentNode(o);return Ss(t,i,r,t.nextSibling(o),!1),r}var LI=BI,VI=()=>!1;function jI(e,n,t){return VI(e,n,t)}function BI(e,n,t,r){if(e[Ut])return;let o;t.type&8?o=Pe(r):o=PI(n,t),e[Ut]=o}var ol=class e{queryList;matches=null;constructor(n){this.queryList=n}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},il=class e{queries;constructor(n=[]){this.queries=n}createEmbeddedView(n){let t=n.queries;if(t!==null){let r=n.contentQueries!==null?n.contentQueries[0]:t.length,o=[];for(let i=0;i<r;i++){let s=t.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)Bl(n,t).matches!==null&&this.queries[t].setDirty()}},Ns=class{flags;read;predicate;constructor(n,t,r=null){this.flags=t,this.read=r,typeof n=="string"?this.predicate=ZI(n):this.predicate=n}},sl=class e{queries;constructor(n=[]){this.queries=n}elementStart(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let r=0;r<this.length;r++){let o=t!==null?t.length:0,i=this.getByIndex(r).embeddedTView(n,o);i&&(i.indexInDeclarationView=r,t!==null?t.push(i):t=[i])}return t!==null?new e(t):null}template(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}},al=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(n,t=-1){this.metadata=n,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new e(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let t=this._declarationNodeIndex,r=n.parent;for(;r!==null&&r.type&8&&r.index!==t;)r=r.parent;return t===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(n,t){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(n,t,HI(t,i)),this.matchTNodeWithReadOption(n,t,ys(t,n,i,!1,!1))}else r===Ze?t.type&4&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,ys(t,n,r,!1,!1))}matchTNodeWithReadOption(n,t,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===G||o===Ve||o===Ze&&t.type&4)this.addMatch(t.index,-2);else{let i=ys(t,n,o,!1,!1);i!==null&&this.addMatch(t.index,i)}else this.addMatch(t.index,r)}}addMatch(n,t){this.matches===null?this.matches=[n,t]:this.matches.push(n,t)}};function HI(e,n){let t=e.localNames;if(t!==null){for(let r=0;r<t.length;r+=2)if(t[r]===n)return t[r+1]}return null}function UI(e,n){return e.type&11?vr(e,n):e.type&4?Pl(e,n):null}function $I(e,n,t,r){return t===-1?UI(n,e):t===-2?zI(e,n,r):vo(e,e[b],t,n)}function zI(e,n,t){if(t===G)return vr(n,e);if(t===Ze)return Pl(n,e);if(t===Ve)return qg(n,e)}function Zg(e,n,t,r){let o=n[st].queries[r];if(o.matches===null){let i=e.data,s=t.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push($I(n,l,s[c+1],t.metadata.read))}}o.matches=a}return o.matches}function cl(e,n,t,r){let o=e.queries.getByIndex(t),i=o.matches;if(i!==null){let s=Zg(e,n,o,t);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=n[-c];for(let d=ye;d<l.length;d++){let h=l[d];h[Ht]===h[ne]&&cl(h[b],h,u,r)}if(l[Cn]!==null){let d=l[Cn];for(let h=0;h<d.length;h++){let f=d[h];cl(f[b],f,u,r)}}}}}return r}function GI(e,n){return e[st].queries[n].queryList}function Yg(e,n,t){let r=new _s((t&4)===4);return Oh(e,n,r,r.destroy),(n[st]??=new il).queries.push(new ol(r))-1}function WI(e,n,t){let r=he();return r.firstCreatePass&&(Qg(r,new Ns(e,n,t),-1),(n&2)===2&&(r.staticViewQueries=!0)),Yg(r,V(),n)}function qI(e,n,t,r){let o=he();if(o.firstCreatePass){let i=ie();Qg(o,new Ns(n,t,r),i.index),YI(o,e),(t&2)===2&&(o.staticContentQueries=!0)}return Yg(o,V(),t)}function ZI(e){return e.split(",").map(n=>n.trim())}function Qg(e,n,t){e.queries===null&&(e.queries=new sl),e.queries.track(new al(n,t))}function YI(e,n){let t=e.contentQueries||(e.contentQueries=[]),r=t.length?t[t.length-1]:-1;n!==r&&t.push(e.queries.length-1,n)}function Bl(e,n){return e.queries.getByIndex(n)}function QI(e,n){let t=e[b],r=Bl(t,n);return r.crossesNgTemplate?cl(t,e,n,[]):Zg(t,e,r,n)}var Ep=new Set;function Zs(e){Ep.has(e)||(Ep.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var bn=class{},Ys=class{};var Rs=class extends bn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Ts(this);constructor(n,t,r,o=!0){super(),this.ngModuleType=n,this._parent=t;let i=ru(n);this._bootstrapComponents=lg(i.bootstrap),this._r3Injector=Au(n,t,[{provide:bn,useValue:this},{provide:Io,useValue:this.componentFactoryResolver},...r],Ct(n),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}},xs=class extends Ys{moduleType;constructor(n){super(),this.moduleType=n}create(n){return new Rs(this.moduleType,n,[])}};var Do=class extends bn{injector;componentFactoryResolver=new Ts(this);instance=null;constructor(n){super();let t=new hn([...n.providers,{provide:bn,useValue:this},{provide:Io,useValue:this.componentFactoryResolver}],n.parent||nr(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}};function Cr(e,n,t=null){return new Do({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}var KI=(()=>{class e{_injector;cachedInjectors=new Map;constructor(t){this._injector=t}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){let r=iu(!1,t.type),o=r.length>0?Cr([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,o)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(let t of this.cachedInjectors.values())t!==null&&t.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=D({token:e,providedIn:"environment",factory:()=>new e(I(Z))})}return e})();function Hl(e){return mr(()=>{let n=Kg(e),t=A(g({},n),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===ml.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:n.standalone?o=>o.get(KI).getOrCreateStandaloneInjector(t):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||wt.Emulated,styles:e.styles||Ae,_:null,schemas:e.schemas||null,tView:null,id:""});n.standalone&&Zs("NgStandalone"),Jg(t);let r=e.dependencies;return t.directiveDefs=Ip(r,JI),t.pipeDefs=Ip(r,Ch),t.id=tw(t),t})}function JI(e){return ot(e)||ou(e)}function _t(e){return mr(()=>({type:e.type,bootstrap:e.bootstrap||Ae,declarations:e.declarations||Ae,imports:e.imports||Ae,exports:e.exports||Ae,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function XI(e,n){if(e==null)return jt;let t={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=zs.None,c=null),t[i]=[r,a,c],n[i]=s}return t}function ew(e){if(e==null)return jt;let n={};for(let t in e)e.hasOwnProperty(t)&&(n[e[t]]=t);return n}function F(e){return mr(()=>{let n=Kg(e);return Jg(n),n})}function Kg(e){let n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputConfig:e.inputs||jt,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Ae,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:XI(e.inputs,n),outputs:ew(e.outputs),debugInfo:null}}function Jg(e){e.features?.forEach(n=>n(e))}function Ip(e,n){return e?()=>{let t=typeof e=="function"?e():e,r=[];for(let o of t){let i=n(o);i!==null&&r.push(i)}return r}:null}function tw(e){let n=0,t=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,t,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=2147483648,"c"+n}function nw(e){return Object.getPrototypeOf(e.prototype).constructor}function De(e){let n=nw(e.type),t=!0,r=[e];for(;n;){let o;if(ct(e))o=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new y(903,!1);o=n.\u0275dir}if(o){if(t){r.push(o);let s=e;s.inputs=Vu(e.inputs),s.declaredInputs=Vu(e.declaredInputs),s.outputs=Vu(e.outputs);let a=o.hostBindings;a&&aw(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&iw(e,c),u&&sw(e,u),rw(e,o),lh(e.outputs,o.outputs),ct(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===De&&(t=!1)}}n=Object.getPrototypeOf(n)}ow(r)}function rw(e,n){for(let t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;let r=n.inputs[t];r!==void 0&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t])}}function ow(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=n+=o.hostVars,o.hostAttrs=fr(o.hostAttrs,t=fr(t,o.hostAttrs))}}function Vu(e){return e===jt?{}:e===Ae?[]:e}function iw(e,n){let t=e.viewQuery;t?e.viewQuery=(r,o)=>{n(r,o),t(r,o)}:e.viewQuery=n}function sw(e,n){let t=e.contentQueries;t?e.contentQueries=(r,o,i)=>{n(r,o,i),t(r,o,i)}:e.contentQueries=n}function aw(e,n){let t=e.hostBindings;t?e.hostBindings=(r,o)=>{n(r,o),t(r,o)}:e.hostBindings=n}function Xg(e,n,t,r,o,i,s,a){if(t.firstCreatePass){e.mergedAttrs=fr(e.mergedAttrs,e.attrs);let l=e.tView=Il(2,e,o,i,s,t.directiveRegistry,t.pipeRegistry,null,t.schemas,t.consts,null);t.queries!==null&&(t.queries.template(t,e),l.queries=t.queries.embeddedTView(e))}a&&(e.flags|=a),ur(e,!1);let c=lw(t,n,e,r);ls()&&Ml(t,n,c,e),hr(c,n);let u=Pg(c,n,c,e);n[r+oe]=u,_l(n,u),jI(u,e,n)}function cw(e,n,t,r,o,i,s,a,c,u,l){let d=t+oe,h;return n.firstCreatePass?(h=Dr(n,d,4,s||null,a||null),os()&&Hg(n,e,h,zt(n.consts,u),Al),Lp(n,h)):h=n.data[d],Xg(h,e,n,t,r,o,i,c),sr(h)&&Ws(n,e,h),u!=null&&Eo(e,h,l),h}function uw(e,n,t,r,o,i,s,a,c,u,l){let d=t+oe,h;if(n.firstCreatePass){if(h=Dr(n,d,4,s||null,a||null),u!=null){let f=zt(n.consts,u);h.localNames=[];for(let v=0;v<f.length;v+=2)h.localNames.push(f[v],-1)}}else h=n.data[d];return Xg(h,e,n,t,r,o,i,c),u!=null&&Eo(e,h,l),h}function em(e,n,t,r,o,i,s,a){let c=V(),u=he(),l=zt(u.consts,i);return cw(c,u,e,n,t,r,o,l,void 0,s,a),em}var lw=dw;function dw(e,n,t,r){return ho(!0),n[Q].createComment("")}var Qs=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Qs||{}),Mn=new C(""),tm=!1,ul=class extends U{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(n=!1){super(),this.__isAsync=n,bh()&&(this.destroyRef=p(ut,{optional:!0})??void 0,this.pendingTasks=p(It,{optional:!0})??void 0)}emit(n){let t=N(null);try{super.next(n)}finally{N(t)}}subscribe(n,t,r){let o=n,i=t||(()=>null),s=r;if(n&&typeof n=="object"){let c=n;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return n instanceof K&&n.add(a),a}wrapInTimeout(n){return t=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{n(t)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},z=ul;function nm(e){let n,t;function r(){e=po;try{t!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(t),n!==void 0&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function wp(e){return queueMicrotask(()=>e()),()=>{e=po}}var Ul="isAngularZone",Os=Ul+"_ID",fw=0,$=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new z(!1);onMicrotaskEmpty=new z(!1);onStable=new z(!1);onError=new z(!1);constructor(n){let{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=tm}=n;if(typeof Zone>"u")throw new y(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,gw(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Ul)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new y(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new y(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,n,hw,po,po);try{return i.runTask(s,t,r)}finally{i.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}},hw={};function $l(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function pw(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function n(){nm(()=>{e.callbackScheduled=!1,ll(e),e.isCheckStableRunning=!0,$l(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),ll(e)}function gw(e){let n=()=>{pw(e)},t=fw++;e._inner=e._inner.fork({name:"angular",properties:{[Ul]:!0,[Os]:t,[Os+t]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(mw(c))return r.invokeTask(i,s,a,c);try{return _p(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&n(),bp(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return _p(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!vw(c)&&n(),bp(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,ll(e),$l(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function ll(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function _p(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function bp(e){e._nesting--,$l(e)}var ks=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new z;onMicrotaskEmpty=new z;onStable=new z;onError=new z;run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,o){return n.apply(t,r)}};function mw(e){return rm(e,"__ignore_ng_zone__")}function vw(e){return rm(e,"__scheduler_tick__")}function rm(e,n){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[n]===!0}var zl=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})(),om=[0,1,2,3],im=(()=>{class e{ngZone=p($);scheduler=p(Vt);errorHandler=p($e,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){p(Mn,{optional:!0})}execute(){let t=this.sequences.size>0;t&&H(16),this.executing=!0;for(let r of om)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),t&&H(17)}register(t){let{view:r}=t;r!==void 0?((r[Dn]??=[]).push(t),cr(r),r[S]|=8192):this.executing?this.deferredRegistrations.add(t):this.addSequence(t)}addSequence(t){this.sequences.add(t),this.scheduler.notify(7)}unregister(t){this.executing&&this.sequences.has(t)?(t.erroredOrDestroyed=!0,t.pipelinedValue=void 0,t.once=!0):(this.sequences.delete(t),this.deferredRegistrations.delete(t))}maybeTrace(t,r){return r?r.run(Qs.AFTER_NEXT_RENDER,t):t()}static \u0275prov=D({token:e,providedIn:"root",factory:()=>new e})}return e})(),Fs=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(n,t,r,o,i,s=null){this.impl=n,this.hooks=t,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let n=this.view?.[Dn];n&&(this.view[Dn]=n.filter(t=>t!==this))}};function Ks(e,n){let t=n?.injector??p(re);return Zs("NgAfterNextRender"),Dw(e,t,n,!0)}function yw(e){return e instanceof Function?[void 0,void 0,e,void 0]:[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Dw(e,n,t,r){let o=n.get(zl);o.impl??=n.get(im);let i=n.get(Mn,null,{optional:!0}),s=t?.manualCleanup!==!0?n.get(ut):null,a=n.get(fs,null,{optional:!0}),c=new Fs(o.impl,yw(e),a?.view,r,s,i?.snapshot(null));return o.impl.register(c),c}var Gl=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Wl=new C("");function Qt(e){return!!e&&typeof e.then=="function"}function ql(e){return!!e&&typeof e.subscribe=="function"}var sm=new C("");var Zl=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r});appInits=p(sm,{optional:!0})??[];injector=p(re);constructor(){}runInitializers(){if(this.initialized)return;let t=[];for(let o of this.appInits){let i=me(this.injector,o);if(Qt(i))t.push(i);else if(ql(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});t.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(o=>{this.reject(o)}),t.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Js=new C("");function am(){Sc(()=>{let e="";throw new y(600,e)})}function cm(e){return e.isBoundToModule}var Cw=10;var Qe=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(Re);afterRenderManager=p(zl);zonelessEnabled=p(ds);rootEffectScheduler=p(xu);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new U;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=p(It);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(L(t=>!t))}constructor(){p(Mn,{optional:!0})}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{t.unsubscribe()})}_injector=p(Z);_rendererFactory=null;get injector(){return this._injector}bootstrap(t,r){return this.bootstrapImpl(t,r)}bootstrapImpl(t,r,o=re.NULL){return this._injector.get($).run(()=>{H(10);let s=t instanceof qs;if(!this._injector.get(Zl).done){let v="";throw new y(405,v)}let c;s?c=t:c=this._injector.get(Io).resolveComponentFactory(t),this.componentTypes.push(c.componentType);let u=cm(c)?void 0:this._injector.get(bn),l=r||c.selector,d=c.create(o,[],l,u),h=d.location.nativeElement,f=d.injector.get(Wl,null);return f?.registerApplication(h),d.onDestroy(()=>{this.detachView(d.hostView),mo(this.components,d),f?.unregisterApplication(h)}),this._loadComponent(d),H(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){H(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Qs.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new y(101,!1);let t=N(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,N(t),this.afterTick.next(),H(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(_n,null,{optional:!0}));let t=0;for(;this.dirtyFlags!==0&&t++<Cw;)H(14),this.synchronizeOnce(),H(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let t=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!lo(o))continue;let i=r&&!this.zonelessEnabled?0:1;kl(o,i),t=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}t||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:t})=>lo(t))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(t){let r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){let r=t;mo(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(t),this._injector.get(Js,[]).forEach(o=>o(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>mo(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new y(406,!1);let t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function mo(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function Tn(e,n,t,r){let o=V(),i=is();if(pr(o,i,n)){let s=he(),a=Tu();BE(a,o,e,n,t,r)}return Tn}function um(e,n,t){let r=V(),o=is();if(pr(r,o,n)){let i=he(),s=Tu();kE(s,r,e,n,r[Q],t)}return um}function Sp(e,n,t,r,o){xl(n,e,t,o?"class":"style",r)}function Yl(e,n,t,r){let o=V(),i=o[b],s=e+oe,a=i.firstCreatePass?Ll(s,o,2,n,Al,os(),t,r):i.data[s];if(Nl(a,o,e,n,Ew),sr(a)){let c=o[b];Ws(c,o,a),yl(c,a,o)}return r!=null&&Eo(o,a),Yl}function Ql(){let e=he(),n=ie(),t=Rl(n);return e.firstCreatePass&&Vl(e,t),Lh(t)&&Vh(),Ph(),t.classesWithoutHost!=null&&yC(t)&&Sp(e,t,V(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&DC(t)&&Sp(e,t,V(),t.stylesWithoutHost,!1),Ql}function Xs(e,n,t,r){return Yl(e,n,t,r),Ql(),Xs}var Ew=(e,n,t,r,o)=>(ho(!0),hg(n[Q],r,Jh()));function Kl(e,n,t){let r=V(),o=r[b],i=e+oe,s=o.firstCreatePass?Ll(i,r,8,"ng-container",Al,os(),n,t):o.data[i];if(Nl(s,r,e,"ng-container",fm),sr(s)){let a=r[b];Ws(a,r,s),yl(a,s,r)}return t!=null&&Eo(r,s),Kl}function ea(){let e=he(),n=ie(),t=Rl(n);return e.firstCreatePass&&Vl(e,t),ea}function lm(e,n,t){return Kl(e,n,t),ea(),lm}function dm(e,n,t){let r=V(),o=r[b],i=e+oe,s=o.firstCreatePass?II(i,o,8,"ng-container",n,t):o.data[i];return Nl(s,r,e,"ng-container",fm),t!=null&&Eo(r,s),dm}function Iw(){let e=ie(),n=Rl(e);return ea}var fm=(e,n,t,r,o)=>(ho(!0),lE(n[Q],""));function ww(){return V()}var wo="en-US";var _w=wo;function hm(e){typeof e=="string"&&(_w=e.toLowerCase().replace(/_/g,"-"))}function le(e,n,t){let r=V(),o=he(),i=ie();return bw(o,r,r[Q],i,e,n,t),le}function bw(e,n,t,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=Pu(r,n,i),_I(r,e,n,s,t,o,i,c)&&(a=!1)),a){let u=r.outputs?.[o],l=r.hostDirectiveOutputs?.[o];if(l&&l.length)for(let d=0;d<l.length;d+=2){let h=l[d],f=l[d+1];c??=Pu(r,n,i),yp(r,n,h,f,o,c)}if(u&&u.length)for(let d of u)c??=Pu(r,n,i),yp(r,n,d,o,o,c)}}function Sw(e=1){return Kh(e)}function Mw(e,n){let t=null,r=tE(e);for(let o=0;o<n.length;o++){let i=n[o];if(i==="*"){t=o;continue}if(r===null?fg(e,i,!0):oE(r,i))return o}return t}function Tw(e){let n=V()[_e][ve];if(!n.projection){let t=e?e.length:1,r=n.projection=vh(t,null),o=r.slice(),i=n.child;for(;i!==null;){if(i.type!==128){let s=e?Mw(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function Aw(e,n=0,t,r,o,i){let s=V(),a=he(),c=r?e+1:null;c!==null&&uw(s,a,c,r,o,i,null,t);let u=Dr(a,oe+e,16,null,t||null);u.projection===null&&(u.projection=n),Cu();let d=!s[rr]||vu();s[_e][ve].projection[u.projection]===null&&c!==null?Nw(s,a,c):d&&!Hs(u)&&ME(a,s,u)}function Nw(e,n,t){let r=oe+t,o=n.data[r],i=e[r],s=el(i,o.tView.ssrId),a=Sg(e,o,void 0,{dehydratedView:s});Lg(i,a,0,Ku(o,s))}function _o(e,n,t,r){qI(e,n,t,r)}function Jl(e,n,t){WI(e,n,t)}function Er(e){let n=V(),t=he(),r=bu();as(r+1);let o=Bl(t,r);if(e.dirty&&Ah(n)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=QI(n,r);e.reset(i,xC),e.notifyOnChanges()}return!0}return!1}function Ir(){return GI(V(),bu())}function gs(e,n){return e<<17|n<<2}function Sn(e){return e>>17&32767}function Rw(e){return(e&2)==2}function xw(e,n){return e&131071|n<<17}function dl(e){return e|2}function gr(e){return(e&131068)>>2}function ju(e,n){return e&-131069|n<<2}function Ow(e){return(e&1)===1}function fl(e){return e|1}function kw(e,n,t,r,o,i){let s=i?n.classBindings:n.styleBindings,a=Sn(s),c=gr(s);e[r]=t;let u=!1,l;if(Array.isArray(t)){let d=t;l=d[1],(l===null||tr(d,l)>0)&&(u=!0)}else l=t;if(o)if(c!==0){let h=Sn(e[a+1]);e[r+1]=gs(h,a),h!==0&&(e[h+1]=ju(e[h+1],r)),e[a+1]=xw(e[a+1],r)}else e[r+1]=gs(a,0),a!==0&&(e[a+1]=ju(e[a+1],r)),a=r;else e[r+1]=gs(c,0),a===0?a=r:e[c+1]=ju(e[c+1],r),c=r;u&&(e[r+1]=dl(e[r+1])),Mp(e,l,r,!0),Mp(e,l,r,!1),Fw(n,l,e,r,i),s=gs(a,c),i?n.classBindings=s:n.styleBindings=s}function Fw(e,n,t,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof n=="string"&&tr(i,n)>=0&&(t[r+1]=fl(t[r+1]))}function Mp(e,n,t,r){let o=e[t+1],i=n===null,s=r?Sn(o):gr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];Pw(c,n)&&(a=!0,e[s+1]=r?fl(u):dl(u)),s=r?Sn(u):gr(u)}a&&(e[t+1]=r?dl(o):fl(o))}function Pw(e,n){return e===null||n==null||(Array.isArray(e)?e[1]:e)===n?!0:Array.isArray(e)&&typeof n=="string"?tr(e,n)>=0:!1}function bo(e,n){return Lw(e,n,null,!0),bo}function Lw(e,n,t,r){let o=V(),i=he(),s=_u(2);if(i.firstUpdatePass&&jw(i,e,s,r),n!==ft&&pr(o,s,n)){let a=i.data[Gt()];zw(i,a,o,o[Q],e,o[s+1]=Gw(n,t),r,s)}}function Vw(e,n){return n>=e.expandoStartIndex}function jw(e,n,t,r){let o=e.data;if(o[t+1]===null){let i=o[Gt()],s=Vw(e,t);Ww(i,r)&&n===null&&!s&&(n=!1),n=Bw(o,i,n,r),kw(o,i,n,t,s,r)}}function Bw(e,n,t,r){let o=qh(e),i=r?n.residualClasses:n.residualStyles;if(o===null)(r?n.classBindings:n.styleBindings)===0&&(t=Bu(null,e,n,t,r),t=Co(t,n.attrs,r),i=null);else{let s=n.directiveStylingLast;if(s===-1||e[s]!==o)if(t=Bu(o,e,n,t,r),i===null){let c=Hw(e,n,r);c!==void 0&&Array.isArray(c)&&(c=Bu(null,e,n,c[1],r),c=Co(c,n.attrs,r),Uw(e,n,r,c))}else i=$w(e,n,r)}return i!==void 0&&(r?n.residualClasses=i:n.residualStyles=i),t}function Hw(e,n,t){let r=t?n.classBindings:n.styleBindings;if(gr(r)!==0)return e[Sn(r)]}function Uw(e,n,t,r){let o=t?n.classBindings:n.styleBindings;e[Sn(o)]=r}function $w(e,n,t){let r,o=n.directiveEnd;for(let i=1+n.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Co(r,s,t)}return Co(r,n.attrs,t)}function Bu(e,n,t,r,o){let i=null,s=t.directiveEnd,a=t.directiveStylingLast;for(a===-1?a=t.directiveStart:a++;a<s&&(i=n[a],r=Co(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(t.directiveStylingLast=a),r}function Co(e,n,t){let r=t?1:2,o=-1;if(n!==null)for(let i=0;i<n.length;i++){let s=n[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Dh(e,s,t?!0:n[++i]))}return e===void 0?null:e}function zw(e,n,t,r,o,i,s,a){if(!(n.type&3))return;let c=e.data,u=c[a+1],l=Ow(u)?Tp(c,n,t,o,gr(u),s):void 0;if(!Ps(l)){Ps(i)||Rw(u)&&(i=Tp(c,null,t,o,a,s));let d=du(Gt(),t);AE(r,s,d,o,i)}}function Tp(e,n,t,r,o,i){let s=n===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,h=t[o+1];h===ft&&(h=d?Ae:void 0);let f=d?Ji(h,r):l===r?h:void 0;if(u&&!Ps(f)&&(f=Ji(c,r)),Ps(f)&&(a=f,s))return a;let v=e[o+1];o=s?Sn(v):gr(v)}if(n!==null){let c=i?n.residualClasses:n.residualStyles;c!=null&&(a=Ji(c,r))}return a}function Ps(e){return e!==void 0}function Gw(e,n){return e==null||e===""||(typeof n=="string"?e=e+n:typeof e=="object"&&(e=Ct(Us(e)))),e}function Ww(e,n){return(e.flags&(n?8:16))!==0}function qw(e,n=""){let t=V(),r=he(),o=e+oe,i=r.firstCreatePass?Dr(r,o,1,n,null):r.data[o],s=Zw(r,t,i,n,e);t[o]=s,ls()&&Ml(r,t,s,i),ur(i,!1)}var Zw=(e,n,t,r,o)=>(ho(!0),cE(n[Q],r));function Yw(e,n,t,r=""){return pr(e,is(),t)?n+gn(t)+r:ft}function Qw(e,n,t,r,o,i=""){let s=Uh(),a=wI(e,s,t,o);return _u(2),a?n+gn(t)+r+gn(o)+i:ft}function pm(e){return Xl("",e),pm}function Xl(e,n,t){let r=V(),o=Yw(r,e,n,t);return o!==ft&&mm(r,Gt(),o),Xl}function gm(e,n,t,r,o){let i=V(),s=Qw(i,e,n,t,r,o);return s!==ft&&mm(i,Gt(),s),gm}function mm(e,n,t){let r=du(n,e);uE(e[Q],r,t)}function Kw(e,n,t){let r=he();if(r.firstCreatePass){let o=ct(e);hl(t,r.data,r.blueprint,o,!0),hl(n,r.data,r.blueprint,o,!1)}}function hl(e,n,t,r,o){if(e=ue(e),Array.isArray(e))for(let i=0;i<e.length;i++)hl(e[i],n,t,r,o);else{let i=he(),s=V(),a=ie(),c=fn(e)?e:ue(e.provide),u=au(e),l=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(fn(e)||!e.multi){let f=new wn(u,o,m,null),v=Uu(c,n,o?l:l+h,d);v===-1?(zu(ws(a,s),i,c),Hu(i,e,n.length),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(f),s.push(f)):(t[v]=f,s[v]=f)}else{let f=Uu(c,n,l+h,d),v=Uu(c,n,l,l+h),E=f>=0&&t[f],T=v>=0&&t[v];if(o&&!T||!o&&!E){zu(ws(a,s),i,c);let R=e_(o?Xw:Jw,t.length,o,r,u,e);!o&&T&&(t[v].providerFactory=R),Hu(i,e,n.length,0),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(R),s.push(R)}else{let R=vm(t[o?v:f],u,!o&&r);Hu(i,e,f>-1?f:v,R)}!o&&r&&T&&t[v].componentProviders++}}}function Hu(e,n,t,r){let o=fn(n),i=_h(n);if(o||i){let c=(i?ue(n.useClass):n).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&n.multi){let l=u.indexOf(t);l===-1?u.push(t,[r,c]):u[l+1].push(r,c)}else u.push(t,c)}}}function vm(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function Uu(e,n,t,r){for(let o=t;o<r;o++)if(n[o]===e)return o;return-1}function Jw(e,n,t,r,o){return pl(this.multi,[])}function Xw(e,n,t,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=vo(r,r[b],this.providerFactory.index,o);s=c.slice(0,a),pl(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],pl(i,s);return s}function pl(e,n){for(let t=0;t<e.length;t++){let r=e[t];n.push(r())}return n}function e_(e,n,t,r,o,i){let s=new wn(e,t,m,null);return s.multi=[],s.index=n,s.componentProviders=0,vm(s,o,r&&!t),s}function Ke(e,n=[]){return t=>{t.providersResolver=(r,o)=>Kw(r,o?o(e):e,n)}}var Ls=class{ngModuleFactory;componentFactories;constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}},ed=(()=>{class e{compileModuleSync(t){return new xs(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){let r=this.compileModuleSync(t),o=ru(t),i=lg(o.declarations).reduce((s,a)=>{let c=ot(a);return c&&s.push(new Zt(c)),s},[]);return new Ls(r,i)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var t_=(()=>{class e{zone=p($);changeDetectionScheduler=p(Vt);applicationRef=p(Qe);applicationErrorHandler=p(Re);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(t){this.applicationErrorHandler(t)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ym({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new $(A(g({},Dm()),{scheduleInRootZone:t})),[{provide:$,useFactory:e},{provide:Bt,multi:!0,useFactory:()=>{let r=p(t_,{optional:!0});return()=>r.initialize()}},{provide:Bt,multi:!0,useFactory:()=>{let r=p(n_);return()=>{r.initialize()}}},n===!0?{provide:Nu,useValue:!0}:[],{provide:Ru,useValue:t??tm},{provide:Re,useFactory:()=>{let r=p($),o=p(Z),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get($e),i.handleError(s))})}}}]}function Dm(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var n_=(()=>{class e{subscription=new K;initialized=!1;zone=p($);pendingTasks=p(It);initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{$.assertNotInAngularZone(),queueMicrotask(()=>{t!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{$.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Cm=(()=>{class e{applicationErrorHandler=p(Re);appRef=p(Qe);taskService=p(It);ngZone=p($);zonelessEnabled=p(ds);tracing=p(Mn,{optional:!0});disableScheduling=p(Nu,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new K;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Os):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(Ru,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof ks||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&t===5)return;let r=!1;switch(t){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?wp:nm;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(t){return!(this.disableScheduling&&!t||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Os+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(t),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,wp(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function r_(){return typeof $localize<"u"&&$localize.locale||wo}var ta=new C("",{providedIn:"root",factory:()=>p(ta,{optional:!0,skipSelf:!0})||r_()});function je(e){return ah(e)}function wr(e,n){return Ui(e,n?.equal)}var Em=class{[ge];constructor(n){this[ge]=n}destroy(){this[ge].destroy()}};var bm=Symbol("InputSignalNode#UNSET"),D_=A(g({},$i),{transformFn:void 0,applyValueToInputSignal(e,n){Xn(e,n)}});function Sm(e,n){let t=Object.create(D_);t.value=e,t.transformFn=n?.transform;function r(){if(Kn(t),t.value===bm){let o=null;throw new y(-950,o)}return t.value}return r[ge]=t,r}var ra=class{attributeName;constructor(n){this.attributeName=n}__NG_ELEMENT_ID__=()=>Yt(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},C_=new C("");C_.__NG_ELEMENT_ID__=e=>{let n=ie();if(n===null)throw new y(204,!1);if(n.type&2)return n.value;if(e&8)return null;throw new y(204,!1)};function Im(e,n){return Sm(e,n)}function E_(e){return Sm(bm,e)}var Mm=(Im.required=E_,Im);var td=new C(""),I_=new C("");function So(e){return!e.moduleRef}function w_(e){let n=So(e)?e.r3Injector:e.moduleRef.injector,t=n.get($);return t.run(()=>{So(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=n.get(Re),o;if(t.runOutsideAngular(()=>{o=t.onError.subscribe({next:r})}),So(e)){let i=()=>n.destroy(),s=e.platformInjector.get(td);s.add(i),n.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(td);s.add(i),e.moduleRef.onDestroy(()=>{mo(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return b_(r,t,()=>{let i=n.get(It),s=i.add(),a=n.get(Zl);return a.runInitializers(),a.donePromise.then(()=>{let c=n.get(ta,wo);if(hm(c||wo),!n.get(I_,!0))return So(e)?n.get(Qe):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(So(e)){let l=n.get(Qe);return e.rootComponent!==void 0&&l.bootstrap(e.rootComponent),l}else return __?.(e.moduleRef,e.allPlatformModules),e.moduleRef}).finally(()=>void i.remove(s))})})}var __;function b_(e,n,t){try{let r=t();return Qt(r)?r.catch(o=>{throw n.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw n.runOutsideAngular(()=>e(r)),r}}var na=null;function S_(e=[],n){return re.create({name:n,providers:[{provide:so,useValue:"platform"},{provide:td,useValue:new Set([()=>na=null])},...e]})}function M_(e=[]){if(na)return na;let n=S_(e);return na=n,am(),T_(n),n}function T_(e){let n=e.get(js,null);me(e,()=>{n?.forEach(t=>t())})}var Je=(()=>{class e{static __NG_ELEMENT_ID__=A_}return e})();function A_(e){return N_(ie(),V(),(e&16)===16)}function N_(e,n,t){if($t(e)&&!t){let r=Le(e.index,n);return new qt(r,r)}else if(e.type&175){let r=n[_e];return new qt(r,n)}return null}var nd=class{constructor(){}supports(n){return jl(n)}create(n){return new rd(n)}},R_=(e,n)=>n,rd=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(n){this._trackByFn=n||R_}forEachItem(n){let t;for(t=this._itHead;t!==null;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,r=this._removalsHead,o=0,i=null;for(;t||r;){let s=!r||t&&t.currentIndex<wm(r,o,i)?t:r,a=wm(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(t=t._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,v=f+h;l<=v&&v<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&n(s,a,c)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;t!==null;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;t!==null;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;t!==null;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;t!==null;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;t!==null;t=t._nextIdentityChange)n(t)}diff(n){if(n==null&&(n=[]),!jl(n))throw new y(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._itHead,r=!1,o,i,s;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)i=n[a],s=this._trackByFn(a,i),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,i,s,a),r=!0):(r&&(t=this._verifyReinsertion(t,i,s,a)),Object.is(t.item,i)||this._addIdentityChange(t,i)),t=t._next}else o=0,$g(n,a=>{s=this._trackByFn(o,a),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,a,s,o),r=!0):(r&&(t=this._verifyReinsertion(t,a,s,o)),Object.is(t.item,a)||this._addIdentityChange(t,a)),t=t._next,o++}),this.length=o;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;n!==null;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;n!==null;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;n!==null;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,r,o){let i;return n===null?i=this._itTail:(i=n._prev,this._remove(n)),n=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,i,o)):(n=this._linkedRecords===null?null:this._linkedRecords.get(r,o),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,i,o)):n=this._addAfter(new od(t,r),i,o)),n}_verifyReinsertion(n,t,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?n=this._reinsertAfter(i,n._prev,o):n.currentIndex!=o&&(n.currentIndex=o,this._addToMoves(n,o)),n}_truncate(n){for(;n!==null;){let t=n._next;this._addToRemovals(this._unlink(n)),n=t}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(n);let o=n._prevRemoved,i=n._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(n,t,r),this._addToMoves(n,r),n}_moveAfter(n,t,r){return this._unlink(n),this._insertAfter(n,t,r),this._addToMoves(n,r),n}_addAfter(n,t,r){return this._insertAfter(n,t,r),this._additionsTail===null?this._additionsTail=this._additionsHead=n:this._additionsTail=this._additionsTail._nextAdded=n,n}_insertAfter(n,t,r){let o=t===null?this._itHead:t._next;return n._next=o,n._prev=t,o===null?this._itTail=n:o._prev=n,t===null?this._itHead=n:t._next=n,this._linkedRecords===null&&(this._linkedRecords=new oa),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){this._linkedRecords!==null&&this._linkedRecords.remove(n);let t=n._prev,r=n._next;return t===null?this._itHead=r:t._next=r,r===null?this._itTail=t:r._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail===null?this._movesTail=this._movesHead=n:this._movesTail=this._movesTail._nextMoved=n),n}_addToRemovals(n){return this._unlinkedRecords===null&&(this._unlinkedRecords=new oa),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=n:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=n,n}},od=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(n,t){this.item=n,this.trackById=t}},id=class{_head=null;_tail=null;add(n){this._head===null?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let r;for(r=this._head;r!==null;r=r._nextDup)if((t===null||t<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){let t=n._prevDup,r=n._nextDup;return t===null?this._head=r:t._nextDup=r,r===null?this._tail=t:r._prevDup=t,this._head===null}},oa=class{map=new Map;put(n){let t=n.trackById,r=this.map.get(t);r||(r=new id,this.map.set(t,r)),r.add(n)}get(n,t){let r=n,o=this.map.get(r);return o?o.get(n,t):null}remove(n){let t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function wm(e,n,t){let r=e.previousIndex;if(r===null)return r;let o=0;return t&&r<t.length&&(o=t[r]),r+n+o}function _m(){return new sd([new nd])}var sd=(()=>{class e{factories;static \u0275prov=D({token:e,providedIn:"root",factory:_m});constructor(t){this.factories=t}static create(t,r){if(r!=null){let o=r.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||_m()),deps:[[e,new xp,new Rp]]}}find(t){let r=this.factories.find(o=>o.supports(t));if(r!=null)return r;throw new y(901,!1)}}return e})();function Tm(e){H(8);try{let{rootComponent:n,appProviders:t,platformProviders:r}=e,o=M_(r),i=[ym({}),{provide:Vt,useExisting:Cm},ep,...t||[]],s=new Do({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return w_({r3Injector:s.injector,platformInjector:o,rootComponent:n})}catch(n){return Promise.reject(n)}finally{H(9)}}function Mo(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Am(e,n){let t=ot(e),r=n.elementInjector||nr();return new Zt(t).create(r,n.projectableNodes,n.hostElement,n.environmentInjector,n.directives,n.bindings)}function ad(e){let n=ot(e);if(!n)return null;let t=new Zt(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}var xm=null;function Be(){return xm}function cd(e){xm??=e}var To=class{},ud=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Om),providedIn:"platform"})}return e})();var Om=(()=>{class e extends ud{_location;_history;_doc=p(ee);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Be().getBaseHref(this._doc)}onPopState(t){let r=Be().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){let r=Be().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,o){this._history.pushState(t,r,o)}replaceState(t,r,o){this._history.replaceState(t,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function km(e,n){return e?n?e.endsWith("/")?n.startsWith("/")?e+n.slice(1):e+n:n.startsWith("/")?e+n:`${e}/${n}`:e:n}function Nm(e){let n=e.search(/#|\?|$/);return e[n-1]==="/"?e.slice(0,n-1)+e.slice(n):e}function Kt(e){return e&&e[0]!=="?"?`?${e}`:e}var St=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Pm),providedIn:"root"})}return e})(),Fm=new C(""),Pm=(()=>{class e extends St{_platformLocation;_baseHref;_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(ee).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return km(this._baseHref,t)}path(t=!1){let r=this._platformLocation.pathname+Kt(this._platformLocation.search),o=this._platformLocation.hash;return o&&t?`${r}${o}`:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+Kt(i));this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+Kt(i));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(I(ud),I(Fm,8))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Mt=(()=>{class e{_subject=new U;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(t){this._locationStrategy=t;let r=this._locationStrategy.getBaseHref();this._basePath=k_(Nm(Rm(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+Kt(r))}normalize(t){return e.stripTrailingSlash(O_(this._basePath,Rm(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",o=null){this._locationStrategy.pushState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Kt(r)),o)}replaceState(t,r="",o=null){this._locationStrategy.replaceState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Kt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(o=>o(t,r))}subscribe(t,r,o){return this._subject.subscribe({next:t,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=Kt;static joinWithSlash=km;static stripTrailingSlash=Nm;static \u0275fac=function(r){return new(r||e)(I(St))};static \u0275prov=D({token:e,factory:()=>x_(),providedIn:"root"})}return e})();function x_(){return new Mt(I(St))}function O_(e,n){if(!e||!n.startsWith(e))return n;let t=n.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:n}function Rm(e){return e.replace(/\/index.html$/,"")}function k_(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var ia=class{$implicit;ngForOf;index;count;constructor(n,t,r,o){this.$implicit=n,this.ngForOf=t,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},jm=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(t,r,o){this._viewContainer=t,this._template=r,this._differs=o}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){let t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){let r=this._viewContainer;t.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new ia(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Lm(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}t.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Lm(i,o)})}static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(m(Ve),m(Ze),m(sd))};static \u0275dir=F({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Lm(e,n){e.context.$implicit=n.item}var F_=(()=>{class e{_viewContainer;_context=new sa;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(t,r){this._viewContainer=t,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){Vm(t,!1),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){Vm(t,!1),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(m(Ve),m(Ze))};static \u0275dir=F({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),sa=class{$implicit=null;ngIf=null};function Vm(e,n){if(e&&!e.createEmbeddedView)throw new y(2020,!1)}var P_=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(t){this._viewContainerRef=t}ngOnChanges(t){if(this._shouldRecreateView(t)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(t,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(m(Ve))};static \u0275dir=F({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[be]})}return e})();var Bm=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=_t({type:e});static \u0275inj=rt({})}return e})();function ld(e,n){n=encodeURIComponent(n);for(let t of e.split(";")){let r=t.indexOf("="),[o,i]=r==-1?[t,""]:[t.slice(0,r),t.slice(r+1)];if(o.trim()===n)return decodeURIComponent(i)}return null}var Ao=class{};var Hm="browser";var ca=new C(""),gd=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(t,r){this._zone=r,t.forEach(o=>{o.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,o,i){return this._findPluginFor(r).addEventListener(t,r,o,i)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(i=>i.supports(t)),!r)throw new y(5101,!1);return this._eventNameToPlugin.set(t,r),r}static \u0275fac=function(r){return new(r||e)(I(ca),I($))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),No=class{_doc;constructor(n){this._doc=n}manager},dd="ng-app-id";function $m(e){for(let n of e)n.remove()}function zm(e,n){let t=n.createElement("style");return t.textContent=e,t}function L_(e,n,t,r){let o=e.head?.querySelectorAll(`style[${dd}="${n}"],link[${dd}="${n}"]`);if(o)for(let i of o)i.removeAttribute(dd),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&t.set(i.textContent,{usage:0,elements:[i]})}function hd(e,n){let t=n.createElement("link");return t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t}var md=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;constructor(t,r,o,i={}){this.doc=t,this.appId=r,this.nonce=o,L_(t,r,this.inline,this.external),this.hosts.add(t.head)}addStyles(t,r){for(let o of t)this.addUsage(o,this.inline,zm);r?.forEach(o=>this.addUsage(o,this.external,hd))}removeStyles(t,r){for(let o of t)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(t,r,o){let i=r.get(t);i?i.usage++:r.set(t,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(t,this.doc)))})}removeUsage(t,r){let o=r.get(t);o&&(o.usage--,o.usage<=0&&($m(o.elements),r.delete(t)))}ngOnDestroy(){for(let[,{elements:t}]of[...this.inline,...this.external])$m(t);this.hosts.clear()}addHost(t){this.hosts.add(t);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(t,zm(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(t,hd(r,this.doc)))}removeHost(t){this.hosts.delete(t)}addElement(t,r){return this.nonce&&r.setAttribute("nonce",this.nonce),t.appendChild(r)}static \u0275fac=function(r){return new(r||e)(I(ee),I(Vs),I(Bs,8),I(yr))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),fd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},vd=/%COMP%/g;var Wm="%COMP%",V_=`_nghost-${Wm}`,j_=`_ngcontent-${Wm}`,B_=!0,H_=new C("",{providedIn:"root",factory:()=>B_});function U_(e){return j_.replace(vd,e)}function $_(e){return V_.replace(vd,e)}function qm(e,n){return n.map(t=>t.replace(vd,e))}var yd=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(t,r,o,i,s,a,c,u=null,l=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=!1,this.defaultRenderer=new Ro(t,s,c,this.platformIsServer,this.tracingService)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(t,r);return o instanceof aa?o.applyToHost(t):o instanceof xo&&o.applyStyles(),o}getOrCreateRenderer(t,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case wt.Emulated:i=new aa(c,u,r,this.appId,l,s,a,d,h);break;case wt.ShadowDom:return new pd(c,u,t,r,s,a,this.nonce,d,h);default:i=new xo(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(t){this.rendererByCompId.delete(t)}static \u0275fac=function(r){return new(r||e)(I(gd),I(md),I(Vs),I(H_),I(ee),I(yr),I($),I(Bs),I(Mn,8))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Ro=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,t,r,o,i){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(n,t){return t?this.doc.createElementNS(fd[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(Gm(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(Gm(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new y(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,o){if(o){t=o+":"+t;let i=fd[o];i?n.setAttributeNS(i,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){let o=fd[r];o?n.removeAttributeNS(o,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,o){o&(dt.DashCase|dt.Important)?n.style.setProperty(t,r,o&dt.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&dt.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){n!=null&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r,o){if(typeof n=="string"&&(n=Be().getGlobalEventTarget(this.doc,n),!n))throw new y(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(n,t,i)),this.eventManager.addEventListener(n,t,i,o)}decoratePreventDefault(n){return t=>{if(t==="__ngUnwrap__")return n;n(t)===!1&&t.preventDefault()}}};function Gm(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var pd=class extends Ro{sharedStylesHost;hostEl;shadowRoot;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,c,u),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=qm(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=hd(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},xo=class extends Ro{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,a,c),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?qm(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},aa=class extends xo{contentAttr;hostAttr;constructor(n,t,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(n,t,r,i,s,a,c,u,l),this.contentAttr=U_(l),this.hostAttr=$_(l)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){let r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}};var ua=class e extends To{supportsDOMEvents=!0;static makeCurrent(){cd(new e)}onAndCancel(n,t,r,o){return n.addEventListener(t,r,o),()=>{n.removeEventListener(t,r,o)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return t=t||this.getDefaultDocument(),t.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return t==="window"?window:t==="document"?n:t==="body"?n.body:null}getBaseHref(n){let t=z_();return t==null?null:G_(t)}resetBaseElement(){Oo=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return ld(document.cookie,n)}},Oo=null;function z_(){return Oo=Oo||document.head.querySelector("base"),Oo?Oo.getAttribute("href"):null}function G_(e){return new URL(e,document.baseURI).pathname}var W_=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Ym=(()=>{class e extends No{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,o,i){return t.addEventListener(r,o,i),()=>this.removeEventListener(t,r,o,i)}removeEventListener(t,r,o,i){return t.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(I(ee))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),Zm=["alt","control","meta","shift"],q_={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Z_={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},Qm=(()=>{class e extends No{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Be().onAndCancel(t,s.domEventName,a,i))}static parseEventName(t){let r=t.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Zm.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(t,r){let o=q_[t.key]||t.key,i="";return r.indexOf("code.")>-1&&(o=t.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Zm.forEach(s=>{if(s!==o){let a=Z_[s];a(t)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(t,r,o){return i=>{e.matchEventFullKeyCode(i,t)&&o.runGuarded(()=>r(i))}}static _normalizeKey(t){return t==="esc"?"escape":t}static \u0275fac=function(r){return new(r||e)(I(ee))};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})();function Y_(e,n){let t=g({rootComponent:e},Q_(n));return Tm(t)}function Q_(e){return{appProviders:[...tb,...e?.providers??[]],platformProviders:eb}}function K_(){ua.makeCurrent()}function J_(){return new $e}function X_(){return vl(document),document}var eb=[{provide:yr,useValue:Hm},{provide:js,useValue:K_,multi:!0},{provide:ee,useFactory:X_}];var tb=[{provide:so,useValue:"root"},{provide:$e,useFactory:J_},{provide:ca,useClass:Ym,multi:!0,deps:[ee]},{provide:ca,useClass:Qm,multi:!0,deps:[ee]},yd,md,gd,{provide:_n,useExisting:yd},{provide:Ao,useClass:W_},[]];var Km=(()=>{class e{_doc;constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static \u0275fac=function(r){return new(r||e)(I(ee))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var M="primary",Zo=Symbol("RouteTitle"),wd=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}};function Rn(e){return new wd(e)}function iv(e,n,t){let r=t.path.split("/");if(r.length>e.length||t.pathMatch==="full"&&(n.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function rb(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!pt(e[t],n[t]))return!1;return!0}function pt(e,n){let t=e?_d(e):void 0,r=n?_d(n):void 0;if(!t||!r||t.length!=r.length)return!1;let o;for(let i=0;i<t.length;i++)if(o=t[i],!sv(e[o],n[o]))return!1;return!0}function _d(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function sv(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;let t=[...e].sort(),r=[...n].sort();return t.every((o,i)=>r[i]===o)}else return e===n}function av(e){return e.length>0?e[e.length-1]:null}function Nt(e){return mc(e)?e:Qt(e)?W(Promise.resolve(e)):_(e)}var ob={exact:uv,subset:lv},cv={exact:ib,subset:sb,ignored:()=>!0};function Jm(e,n,t){return ob[t.paths](e.root,n.root,t.matrixParams)&&cv[t.queryParams](e.queryParams,n.queryParams)&&!(t.fragment==="exact"&&e.fragment!==n.fragment)}function ib(e,n){return pt(e,n)}function uv(e,n,t){if(!An(e.segments,n.segments)||!fa(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!e.children[r]||!uv(e.children[r],n.children[r],t))return!1;return!0}function sb(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>sv(e[t],n[t]))}function lv(e,n,t){return dv(e,n,n.segments,t)}function dv(e,n,t,r){if(e.segments.length>t.length){let o=e.segments.slice(0,t.length);return!(!An(o,t)||n.hasChildren()||!fa(o,t,r))}else if(e.segments.length===t.length){if(!An(e.segments,t)||!fa(e.segments,t,r))return!1;for(let o in n.children)if(!e.children[o]||!lv(e.children[o],n.children[o],r))return!1;return!0}else{let o=t.slice(0,e.segments.length),i=t.slice(e.segments.length);return!An(e.segments,o)||!fa(e.segments,o,r)||!e.children[M]?!1:dv(e.children[M],n,i,r)}}function fa(e,n,t){return n.every((r,o)=>cv[t](e[o].parameters,r.parameters))}var mt=class{root;queryParams;fragment;_queryParamMap;constructor(n=new j([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Rn(this.queryParams),this._queryParamMap}toString(){return ub.serialize(this)}},j=class{segments;children;parent=null;constructor(n,t){this.segments=n,this.children=t,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return ha(this)}},Jt=class{path;parameters;_parameterMap;constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=Rn(this.parameters),this._parameterMap}toString(){return hv(this)}};function ab(e,n){return An(e,n)&&e.every((t,r)=>pt(t.parameters,n[r].parameters))}function An(e,n){return e.length!==n.length?!1:e.every((t,r)=>t.path===n[r].path)}function cb(e,n){let t=[];return Object.entries(e.children).forEach(([r,o])=>{r===M&&(t=t.concat(n(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==M&&(t=t.concat(n(o,r)))}),t}var On=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>new xn,providedIn:"root"})}return e})(),xn=class{parse(n){let t=new Sd(n);return new mt(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){let t=`/${ko(n.root,!0)}`,r=fb(n.queryParams),o=typeof n.fragment=="string"?`#${lb(n.fragment)}`:"";return`${t}${r}${o}`}},ub=new xn;function ha(e){return e.segments.map(n=>hv(n)).join("/")}function ko(e,n){if(!e.hasChildren())return ha(e);if(n){let t=e.children[M]?ko(e.children[M],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==M&&r.push(`${o}:${ko(i,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}else{let t=cb(e,(r,o)=>o===M?[ko(e.children[M],!1)]:[`${o}:${ko(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[M]!=null?`${ha(e)}/${t[0]}`:`${ha(e)}/(${t.join("//")})`}}function fv(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function la(e){return fv(e).replace(/%3B/gi,";")}function lb(e){return encodeURI(e)}function bd(e){return fv(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function pa(e){return decodeURIComponent(e)}function Xm(e){return pa(e.replace(/\+/g,"%20"))}function hv(e){return`${bd(e.path)}${db(e.parameters)}`}function db(e){return Object.entries(e).map(([n,t])=>`;${bd(n)}=${bd(t)}`).join("")}function fb(e){let n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(o=>`${la(t)}=${la(o)}`).join("&"):`${la(t)}=${la(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}var hb=/^[^\/()?;#]+/;function Dd(e){let n=e.match(hb);return n?n[0]:""}var pb=/^[^\/()?;=#]+/;function gb(e){let n=e.match(pb);return n?n[0]:""}var mb=/^[^=?&#]+/;function vb(e){let n=e.match(mb);return n?n[0]:""}var yb=/^[^&#]+/;function Db(e){let n=e.match(yb);return n?n[0]:""}var Sd=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new j([],{}):new j([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[M]=new j(n,t)),r}parseSegment(){let n=Dd(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new y(4009,!1);return this.capture(n),new Jt(pa(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let t=gb(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let o=Dd(this.remaining);o&&(r=o,this.capture(r))}n[pa(t)]=pa(r)}parseQueryParam(n){let t=vb(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let s=Db(this.remaining);s&&(r=s,this.capture(r))}let o=Xm(t),i=Xm(r);if(n.hasOwnProperty(o)){let s=n[o];Array.isArray(s)||(s=[s],n[o]=s),s.push(i)}else n[o]=i}parseParens(n){let t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Dd(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new y(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):n&&(i=M);let s=this.parseChildren();t[i]=Object.keys(s).length===1?s[M]:new j([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new y(4011,!1)}};function pv(e){return e.segments.length>0?new j([],{[M]:e}):e}function gv(e){let n={};for(let[r,o]of Object.entries(e.children)){let i=gv(o);if(r===M&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))n[s]=a;else(i.segments.length>0||i.hasChildren())&&(n[r]=i)}let t=new j(e.segments,n);return Cb(t)}function Cb(e){if(e.numberOfChildren===1&&e.children[M]){let n=e.children[M];return new j(e.segments.concat(n.segments),n.children)}return e}function Xt(e){return e instanceof mt}function mv(e,n,t=null,r=null){let o=vv(e);return yv(o,n,t,r)}function vv(e){let n;function t(i){let s={};for(let c of i.children){let u=t(c);s[c.outlet]=u}let a=new j(i.url,s);return i===e&&(n=a),a}let r=t(e.root),o=pv(r);return n??o}function yv(e,n,t,r){let o=e;for(;o.parent;)o=o.parent;if(n.length===0)return Cd(o,o,o,t,r);let i=Eb(n);if(i.toRoot())return Cd(o,o,new j([],{}),t,r);let s=Ib(i,o,e),a=s.processChildren?Po(s.segmentGroup,s.index,i.commands):Cv(s.segmentGroup,s.index,i.commands);return Cd(o,s.segmentGroup,a,t,r)}function ga(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function jo(e){return typeof e=="object"&&e!=null&&e.outlets}function Cd(e,n,t,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===n?s=t:s=Dv(e,n,t);let a=pv(gv(s));return new mt(a,i,o)}function Dv(e,n,t){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===n?r[o]=t:r[o]=Dv(i,n,t)}),new j(e.segments,r)}var ma=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&ga(r[0]))throw new y(4003,!1);let o=r.find(jo);if(o&&o!==av(r))throw new y(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function Eb(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new ma(!0,0,e);let n=0,t=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?t=!0:a===".."?n++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new ma(t,n,r)}var Sr=class{segmentGroup;processChildren;index;constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}};function Ib(e,n,t){if(e.isAbsolute)return new Sr(n,!0,0);if(!t)return new Sr(n,!1,NaN);if(t.parent===null)return new Sr(t,!0,0);let r=ga(e.commands[0])?0:1,o=t.segments.length-1+r;return wb(t,o,e.numberOfDoubleDots)}function wb(e,n,t){let r=e,o=n,i=t;for(;i>o;){if(i-=o,r=r.parent,!r)throw new y(4005,!1);o=r.segments.length}return new Sr(r,!1,o-i)}function _b(e){return jo(e[0])?e[0].outlets:{[M]:e}}function Cv(e,n,t){if(e??=new j([],{}),e.segments.length===0&&e.hasChildren())return Po(e,n,t);let r=bb(e,n,t),o=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new j(e.segments.slice(0,r.pathIndex),{});return i.children[M]=new j(e.segments.slice(r.pathIndex),e.children),Po(i,0,o)}else return r.match&&o.length===0?new j(e.segments,{}):r.match&&!e.hasChildren()?Md(e,n,t):r.match?Po(e,0,o):Md(e,n,t)}function Po(e,n,t){if(t.length===0)return new j(e.segments,{});{let r=_b(t),o={};if(Object.keys(r).some(i=>i!==M)&&e.children[M]&&e.numberOfChildren===1&&e.children[M].segments.length===0){let i=Po(e.children[M],n,t);return new j(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Cv(e.children[i],n,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new j(e.segments,o)}}function bb(e,n,t){let r=0,o=n,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=t.length)return i;let s=e.segments[o],a=t[r];if(jo(a))break;let c=`${a}`,u=r<t.length-1?t[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!tv(c,u,s))return i;r+=2}else{if(!tv(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function Md(e,n,t){let r=e.segments.slice(0,n),o=0;for(;o<t.length;){let i=t[o];if(jo(i)){let c=Sb(i.outlets);return new j(r,c)}if(o===0&&ga(t[0])){let c=e.segments[n];r.push(new Jt(c.path,ev(t[0]))),o++;continue}let s=jo(i)?i.outlets[M]:`${i}`,a=o<t.length-1?t[o+1]:null;s&&a&&ga(a)?(r.push(new Jt(s,ev(a))),o+=2):(r.push(new Jt(s,{})),o++)}return new j(r,{})}function Sb(e){let n={};return Object.entries(e).forEach(([t,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[t]=Md(new j([],{}),0,r))}),n}function ev(e){let n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function tv(e,n,t){return e==t.path&&pt(n,t.parameters)}var Lo="imperative",se=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(se||{}),Oe=class{id;url;constructor(n,t){this.id=n,this.url=t}},Tt=class extends Oe{type=se.NavigationStart;navigationTrigger;restoredState;constructor(n,t,r="imperative",o=null){super(n,t),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},et=class extends Oe{urlAfterRedirects;type=se.NavigationEnd;constructor(n,t,r){super(n,t),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Ce=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e}(Ce||{}),Bo=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Bo||{}),gt=class extends Oe{reason;code;type=se.NavigationCancel;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},At=class extends Oe{reason;code;type=se.NavigationSkipped;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}},Tr=class extends Oe{error;target;type=se.NavigationError;constructor(n,t,r,o){super(n,t),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Ho=class extends Oe{urlAfterRedirects;state;type=se.RoutesRecognized;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},va=class extends Oe{urlAfterRedirects;state;type=se.GuardsCheckStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ya=class extends Oe{urlAfterRedirects;state;shouldActivate;type=se.GuardsCheckEnd;constructor(n,t,r,o,i){super(n,t),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Da=class extends Oe{urlAfterRedirects;state;type=se.ResolveStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ca=class extends Oe{urlAfterRedirects;state;type=se.ResolveEnd;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ea=class{route;type=se.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Ia=class{route;type=se.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},wa=class{snapshot;type=se.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},_a=class{snapshot;type=se.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ba=class{snapshot;type=se.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Sa=class{snapshot;type=se.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Uo=class{},Ar=class{url;navigationBehaviorOptions;constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}};function Mb(e){return!(e instanceof Uo)&&!(e instanceof Ar)}function Tb(e,n){return e.providers&&!e._injector&&(e._injector=Cr(e.providers,n,`Route: ${e.path}`)),e._injector??n}function Xe(e){return e.outlet||M}function Ab(e,n){let t=e.filter(r=>Xe(r)===n);return t.push(...e.filter(r=>Xe(r)!==n)),t}function xr(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){let t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}var Ma=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return xr(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new Rt(this.rootInjector)}},Rt=(()=>{class e{rootInjector;contexts=new Map;constructor(t){this.rootInjector=t}onChildOutletCreated(t,r){let o=this.getOrCreateContext(t);o.outlet=r,this.contexts.set(t,o)}onChildOutletDestroyed(t){let r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new Ma(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static \u0275fac=function(r){return new(r||e)(I(Z))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ta=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){let t=Td(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){let t=Td(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){let t=Ad(n,this._root);return t.length<2?[]:t[t.length-2].children.map(o=>o.value).filter(o=>o!==n)}pathFromRoot(n){return Ad(n,this._root).map(t=>t.value)}};function Td(e,n){if(e===n.value)return n;for(let t of n.children){let r=Td(e,t);if(r)return r}return null}function Ad(e,n){if(e===n.value)return[n];for(let t of n.children){let r=Ad(e,t);if(r.length)return r.unshift(n),r}return[]}var xe=class{value;children;constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}};function br(e){let n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}var $o=class extends Ta{snapshot;constructor(n,t){super(n),this.snapshot=t,Ld(this,n)}toString(){return this.snapshot.toString()}};function Ev(e){let n=Nb(e),t=new J([new Jt("",{})]),r=new J({}),o=new J({}),i=new J({}),s=new J(""),a=new Se(t,r,i,s,o,M,e,n.root);return a.snapshot=n.root,new $o(new xe(a,[]),n)}function Nb(e){let n={},t={},r={},o="",i=new Nn([],n,r,o,t,M,e,null,{});return new zo("",new xe(i,[]))}var Se=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,t,r,o,i,s,a,c){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(L(u=>u[Zo]))??_(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(L(n=>Rn(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(L(n=>Rn(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Aa(e,n,t="emptyOnly"){let r,{routeConfig:o}=e;return n!==null&&(t==="always"||o?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:g(g({},n.params),e.params),data:g(g({},n.data),e.data),resolve:g(g(g(g({},e.data),n.data),o?.data),e._resolvedData)}:r={params:g({},e.params),data:g({},e.data),resolve:g(g({},e.data),e._resolvedData??{})},o&&wv(o)&&(r.resolve[Zo]=o.title),r}var Nn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Zo]}constructor(n,t,r,o,i,s,a,c,u){this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Rn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Rn(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),t=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${t}')`}},zo=class extends Ta{url;constructor(n,t){super(t),this.url=n,Ld(this,t)}toString(){return Iv(this._root)}};function Ld(e,n){n.value._routerState=e,n.children.forEach(t=>Ld(e,t))}function Iv(e){let n=e.children.length>0?` { ${e.children.map(Iv).join(", ")} } `:"";return`${e.value}${n}`}function Ed(e){if(e.snapshot){let n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,pt(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),pt(n.params,t.params)||e.paramsSubject.next(t.params),rb(n.url,t.url)||e.urlSubject.next(t.url),pt(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Nd(e,n){let t=pt(e.params,n.params)&&ab(e.url,n.url),r=!e.parent!=!n.parent;return t&&!r&&(!e.parent||Nd(e.parent,n.parent))}function wv(e){return typeof e.title=="string"||e.title===null}var _v=new C(""),Vd=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=M;activateEvents=new z;deactivateEvents=new z;attachEvents=new z;detachEvents=new z;routerOutletData=Mm(void 0);parentContexts=p(Rt);location=p(Ve);changeDetector=p(Je);inputBinder=p(Oa,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(t){if(t.name){let{firstChange:r,previousValue:o}=t.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new y(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new y(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new y(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new y(4013,!1);this._activatedRoute=t;let o=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Rd(t,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=F({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[be]})}return e})(),Rd=class{route;childContexts;parent;outletData;constructor(n,t,r,o){this.route=n,this.childContexts=t,this.parent=r,this.outletData=o}get(n,t){return n===Se?this.route:n===Rt?this.childContexts:n===_v?this.outletData:this.parent.get(n,t)}},Oa=new C("");var jd=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Hl({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Xs(0,"router-outlet")},dependencies:[Vd],encapsulation:2})}return e})();function Bd(e){let n=e.children&&e.children.map(Bd),t=n?A(g({},e),{children:n}):g({},e);return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==M&&(t.component=jd),t}function Rb(e,n,t){let r=Go(e,n._root,t?t._root:void 0);return new $o(r,n)}function Go(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){let r=t.value;r._futureSnapshot=n.value;let o=xb(e,n,t);return new xe(r,o)}else{if(e.shouldAttach(n.value)){let i=e.retrieve(n.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>Go(e,a)),s}}let r=Ob(n.value),o=n.children.map(i=>Go(e,i));return new xe(r,o)}}function xb(e,n,t){return n.children.map(r=>{for(let o of t.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Go(e,r,o);return Go(e,r)})}function Ob(e){return new Se(new J(e.url),new J(e.params),new J(e.queryParams),new J(e.fragment),new J(e.data),e.outlet,e.component,e)}var Nr=class{redirectTo;navigationBehaviorOptions;constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}},bv="ngNavigationCancelingError";function Na(e,n){let{redirectTo:t,navigationBehaviorOptions:r}=Xt(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,o=Sv(!1,Ce.Redirect);return o.url=t,o.navigationBehaviorOptions=r,o}function Sv(e,n){let t=new Error(`NavigationCancelingError: ${e||""}`);return t[bv]=!0,t.cancellationCode=n,t}function kb(e){return Mv(e)&&Xt(e.url)}function Mv(e){return!!e&&e[bv]}var Fb=(e,n,t,r)=>L(o=>(new xd(n,o.targetRouterState,o.currentRouterState,t,r).activate(e),o)),xd=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,t,r,o,i){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(n){let t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),Ed(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){let o=br(t);n.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else i&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=br(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=br(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){let o=br(t);n.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new Sa(i.value.snapshot))}),n.children.length&&this.forwardEvent(new _a(n.value.snapshot))}activateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(Ed(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Ed(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},Ra=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Mr=class{component;route;constructor(n,t){this.component=n,this.route=t}};function Pb(e,n,t){let r=e._root,o=n?n._root:null;return Fo(r,o,t,[r.value])}function Lb(e){let n=e.routeConfig?e.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:e,guards:n}}function Or(e,n){let t=Symbol(),r=n.get(e,t);return r===t?typeof e=="function"&&!zc(e)?e:n.get(e):r}function Fo(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=br(n);return e.children.forEach(s=>{Vb(s,i[s.value.outlet],t,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Vo(a,t.getContext(s),o)),o}function Vb(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=jb(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Ra(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?Fo(e,n,a?a.children:null,r,o):Fo(e,n,t,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Mr(a.outlet.component,s))}else s&&Vo(n,a,o),o.canActivateChecks.push(new Ra(r)),i.component?Fo(e,null,a?a.children:null,r,o):Fo(e,null,t,r,o);return o}function jb(e,n,t){if(typeof t=="function")return t(e,n);switch(t){case"pathParamsChange":return!An(e.url,n.url);case"pathParamsOrQueryParamsChange":return!An(e.url,n.url)||!pt(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Nd(e,n)||!pt(e.queryParams,n.queryParams);case"paramsChange":default:return!Nd(e,n)}}function Vo(e,n,t){let r=br(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?n?Vo(s,n.children.getContext(i),t):Vo(s,null,t):Vo(s,n,t)}),o.component?n&&n.outlet&&n.outlet.isActivated?t.canDeactivateChecks.push(new Mr(n.outlet.component,o)):t.canDeactivateChecks.push(new Mr(null,o)):t.canDeactivateChecks.push(new Mr(null,o))}function Yo(e){return typeof e=="function"}function Bb(e){return typeof e=="boolean"}function Hb(e){return e&&Yo(e.canLoad)}function Ub(e){return e&&Yo(e.canActivate)}function $b(e){return e&&Yo(e.canActivateChild)}function zb(e){return e&&Yo(e.canDeactivate)}function Gb(e){return e&&Yo(e.canMatch)}function Tv(e){return e instanceof He||e?.name==="EmptyError"}var da=Symbol("INITIAL_VALUE");function Rr(){return de(e=>Wn(e.map(n=>n.pipe(vt(1),Ec(da)))).pipe(L(n=>{for(let t of n)if(t!==!0){if(t===da)return da;if(t===!1||Wb(t))return t}return!0}),ce(n=>n!==da),vt(1)))}function Wb(e){return Xt(e)||e instanceof Nr}function qb(e,n){return q(t=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=t;return s.length===0&&i.length===0?_(A(g({},t),{guardsResult:!0})):Zb(s,r,o,e).pipe(q(a=>a&&Bb(a)?Yb(r,i,e,n):_(a)),L(a=>A(g({},t),{guardsResult:a})))})}function Zb(e,n,t,r){return W(e).pipe(q(o=>eS(o.component,o.route,t,n,r)),yt(o=>o!==!0,!0))}function Yb(e,n,t,r){return W(n).pipe(Ft(o=>Zn(Kb(o.route.parent,r),Qb(o.route,r),Xb(e,o.path,t),Jb(e,o.route,t))),yt(o=>o!==!0,!0))}function Qb(e,n){return e!==null&&n&&n(new ba(e)),_(!0)}function Kb(e,n){return e!==null&&n&&n(new wa(e)),_(!0)}function Jb(e,n,t){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return _(!0);let o=r.map(i=>Wr(()=>{let s=xr(n)??t,a=Or(i,s),c=Ub(a)?a.canActivate(n,e):me(s,()=>a(n,e));return Nt(c).pipe(yt())}));return _(o).pipe(Rr())}function Xb(e,n,t){let r=n[n.length-1],i=n.slice(0,n.length-1).reverse().map(s=>Lb(s)).filter(s=>s!==null).map(s=>Wr(()=>{let a=s.guards.map(c=>{let u=xr(s.node)??t,l=Or(c,u),d=$b(l)?l.canActivateChild(r,e):me(u,()=>l(r,e));return Nt(d).pipe(yt())});return _(a).pipe(Rr())}));return _(i).pipe(Rr())}function eS(e,n,t,r,o){let i=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!i||i.length===0)return _(!0);let s=i.map(a=>{let c=xr(n)??o,u=Or(a,c),l=zb(u)?u.canDeactivate(e,n,t,r):me(c,()=>u(e,n,t,r));return Nt(l).pipe(yt())});return _(s).pipe(Rr())}function tS(e,n,t,r){let o=n.canLoad;if(o===void 0||o.length===0)return _(!0);let i=o.map(s=>{let a=Or(s,e),c=Hb(a)?a.canLoad(n,t):me(e,()=>a(n,t));return Nt(c)});return _(i).pipe(Rr(),Av(r))}function Av(e){return fc(te(n=>{if(typeof n!="boolean")throw Na(e,n)}),L(n=>n===!0))}function nS(e,n,t,r){let o=n.canMatch;if(!o||o.length===0)return _(!0);let i=o.map(s=>{let a=Or(s,e),c=Gb(a)?a.canMatch(n,t):me(e,()=>a(n,t));return Nt(c)});return _(i).pipe(Rr(),Av(r))}var Wo=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},qo=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function _r(e){return zn(new Wo(e))}function rS(e){return zn(new y(4e3,!1))}function oS(e){return zn(Sv(!1,Ce.GuardRejected))}var Od=class{urlSerializer;urlTree;constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],o=t.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return _(r);if(o.numberOfChildren>1||!o.children[M])return rS(`${n.redirectTo}`);o=o.children[M]}}applyRedirectCommands(n,t,r,o,i){return iS(t,o,i).pipe(L(s=>{if(s instanceof mt)throw new qo(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),n,r);if(s[0]==="/")throw new qo(a);return a}))}applyRedirectCreateUrlTree(n,t,r,o){let i=this.createSegmentGroup(n,t.root,r,o);return new mt(i,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){let r={};return Object.entries(n).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=t[a]}else r[o]=i}),r}createSegmentGroup(n,t,r,o){let i=this.createSegments(n,t.segments,r,o),s={};return Object.entries(t.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,o)}),new j(i,s)}createSegments(n,t,r,o){return t.map(i=>i.path[0]===":"?this.findPosParam(n,i,o):this.findOrReturn(i,r))}findPosParam(n,t,r){let o=r[t.path.substring(1)];if(!o)throw new y(4001,!1);return o}findOrReturn(n,t){let r=0;for(let o of t){if(o.path===n.path)return t.splice(r),o;r++}return n}};function iS(e,n,t){if(typeof e=="string")return _(e);let r=e,{queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,params:u,data:l,title:d}=n;return Nt(me(t,()=>r({params:u,data:l,queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,title:d})))}var kd={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function sS(e,n,t,r,o){let i=Nv(e,n,t);return i.matched?(r=Tb(n,r),nS(r,n,t,o).pipe(L(s=>s===!0?i:g({},kd)))):_(i)}function Nv(e,n,t){if(n.path==="**")return aS(t);if(n.path==="")return n.pathMatch==="full"&&(e.hasChildren()||t.length>0)?g({},kd):{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};let o=(n.matcher||iv)(t,e,n);if(!o)return g({},kd);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?g(g({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:t.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function aS(e){return{matched:!0,parameters:e.length>0?av(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function nv(e,n,t,r){return t.length>0&&lS(e,t,r)?{segmentGroup:new j(n,uS(r,new j(t,e.children))),slicedSegments:[]}:t.length===0&&dS(e,t,r)?{segmentGroup:new j(e.segments,cS(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new j(e.segments,e.children),slicedSegments:t}}function cS(e,n,t,r){let o={};for(let i of t)if(ka(e,n,i)&&!r[Xe(i)]){let s=new j([],{});o[Xe(i)]=s}return g(g({},r),o)}function uS(e,n){let t={};t[M]=n;for(let r of e)if(r.path===""&&Xe(r)!==M){let o=new j([],{});t[Xe(r)]=o}return t}function lS(e,n,t){return t.some(r=>ka(e,n,r)&&Xe(r)!==M)}function dS(e,n,t){return t.some(r=>ka(e,n,r))}function ka(e,n,t){return(e.hasChildren()||n.length>0)&&t.pathMatch==="full"?!1:t.path===""}function fS(e,n,t){return n.length===0&&!e.children[t]}var Fd=class{};function hS(e,n,t,r,o,i,s="emptyOnly"){return new Pd(e,n,t,r,o,s,i).recognize()}var pS=31,Pd=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,t,r,o,i,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Od(this.urlSerializer,this.urlTree)}noMatchError(n){return new y(4002,`'${n.segmentGroup}'`)}recognize(){let n=nv(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(L(({children:t,rootSnapshot:r})=>{let o=new xe(r,t),i=new zo("",o),s=mv(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(n){let t=new Nn([],Object.freeze({}),Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),M,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,M,t).pipe(L(r=>({children:r,rootSnapshot:t})),tt(r=>{if(r instanceof qo)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Wo?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,t,r,i):this.processSegment(n,t,r,r.segments,o,!0,i).pipe(L(s=>s instanceof xe?[s]:[]))}processChildren(n,t,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return W(i).pipe(Ft(s=>{let a=r.children[s],c=Ab(t,s);return this.processSegmentGroup(n,c,a,s,o)}),Cc((s,a)=>(s.push(...a),s)),Pt(null),Dc(),q(s=>{if(s===null)return _r(r);let a=Rv(s);return gS(a),_(a)}))}processSegment(n,t,r,o,i,s,a){return W(t).pipe(Ft(c=>this.processSegmentAgainstRoute(c._injector??n,t,c,r,o,i,s,a).pipe(tt(u=>{if(u instanceof Wo)return _(null);throw u}))),yt(c=>!!c),tt(c=>{if(Tv(c))return fS(r,o,i)?_(new Fd):_r(r);throw c}))}processSegmentAgainstRoute(n,t,r,o,i,s,a,c){return Xe(r)!==s&&(s===M||!ka(o,i,r))?_r(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,o,t,r,i,s,c):_r(o)}expandSegmentAgainstRouteUsingRedirect(n,t,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=Nv(t,o,i);if(!c)return _r(t);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>pS&&(this.allowRedirects=!1));let f=new Nn(i,u,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,rv(o),Xe(o),o.component??o._loadedComponent??null,o,ov(o)),v=Aa(f,a,this.paramsInheritanceStrategy);return f.params=Object.freeze(v.params),f.data=Object.freeze(v.data),this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,n).pipe(de(T=>this.applyRedirects.lineralizeSegments(o,T)),q(T=>this.processSegment(n,r,t,T.concat(h),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,o,i,s){let a=sS(t,r,o,n,this.urlSerializer);return r.path==="**"&&(t.children={}),a.pipe(de(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,o).pipe(de(({routes:u})=>{let l=r._loadedInjector??n,{parameters:d,consumedSegments:h,remainingSegments:f}=c,v=new Nn(h,d,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,rv(r),Xe(r),r.component??r._loadedComponent??null,r,ov(r)),E=Aa(v,s,this.paramsInheritanceStrategy);v.params=Object.freeze(E.params),v.data=Object.freeze(E.data);let{segmentGroup:T,slicedSegments:R}=nv(t,h,f,u);if(R.length===0&&T.hasChildren())return this.processChildren(l,u,T,v).pipe(L(nn=>new xe(v,nn)));if(u.length===0&&R.length===0)return _(new xe(v,[]));let Fn=Xe(r)===i;return this.processSegment(l,u,T,R,Fn?M:i,!0,v).pipe(L(nn=>new xe(v,nn instanceof xe?[nn]:[])))}))):_r(t)))}getChildConfig(n,t,r){return t.children?_({routes:t.children,injector:n}):t.loadChildren?t._loadedRoutes!==void 0?_({routes:t._loadedRoutes,injector:t._loadedInjector}):tS(n,t,r,this.urlSerializer).pipe(q(o=>o?this.configLoader.loadChildren(n,t).pipe(te(i=>{t._loadedRoutes=i.routes,t._loadedInjector=i.injector})):oS(t))):_({routes:[],injector:n})}};function gS(e){e.sort((n,t)=>n.value.outlet===M?-1:t.value.outlet===M?1:n.value.outlet.localeCompare(t.value.outlet))}function mS(e){let n=e.value.routeConfig;return n&&n.path===""}function Rv(e){let n=[],t=new Set;for(let r of e){if(!mS(r)){n.push(r);continue}let o=n.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),t.add(o)):n.push(r)}for(let r of t){let o=Rv(r.children);n.push(new xe(r.value,o))}return n.filter(r=>!t.has(r))}function rv(e){return e.data||{}}function ov(e){return e.resolve||{}}function vS(e,n,t,r,o,i){return q(s=>hS(e,n,t,r,s.extractedUrl,o,i).pipe(L(({state:a,tree:c})=>A(g({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function yS(e,n){return q(t=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=t;if(!o.length)return _(t);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of xv(c))s.add(u);let a=0;return W(s).pipe(Ft(c=>i.has(c)?DS(c,r,e,n):(c.data=Aa(c,c.parent,e).resolve,_(void 0))),te(()=>a++),Yn(1),q(c=>a===s.size?_(t):Ee))})}function xv(e){let n=e.children.map(t=>xv(t)).flat();return[e,...n]}function DS(e,n,t,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!wv(o)&&(i[Zo]=o.title),Wr(()=>(e.data=Aa(e,e.parent,t).resolve,CS(i,e,n,r).pipe(L(s=>(e._resolvedData=s,e.data=g(g({},e.data),s),null)))))}function CS(e,n,t,r){let o=_d(e);if(o.length===0)return _({});let i={};return W(o).pipe(q(s=>ES(e[s],n,t,r).pipe(yt(),te(a=>{if(a instanceof Nr)throw Na(new xn,a);i[s]=a}))),Yn(1),L(()=>i),tt(s=>Tv(s)?Ee:zn(s)))}function ES(e,n,t,r){let o=xr(n)??r,i=Or(e,o),s=i.resolve?i.resolve(n,t):me(o,()=>i(n,t));return Nt(s)}function Id(e){return de(n=>{let t=e(n);return t?W(t).pipe(L(()=>n)):_(n)})}var Hd=(()=>{class e{buildTitle(t){let r,o=t.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===M);return r}getResolvedTitleForRoute(t){return t.data[Zo]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(Ov),providedIn:"root"})}return e})(),Ov=(()=>{class e extends Hd{title;constructor(t){super(),this.title=t}updateTitle(t){let r=this.buildTitle(t);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(I(Km))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),kr=new C("",{providedIn:"root",factory:()=>({})}),Qo=new C(""),Ud=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(ed);loadComponent(t,r){if(this.componentLoaders.get(r))return this.componentLoaders.get(r);if(r._loadedComponent)return _(r._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(r);let o=Nt(me(t,()=>r.loadComponent())).pipe(L(Fv),te(s=>{this.onLoadEndListener&&this.onLoadEndListener(r),r._loadedComponent=s}),Zr(()=>{this.componentLoaders.delete(r)})),i=new Un(o,()=>new U).pipe(Hn());return this.componentLoaders.set(r,i),i}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return _({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=kv(r,this.compiler,t,this.onLoadEndListener).pipe(Zr(()=>{this.childrenLoaders.delete(r)})),s=new Un(i,()=>new U).pipe(Hn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function kv(e,n,t,r){return Nt(me(t,()=>e.loadChildren())).pipe(L(Fv),q(o=>o instanceof Ys||Array.isArray(o)?_(o):W(n.compileModuleAsync(o))),L(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(t).injector,s=i.get(Qo,[],{optional:!0,self:!0}).flat()),{routes:s.map(Bd),injector:i}}))}function IS(e){return e&&typeof e=="object"&&"default"in e}function Fv(e){return IS(e)?e.default:e}var Fa=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(wS),providedIn:"root"})}return e})(),wS=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Pv=new C("");var Lv=new C(""),Vv=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new U;transitionAbortWithErrorSubject=new U;configLoader=p(Ud);environmentInjector=p(Z);destroyRef=p(ut);urlSerializer=p(On);rootContexts=p(Rt);location=p(Mt);inputBindingEnabled=p(Oa,{optional:!0})!==null;titleStrategy=p(Hd);options=p(kr,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(Fa);createViewTransition=p(Pv,{optional:!0});navigationErrorHandler=p(Lv,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>_(void 0);rootComponentType=null;destroyed=!1;constructor(){let t=o=>this.events.next(new Ea(o)),r=o=>this.events.next(new Ia(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=t,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(t){let r=++this.navigationId;this.transitions?.next(A(g({},t),{extractedUrl:this.urlHandlingStrategy.extract(t.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(t){return this.transitions=new J(null),this.transitions.pipe(ce(r=>r!==null),de(r=>{let o=!1;return _(r).pipe(de(i=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",Ce.SupersededByNewNavigation),Ee;this.currentTransition=r,this.currentNavigation={id:i.id,initialUrl:i.rawUrl,extractedUrl:i.extractedUrl,targetBrowserUrl:typeof i.extras.browserUrl=="string"?this.urlSerializer.parse(i.extras.browserUrl):i.extras.browserUrl,trigger:i.source,extras:i.extras,previousNavigation:this.lastSuccessfulNavigation?A(g({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>i.abortController.abort()};let s=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=i.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!s&&a!=="reload"){let c="";return this.events.next(new At(i.id,this.urlSerializer.serialize(i.rawUrl),c,Bo.IgnoredSameUrlNavigation)),i.resolve(!1),Ee}if(this.urlHandlingStrategy.shouldProcessUrl(i.rawUrl))return _(i).pipe(de(c=>(this.events.next(new Tt(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?Ee:Promise.resolve(c))),vS(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),te(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=A(g({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let u=new Ho(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(i.currentRawUrl)){let{id:c,extractedUrl:u,source:l,restoredState:d,extras:h}=i,f=new Tt(c,this.urlSerializer.serialize(u),l,d);this.events.next(f);let v=Ev(this.rootComponentType).snapshot;return this.currentTransition=r=A(g({},i),{targetSnapshot:v,urlAfterRedirects:u,extras:A(g({},h),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=u,_(r)}else{let c="";return this.events.next(new At(i.id,this.urlSerializer.serialize(i.extractedUrl),c,Bo.IgnoredByUrlHandlingStrategy)),i.resolve(!1),Ee}}),te(i=>{let s=new va(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot);this.events.next(s)}),L(i=>(this.currentTransition=r=A(g({},i),{guards:Pb(i.targetSnapshot,i.currentSnapshot,this.rootContexts)}),r)),qb(this.environmentInjector,i=>this.events.next(i)),te(i=>{if(r.guardsResult=i.guardsResult,i.guardsResult&&typeof i.guardsResult!="boolean")throw Na(this.urlSerializer,i.guardsResult);let s=new ya(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot,!!i.guardsResult);this.events.next(s)}),ce(i=>i.guardsResult?!0:(this.cancelNavigationTransition(i,"",Ce.GuardRejected),!1)),Id(i=>{if(i.guards.canActivateChecks.length!==0)return _(i).pipe(te(s=>{let a=new Da(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),de(s=>{let a=!1;return _(s).pipe(yS(this.paramsInheritanceStrategy,this.environmentInjector),te({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",Ce.NoDataFromResolver)}}))}),te(s=>{let a=new Ca(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),Id(i=>{let s=a=>{let c=[];if(a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent){let u=xr(a)??this.environmentInjector;c.push(this.configLoader.loadComponent(u,a.routeConfig).pipe(te(l=>{a.component=l}),L(()=>{})))}for(let u of a.children)c.push(...s(u));return c};return Wn(s(i.targetSnapshot.root)).pipe(Pt(null),vt(1))}),Id(()=>this.afterPreactivation()),de(()=>{let{currentSnapshot:i,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,i.root,s.root);return a?W(a).pipe(L(()=>r)):_(r)}),L(i=>{let s=Rb(t.routeReuseStrategy,i.targetSnapshot,i.currentRouterState);return this.currentTransition=r=A(g({},i),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,r}),te(()=>{this.events.next(new Uo)}),Fb(this.rootContexts,t.routeReuseStrategy,i=>this.events.next(i),this.inputBindingEnabled),vt(1),Ai(new k(i=>{let s=r.abortController.signal,a=()=>i.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(ce(()=>!o&&!r.targetRouterState),te(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",Ce.Aborted)}))),te({next:i=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new et(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects))),this.titleStrategy?.updateTitle(i.targetRouterState.snapshot),i.resolve(!0)},complete:()=>{o=!0}}),Ai(this.transitionAbortWithErrorSubject.pipe(te(i=>{throw i}))),Zr(()=>{o||this.cancelNavigationTransition(r,"",Ce.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),tt(i=>{if(this.destroyed)return r.resolve(!1),Ee;if(o=!0,Mv(i))this.events.next(new gt(r.id,this.urlSerializer.serialize(r.extractedUrl),i.message,i.cancellationCode)),kb(i)?this.events.next(new Ar(i.url,i.navigationBehaviorOptions)):r.resolve(!1);else{let s=new Tr(r.id,this.urlSerializer.serialize(r.extractedUrl),i,r.targetSnapshot??void 0);try{let a=me(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof Nr){let{message:c,cancellationCode:u}=Na(this.urlSerializer,a);this.events.next(new gt(r.id,this.urlSerializer.serialize(r.extractedUrl),c,u)),this.events.next(new Ar(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),i}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return Ee}))}))}cancelNavigationTransition(t,r,o){let i=new gt(t.id,this.urlSerializer.serialize(t.extractedUrl),r,o);this.events.next(i),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function _S(e){return e!==Lo}var jv=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(bS),providedIn:"root"})}return e})(),xa=class{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}},bS=(()=>{class e extends xa{static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Bv=(()=>{class e{urlSerializer=p(On);options=p(kr,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(Mt);urlHandlingStrategy=p(Fa);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new mt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:t,initialUrl:r,targetBrowserUrl:o}){let i=t!==void 0?this.urlHandlingStrategy.merge(t,r):r,s=o??i;return s instanceof mt?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:t,finalUrl:r,initialUrl:o}){r&&t?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=t):this.rawUrlTree=o}routerState=Ev(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:t}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:()=>p(SS),providedIn:"root"})}return e})(),SS=(()=>{class e extends Bv{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{t(r.url,r.state,"popstate")})})}handleRouterEvent(t,r){t instanceof Tt?this.updateStateMemento():t instanceof At?this.commitTransition(r):t instanceof Ho?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof Uo?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof gt&&t.code!==Ce.SupersededByNewNavigation&&t.code!==Ce.Redirect?this.restoreHistory(r):t instanceof Tr?this.restoreHistory(r,!0):t instanceof et&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(t)||i){let a=this.browserPageId,c=g(g({},s),this.generateNgRouterState(o,a));this.location.replaceState(t,"",c)}else{let a=g(g({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(t,"",a)}}restoreHistory(t,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===t.finalUrl&&i===0&&(this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function $d(e,n){e.events.pipe(ce(t=>t instanceof et||t instanceof gt||t instanceof Tr||t instanceof At),L(t=>t instanceof et||t instanceof At?0:(t instanceof gt?t.code===Ce.Redirect||t.code===Ce.SupersededByNewNavigation:!1)?2:1),ce(t=>t!==2),vt(1)).subscribe(()=>{n()})}var MS={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},TS={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},ke=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(Gl);stateManager=p(Bv);options=p(kr,{optional:!0})||{};pendingTasks=p(It);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(Vv);urlSerializer=p(On);location=p(Mt);urlHandlingStrategy=p(Fa);injector=p(Z);_events=new U;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(jv);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(Qo,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(Oa,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}eventsSubscription=new K;subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof gt&&r.code!==Ce.Redirect&&r.code!==Ce.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof et)this.navigated=!0;else if(r instanceof Ar){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=g({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||_S(o.source)},s);this.scheduleNavigation(a,Lo,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}Mb(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortWithErrorSubject.next(o)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Lo,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r,o)=>{this.navigateToSyncWithBrowser(t,o,r)})}navigateToSyncWithBrowser(t,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=g({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(t);this.scheduleNavigation(a,r,s,i).catch(c=>{this.disposed||this.injector.get(Re)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(Bd),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=g(g({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=vv(h)}catch{(typeof t[0]!="string"||t[0][0]!=="/")&&(t=[]),d=this.currentUrlTree.root}return yv(d,t,l,u??null)}navigateByUrl(t,r={skipLocationChange:!1}){let o=Xt(t)?t:this.parseUrl(t),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Lo,null,r)}navigate(t,r={skipLocationChange:!1}){return AS(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let o;if(r===!0?o=g({},MS):r===!1?o=g({},TS):o=r,Xt(t))return Jm(this.currentUrlTree,t,o);let i=this.parseUrl(t);return Jm(this.currentUrlTree,i,o)}removeEmptyProps(t){return Object.entries(t).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(t,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return $d(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function AS(e){for(let n=0;n<e.length;n++)if(e[n]==null)throw new y(4008,!1)}var Jo=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;reactiveHref=Et(null);get href(){return je(this.reactiveHref)}set href(t){this.reactiveHref.set(t)}target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new U;applicationErrorHandler=p(Re);options=p(kr,{optional:!0});constructor(t,r,o,i,s,a){this.router=t,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a,this.reactiveHref.set(p(new ra("href"),{optional:!0}));let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area"||!!(typeof customElements=="object"&&customElements.get(c)?.observedAttributes?.includes?.("href")),this.isAnchorElement?this.setTabIndexIfNotOnNativeEl("0"):this.subscribeToNavigationEventsIfNecessary()}subscribeToNavigationEventsIfNecessary(){if(this.subscription!==void 0||!this.isAnchorElement)return;let t=this.preserveFragment,r=o=>o==="merge"||o==="preserve";t||=r(this.queryParamsHandling),t||=!this.queryParamsHandling&&!r(this.options?.defaultQueryParamsHandling),t&&(this.subscription=this.router.events.subscribe(o=>{o instanceof et&&this.updateHref()}))}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&(this.updateHref(),this.subscribeToNavigationEventsIfNecessary()),this.onChanges.next(this)}routerLinkInput=null;set routerLink(t){t==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(Xt(t)?this.routerLinkInput=t:this.routerLinkInput=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0"))}onClick(t,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(t!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c)?.catch(u=>{this.applicationErrorHandler(u)}),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.reactiveHref.set(t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t))??"":null)}applyAttributeValue(t,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,t,r):o.removeAttribute(i,t)}get urlTree(){return this.routerLinkInput===null?null:Xt(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(m(ke),m(Se),Yt("tabindex"),m(ht),m(G),m(St))};static \u0275dir=F({type:e,selectors:[["","routerLink",""]],hostVars:2,hostBindings:function(r,o){r&1&&le("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Tn("href",o.reactiveHref(),Cl)("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Mo],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Mo],replaceUrl:[2,"replaceUrl","replaceUrl",Mo],routerLink:"routerLink"},features:[be]})}return e})();var Ko=class{},RS=(()=>{class e{preload(t,r){return r().pipe(tt(()=>_(null)))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Hv=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(t,r,o,i){this.router=t,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(ce(t=>t instanceof et),Ft(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=Cr(i.providers,t,`Route: ${i.path}`));let s=i._injector??t,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return W(o).pipe(qn())}preloadConfig(t,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(t,r):o=_(null);let i=o.pipe(q(s=>s===null?_(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??t,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(t,r);return W([i,s]).pipe(qn())}else return i})}static \u0275fac=function(r){return new(r||e)(I(ke),I(Z),I(Ko),I(Ud))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),xS=new C("");function OS(e,...n){return Xi([{provide:Qo,multi:!0,useValue:e},[],{provide:Se,useFactory:kS,deps:[ke]},{provide:Js,multi:!0,useFactory:PS},n.map(t=>t.\u0275providers)])}function kS(e){return e.routerState.root}function FS(e,n){return{\u0275kind:e,\u0275providers:n}}function PS(){let e=p(re);return n=>{let t=e.get(Qe);if(n!==t.components[0])return;let r=e.get(ke),o=e.get(LS);e.get(VS)===1&&r.initialNavigation(),e.get(Uv,null,{optional:!0})?.setUpPreloading(),e.get(xS,null,{optional:!0})?.init(),r.resetRootComponentType(t.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var LS=new C("",{factory:()=>new U}),VS=new C("",{providedIn:"root",factory:()=>1});var Uv=new C("");function jS(e){return FS(0,[{provide:Uv,useExisting:Hv},{provide:Ko,useExisting:e}])}var Xv=(()=>{class e{_renderer;_elementRef;onChange=t=>{};onTouched=()=>{};constructor(t,r){this._renderer=t,this._elementRef=r}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static \u0275fac=function(r){return new(r||e)(m(ht),m(G))};static \u0275dir=F({type:e})}return e})(),ri=(()=>{class e extends Xv{static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,features:[De]})}return e})(),jr=new C("");var HS={provide:jr,useExisting:we(()=>ey),multi:!0};function US(){let e=Be()?Be().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var $S=new C(""),ey=(()=>{class e extends Xv{_compositionMode;_composing=!1;constructor(t,r,o){super(t,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!US())}writeValue(t){let r=t??"";this.setProperty("value",r)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static \u0275fac=function(r){return new(r||e)(m(ht),m(G),m($S,8))};static \u0275dir=F({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&le("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[Ke([HS]),De]})}return e})();var Ga=new C(""),ty=new C("");function zS(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t<e?{min:{min:e,actual:n.value}}:null}}function GS(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t>e?{max:{max:e,actual:n.value}}:null}}function $v(e){return null}function ny(e){return e!=null}function ry(e){return Qt(e)?W(e):e}function oy(e){let n={};return e.forEach(t=>{n=t!=null?g(g({},n),t):n}),Object.keys(n).length===0?null:n}function iy(e,n){return n.map(t=>t(e))}function WS(e){return!e.validate}function sy(e){return e.map(n=>WS(n)?n:t=>n.validate(t))}function qS(e){if(!e)return null;let n=e.filter(ny);return n.length==0?null:function(t){return oy(iy(t,n))}}function ay(e){return e!=null?qS(sy(e)):null}function ZS(e){if(!e)return null;let n=e.filter(ny);return n.length==0?null:function(t){let r=iy(t,n).map(ry);return vc(r).pipe(L(oy))}}function cy(e){return e!=null?ZS(sy(e)):null}function zv(e,n){return e===null?[n]:Array.isArray(e)?[...e,n]:[e,n]}function uy(e){return e._rawValidators}function ly(e){return e._rawAsyncValidators}function zd(e){return e?Array.isArray(e)?e:[e]:[]}function Va(e,n){return Array.isArray(e)?e.includes(n):e===n}function Gv(e,n){let t=zd(n);return zd(e).forEach(o=>{Va(t,o)||t.push(o)}),t}function Wv(e,n){return zd(n).filter(t=>!Va(e,t))}var ja=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=ay(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=cy(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return this.control?this.control.hasError(n,t):!1}getError(n,t){return this.control?this.control.getError(n,t):null}},Lr=class extends ja{name;get formDirective(){return null}get path(){return null}},en=class extends ja{_parent=null;name=null;valueAccessor=null},Ba=class{_cd;constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},YS={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},X$=A(g({},YS),{"[class.ng-submitted]":"isSubmitted"}),e2=(()=>{class e extends Ba{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(m(en,2))};static \u0275dir=F({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&bo("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[De]})}return e})(),t2=(()=>{class e extends Ba{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(m(Lr,10))};static \u0275dir=F({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&bo("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},standalone:!1,features:[De]})}return e})();var Xo="VALID",Pa="INVALID",Fr="PENDING",ei="DISABLED",tn=class{},Ha=class extends tn{value;source;constructor(n,t){super(),this.value=n,this.source=t}},ti=class extends tn{pristine;source;constructor(n,t){super(),this.pristine=n,this.source=t}},ni=class extends tn{touched;source;constructor(n,t){super(),this.touched=n,this.source=t}},Pr=class extends tn{status;source;constructor(n,t){super(),this.status=n,this.source=t}},Gd=class extends tn{source;constructor(n){super(),this.source=n}},Wd=class extends tn{source;constructor(n){super(),this.source=n}};function Yd(e){return(Wa(e)?e.validators:e)||null}function QS(e){return Array.isArray(e)?ay(e):e||null}function Qd(e,n){return(Wa(n)?n.asyncValidators:e)||null}function KS(e){return Array.isArray(e)?cy(e):e||null}function Wa(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function dy(e,n,t){let r=e.controls;if(!(n?Object.keys(r):r).length)throw new y(1e3,"");if(!r[t])throw new y(1001,"")}function fy(e,n,t){e._forEachChild((r,o)=>{if(t[o]===void 0)throw new y(1002,"")})}var Vr=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(n,t){this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return je(this.statusReactive)}set status(n){je(()=>this.statusReactive.set(n))}_status=wr(()=>this.statusReactive());statusReactive=Et(void 0);get valid(){return this.status===Xo}get invalid(){return this.status===Pa}get pending(){return this.status==Fr}get disabled(){return this.status===ei}get enabled(){return this.status!==ei}errors;get pristine(){return je(this.pristineReactive)}set pristine(n){je(()=>this.pristineReactive.set(n))}_pristine=wr(()=>this.pristineReactive());pristineReactive=Et(!0);get dirty(){return!this.pristine}get touched(){return je(this.touchedReactive)}set touched(n){je(()=>this.touchedReactive.set(n))}_touched=wr(()=>this.touchedReactive());touchedReactive=Et(!1);get untouched(){return!this.touched}_events=new U;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(Gv(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(Gv(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(Wv(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(Wv(n,this._rawAsyncValidators))}hasValidator(n){return Va(this._rawValidators,n)}hasAsyncValidator(n){return Va(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){let t=this.touched===!1;this.touched=!0;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched(A(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new ni(!0,r))}markAllAsDirty(n={}){this.markAsDirty({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsDirty(n))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&n.emitEvent!==!1&&this._events.next(new ni(!1,r))}markAsDirty(n={}){let t=this.pristine===!0;this.pristine=!1;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty(A(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new ti(!1,r))}markAsPristine(n={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&n.emitEvent!==!1&&this._events.next(new ti(!0,r))}markAsPending(n={}){this.status=Fr;let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Pr(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending(A(g({},n),{sourceControl:t}))}disable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=ei,this.errors=null,this._forEachChild(o=>{o.disable(A(g({},n),{onlySelf:!0}))}),this._updateValue();let r=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Ha(this.value,r)),this._events.next(new Pr(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(A(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=Xo,this._forEachChild(r=>{r.enable(A(g({},n),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors(A(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Xo||this.status===Fr)&&this._runAsyncValidator(r,n.emitEvent)}let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Ha(this.value,t)),this._events.next(new Pr(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(A(g({},n),{sourceControl:t}))}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?ei:Xo}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=Fr,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1,shouldHaveEmitted:n!==!1};let r=ry(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=(this._hasOwnPendingAsyncValidator?.emitEvent||this._hasOwnPendingAsyncValidator?.shouldHaveEmitted)??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(n){let t=n;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((r,o)=>r&&r._find(o),this)}getError(n,t){let r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new Pr(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new z,this.statusChanges=new z}_calculateStatus(){return this._allControlsDisabled()?ei:this.errors?Pa:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Fr)?Fr:this._anyControlsHaveStatus(Pa)?Pa:Xo}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),o&&this._events.next(new ti(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new ni(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_onDisabledChange=[];_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){Wa(n)&&n.updateOn!=null&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){let t=this._parent&&this._parent.dirty;return!n&&!!t&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=QS(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=KS(this._rawAsyncValidators)}},Ua=class extends Vr{constructor(n,t,r){super(Yd(t),Qd(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(n,t){return this.controls[n]?this.controls[n]:(this.controls[n]=t,t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange),t)}addControl(n,t,r={}){this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,t={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}setControl(n,t,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],t&&this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,t={}){fy(this,!0,n),Object.keys(n).forEach(r=>{dy(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(Object.keys(n).forEach(r=>{let o=this.controls[r];o&&o.patchValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n={},t={}){this._forEachChild((r,o)=>{r.reset(n?n[o]:null,{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this._reduceChildren({},(n,t,r)=>(n[r]=t.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(t,r)=>r._syncPendingControls()?!0:t);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(t=>{let r=this.controls[t];r&&n(r,t)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(let[t,r]of Object.entries(this.controls))if(this.contains(t)&&n(r))return!0;return!1}_reduceValue(){let n={};return this._reduceChildren(n,(t,r,o)=>((r.enabled||this.disabled)&&(t[o]=r.value),t))}_reduceChildren(n,t){let r=n;return this._forEachChild((o,i)=>{r=t(r,o,i)}),r}_allControlsDisabled(){for(let n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}};var qd=class extends Ua{};var Kd=new C("",{providedIn:"root",factory:()=>qa}),qa="always";function JS(e,n){return[...n.path,e]}function qv(e,n,t=qa){Jd(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||t==="always")&&n.valueAccessor.setDisabledState?.(e.disabled),eM(e,n),nM(e,n),tM(e,n),XS(e,n)}function Zv(e,n,t=!0){let r=()=>{};n.valueAccessor&&(n.valueAccessor.registerOnChange(r),n.valueAccessor.registerOnTouched(r)),za(e,n),e&&(n._invokeOnDestroyCallbacks(),e._registerOnCollectionChange(()=>{}))}function $a(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function XS(e,n){if(n.valueAccessor.setDisabledState){let t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}function Jd(e,n){let t=uy(e);n.validator!==null?e.setValidators(zv(t,n.validator)):typeof t=="function"&&e.setValidators([t]);let r=ly(e);n.asyncValidator!==null?e.setAsyncValidators(zv(r,n.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();$a(n._rawValidators,o),$a(n._rawAsyncValidators,o)}function za(e,n){let t=!1;if(e!==null){if(n.validator!==null){let o=uy(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==n.validator);i.length!==o.length&&(t=!0,e.setValidators(i))}}if(n.asyncValidator!==null){let o=ly(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==n.asyncValidator);i.length!==o.length&&(t=!0,e.setAsyncValidators(i))}}}let r=()=>{};return $a(n._rawValidators,r),$a(n._rawAsyncValidators,r),t}function eM(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&hy(e,n)})}function tM(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&hy(e,n),e.updateOn!=="submit"&&e.markAsTouched()})}function hy(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function nM(e,n){let t=(r,o)=>{n.valueAccessor.writeValue(r),o&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}function rM(e,n){e==null,Jd(e,n)}function oM(e,n){return za(e,n)}function iM(e,n){if(!e.hasOwnProperty("model"))return!1;let t=e.model;return t.isFirstChange()?!0:!Object.is(n,t.currentValue)}function sM(e){return Object.getPrototypeOf(e.constructor)===ri}function aM(e,n){e._syncPendingControls(),n.forEach(t=>{let r=t.control;r.updateOn==="submit"&&r._pendingChange&&(t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function cM(e,n){if(!n)return null;Array.isArray(n);let t,r,o;return n.forEach(i=>{i.constructor===ey?t=i:sM(i)?r=i:o=i}),o||r||t||null}function uM(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function Yv(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function Qv(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var La=class extends Vr{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(n=null,t,r){super(Yd(t),Qd(r,t)),this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Wa(t)&&(t.nonNullable||t.initialValueIsDefault)&&(Qv(n)?this.defaultValue=n.value:this.defaultValue=n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){Yv(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){Yv(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(n){Qv(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}};var lM=e=>e instanceof La;var r2=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=F({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})(),dM={provide:jr,useExisting:we(()=>fM),multi:!0},fM=(()=>{class e extends ri{writeValue(t){let r=t??"";this.setProperty("value",r)}registerOnChange(t){this.onChange=r=>{t(r==""?null:parseFloat(r))}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(r,o){r&1&&le("input",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},standalone:!1,features:[Ke([dM]),De]})}return e})(),hM={provide:jr,useExisting:we(()=>gM),multi:!0};var pM=(()=>{class e{_accessors=[];add(t,r){this._accessors.push([t,r])}remove(t){for(let r=this._accessors.length-1;r>=0;--r)if(this._accessors[r][1]===t){this._accessors.splice(r,1);return}}select(t){this._accessors.forEach(r=>{this._isSameGroup(r,t)&&r[1]!==t&&r[1].fireUncheck(t.value)})}_isSameGroup(t,r){return t[0].control?t[0]._parent===r._control._parent&&t[1].name===r.name:!1}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),gM=(()=>{class e extends ri{_registry;_injector;_state;_control;_fn;setDisabledStateFired=!1;onChange=()=>{};name;formControlName;value;callSetDisabledState=p(Kd,{optional:!0})??qa;constructor(t,r,o,i){super(t,r),this._registry=o,this._injector=i}ngOnInit(){this._control=this._injector.get(en),this._checkName(),this._registry.add(this._control,this)}ngOnDestroy(){this._registry.remove(this)}writeValue(t){this._state=t===this.value,this.setProperty("checked",this._state)}registerOnChange(t){this._fn=t,this.onChange=()=>{t(this.value),this._registry.select(this)}}setDisabledState(t){(this.setDisabledStateFired||t||this.callSetDisabledState==="whenDisabledForLegacyCode")&&this.setProperty("disabled",t),this.setDisabledStateFired=!0}fireUncheck(t){this.writeValue(t)}_checkName(){this.name&&this.formControlName&&(this.name,this.formControlName),!this.name&&this.formControlName&&(this.name=this.formControlName)}static \u0275fac=function(r){return new(r||e)(m(ht),m(G),m(pM),m(re))};static \u0275dir=F({type:e,selectors:[["input","type","radio","formControlName",""],["input","type","radio","formControl",""],["input","type","radio","ngModel",""]],hostBindings:function(r,o){r&1&&le("change",function(){return o.onChange()})("blur",function(){return o.onTouched()})},inputs:{name:"name",formControlName:"formControlName",value:"value"},standalone:!1,features:[Ke([hM]),De]})}return e})();var py=new C("");var mM={provide:Lr,useExisting:we(()=>vM)},vM=(()=>{class e extends Lr{callSetDisabledState;get submitted(){return je(this._submittedReactive)}set submitted(t){this._submittedReactive.set(t)}_submitted=wr(()=>this._submittedReactive());_submittedReactive=Et(!1);_oldForm;_onCollectionChange=()=>this._updateDomValue();directives=[];form=null;ngSubmit=new z;constructor(t,r,o){super(),this.callSetDisabledState=o,this._setValidators(t),this._setAsyncValidators(r)}ngOnChanges(t){t.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(za(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(t){let r=this.form.get(t.path);return qv(r,t,this.callSetDisabledState),r.updateValueAndValidity({emitEvent:!1}),this.directives.push(t),r}getControl(t){return this.form.get(t.path)}removeControl(t){Zv(t.control||null,t,!1),uM(this.directives,t)}addFormGroup(t){this._setUpFormContainer(t)}removeFormGroup(t){this._cleanUpFormContainer(t)}getFormGroup(t){return this.form.get(t.path)}addFormArray(t){this._setUpFormContainer(t)}removeFormArray(t){this._cleanUpFormContainer(t)}getFormArray(t){return this.form.get(t.path)}updateModel(t,r){this.form.get(t.path).setValue(r)}onSubmit(t){return this._submittedReactive.set(!0),aM(this.form,this.directives),this.ngSubmit.emit(t),this.form._events.next(new Gd(this.control)),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0,r={}){this.form.reset(t,r),this._submittedReactive.set(!1),r?.emitEvent!==!1&&this.form._events.next(new Wd(this.form))}_updateDomValue(){this.directives.forEach(t=>{let r=t.control,o=this.form.get(t.path);r!==o&&(Zv(r||null,t),lM(o)&&(qv(o,t,this.callSetDisabledState),t.control=o))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(t){let r=this.form.get(t.path);rM(r,t),r.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(t){if(this.form){let r=this.form.get(t.path);r&&oM(r,t)&&r.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){Jd(this.form,this),this._oldForm&&za(this._oldForm,this)}static \u0275fac=function(r){return new(r||e)(m(Ga,10),m(ty,10),m(Kd,8))};static \u0275dir=F({type:e,selectors:[["","formGroup",""]],hostBindings:function(r,o){r&1&&le("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{form:[0,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[Ke([mM]),De,be]})}return e})();var yM={provide:en,useExisting:we(()=>DM)},DM=(()=>{class e extends en{_ngModelWarningConfig;_added=!1;viewModel;control;name=null;set isDisabled(t){}model;update=new z;static _ngModelWarningSentOnce=!1;_ngModelWarningSent=!1;constructor(t,r,o,i,s){super(),this._ngModelWarningConfig=s,this._parent=t,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=cM(this,i)}ngOnChanges(t){this._added||this._setUpControl(),iM(t,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}get path(){return JS(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_setUpControl(){this.control=this.formDirective.addControl(this),this._added=!0}static \u0275fac=function(r){return new(r||e)(m(Lr,13),m(Ga,10),m(ty,10),m(jr,10),m(py,8))};static \u0275dir=F({type:e,selectors:[["","formControlName",""]],inputs:{name:[0,"formControlName","name"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},standalone:!1,features:[Ke([yM]),De,be]})}return e})();var CM={provide:jr,useExisting:we(()=>my),multi:!0};function gy(e,n){return e==null?`${n}`:(n&&typeof n=="object"&&(n="Object"),`${e}: ${n}`.slice(0,50))}function EM(e){return e.split(":")[0]}var my=(()=>{class e extends ri{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;appRefInjector=p(Qe).injector;appRefDestroyRef=this.appRefInjector.get(ut);destroyRef=p(ut);cdr=p(Je);_queuedWrite=!1;_writeValueAfterRender(){this._queuedWrite||this.appRefDestroyRef.destroyed||(this._queuedWrite=!0,Ks({write:()=>{this.destroyRef.destroyed||(this._queuedWrite=!1,this.writeValue(this.value))}},{injector:this.appRefInjector}))}writeValue(t){this.cdr.markForCheck(),this.value=t;let r=this._getOptionId(t),o=gy(r,t);this.setProperty("value",o)}registerOnChange(t){this.onChange=r=>{this.value=this._getOptionValue(r),t(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(t){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r),t))return r;return null}_getOptionValue(t){let r=EM(t);return this._optionMap.has(r)?this._optionMap.get(r):t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(r,o){r&1&&le("change",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[Ke([CM]),De]})}return e})(),o2=(()=>{class e{_element;_renderer;_select;id;constructor(t,r,o){this._element=t,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption())}set ngValue(t){this._select!=null&&(this._select._optionMap.set(this.id,t),this._setElementValue(gy(this.id,t)),this._select._writeValueAfterRender())}set value(t){this._setElementValue(t),this._select&&this._select._writeValueAfterRender()}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select._writeValueAfterRender())}static \u0275fac=function(r){return new(r||e)(m(G),m(ht),m(my,9))};static \u0275dir=F({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})(),IM={provide:jr,useExisting:we(()=>vy),multi:!0};function Kv(e,n){return e==null?`${n}`:(typeof n=="string"&&(n=`'${n}'`),n&&typeof n=="object"&&(n="Object"),`${e}: ${n}`.slice(0,50))}function wM(e){return e.split(":")[0]}var vy=(()=>{class e extends ri{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;writeValue(t){this.value=t;let r;if(Array.isArray(t)){let o=t.map(i=>this._getOptionId(i));r=(i,s)=>{i._setSelected(o.indexOf(s.toString())>-1)}}else r=(o,i)=>{o._setSelected(!1)};this._optionMap.forEach(r)}registerOnChange(t){this.onChange=r=>{let o=[],i=r.selectedOptions;if(i!==void 0){let s=i;for(let a=0;a<s.length;a++){let c=s[a],u=this._getOptionValue(c.value);o.push(u)}}else{let s=r.options;for(let a=0;a<s.length;a++){let c=s[a];if(c.selected){let u=this._getOptionValue(c.value);o.push(u)}}}this.value=o,t(o)}}_registerOption(t){let r=(this._idCounter++).toString();return this._optionMap.set(r,t),r}_getOptionId(t){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r)._value,t))return r;return null}_getOptionValue(t){let r=wM(t);return this._optionMap.has(r)?this._optionMap.get(r)._value:t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(r,o){r&1&&le("change",function(s){return o.onChange(s.target)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[Ke([IM]),De]})}return e})(),i2=(()=>{class e{_element;_renderer;_select;id;_value;constructor(t,r,o){this._element=t,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption(this))}set ngValue(t){this._select!=null&&(this._value=t,this._setElementValue(Kv(this.id,t)),this._select.writeValue(this._select.value))}set value(t){this._select?(this._value=t,this._setElementValue(Kv(this.id,t)),this._select.writeValue(this._select.value)):this._setElementValue(t)}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}_setSelected(t){this._renderer.setProperty(this._element.nativeElement,"selected",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(r){return new(r||e)(m(G),m(ht),m(vy,9))};static \u0275dir=F({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})();function yy(e){return typeof e=="number"?e:parseFloat(e)}var Dy=(()=>{class e{_validator=$v;_onChange;_enabled;ngOnChanges(t){if(this.inputName in t){let r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):$v,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}static \u0275fac=function(r){return new(r||e)};static \u0275dir=F({type:e,features:[be]})}return e})(),_M={provide:Ga,useExisting:we(()=>bM),multi:!0},bM=(()=>{class e extends Dy{max;inputName="max";normalizeInput=t=>yy(t);createValidator=t=>GS(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&Tn("max",o._enabled?o.max:null)},inputs:{max:"max"},standalone:!1,features:[Ke([_M]),De]})}return e})(),SM={provide:Ga,useExisting:we(()=>MM),multi:!0},MM=(()=>{class e extends Dy{min;inputName="min";normalizeInput=t=>yy(t);createValidator=t=>zS(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&Tn("min",o._enabled?o.min:null)},inputs:{min:"min"},standalone:!1,features:[Ke([SM]),De]})}return e})();var TM=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=_t({type:e});static \u0275inj=rt({})}return e})(),Zd=class extends Vr{constructor(n,t,r){super(Yd(t),Qd(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;at(n){return this.controls[this._adjustIndex(n)]}push(n,t={}){this.controls.push(n),this._registerControl(n),this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}insert(n,t,r={}){this.controls.splice(n,0,t),this._registerControl(t),this.updateValueAndValidity({emitEvent:r.emitEvent})}removeAt(n,t={}){let r=this._adjustIndex(n);r<0&&(r=0),this.controls[r]&&this.controls[r]._registerOnCollectionChange(()=>{}),this.controls.splice(r,1),this.updateValueAndValidity({emitEvent:t.emitEvent})}setControl(n,t,r={}){let o=this._adjustIndex(n);o<0&&(o=0),this.controls[o]&&this.controls[o]._registerOnCollectionChange(()=>{}),this.controls.splice(o,1),t&&(this.controls.splice(o,0,t),this._registerControl(t)),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(n,t={}){fy(this,!1,n),n.forEach((r,o)=>{dy(this,!1,o),this.at(o).setValue(r,{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(n.forEach((r,o)=>{this.at(o)&&this.at(o).patchValue(r,{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n=[],t={}){this._forEachChild((r,o)=>{r.reset(n[o],{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this.controls.map(n=>n.getRawValue())}clear(n={}){this.controls.length<1||(this._forEachChild(t=>t._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:n.emitEvent}))}_adjustIndex(n){return n<0?n+this.length:n}_syncPendingControls(){let n=this.controls.reduce((t,r)=>r._syncPendingControls()?!0:t,!1);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){this.controls.forEach((t,r)=>{n(t,r)})}_updateValue(){this.value=this.controls.filter(n=>n.enabled||this.disabled).map(n=>n.value)}_anyControls(n){return this.controls.some(t=>t.enabled&&n(t))}_setUpControls(){this._forEachChild(n=>this._registerControl(n))}_allControlsDisabled(){for(let n of this.controls)if(n.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(n){n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)}_find(n){return this.at(n)??null}};function Jv(e){return!!e&&(e.asyncValidators!==void 0||e.validators!==void 0||e.updateOn!==void 0)}var s2=(()=>{class e{useNonNullable=!1;get nonNullable(){let t=new e;return t.useNonNullable=!0,t}group(t,r=null){let o=this._reduceControls(t),i={};return Jv(r)?i=r:r!==null&&(i.validators=r.validator,i.asyncValidators=r.asyncValidator),new Ua(o,i)}record(t,r=null){let o=this._reduceControls(t);return new qd(o,r)}control(t,r,o){let i={};return this.useNonNullable?(Jv(r)?i=r:(i.validators=r,i.asyncValidators=o),new La(t,A(g({},i),{nonNullable:!0}))):new La(t,r,o)}array(t,r,o){let i=t.map(s=>this._createControl(s));return new Zd(i,r,o)}_reduceControls(t){let r={};return Object.keys(t).forEach(o=>{r[o]=this._createControl(t[o])}),r}_createControl(t){if(t instanceof La)return t;if(t instanceof Vr)return t;if(Array.isArray(t)){let r=t[0],o=t.length>1?t[1]:null,i=t.length>2?t[2]:null;return this.control(r,o,i)}else return this.control(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var a2=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:py,useValue:t.warnOnNgModelWithFormControl??"always"},{provide:Kd,useValue:t.callSetDisabledState??qa}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=_t({type:e});static \u0275inj=rt({imports:[TM]})}return e})();var l2=(e,n,t,r,o)=>NM(e[1],n[1],t[1],r[1],o).map(i=>AM(e[0],n[0],t[0],r[0],i)),AM=(e,n,t,r,o)=>{let i=3*n*Math.pow(o-1,2),s=-3*t*o+3*t+r*o,a=e*Math.pow(o-1,3);return o*(i+o*s)-a},NM=(e,n,t,r,o)=>(e-=o,n-=o,t-=o,r-=o,xM(r-3*t+3*n-e,3*t-6*n+3*e,3*n-3*e,e).filter(s=>s>=0&&s<=1)),RM=(e,n,t)=>{let r=n*n-4*e*t;return r<0?[]:[(-n+Math.sqrt(r))/(2*e),(-n-Math.sqrt(r))/(2*e)]},xM=(e,n,t,r)=>{if(e===0)return RM(n,t,r);n/=e,t/=e,r/=e;let o=(3*t-n*n)/3,i=(2*n*n*n-9*n*t+27*r)/27;if(o===0)return[Math.pow(-i,.3333333333333333)];if(i===0)return[Math.sqrt(-o),-Math.sqrt(-o)];let s=Math.pow(i/2,2)+Math.pow(o/3,3);if(s===0)return[Math.pow(i/2,.5)-n/3];if(s>0)return[Math.pow(-(i/2)+Math.sqrt(s),.3333333333333333)-Math.pow(i/2+Math.sqrt(s),.3333333333333333)-n/3];let a=Math.sqrt(Math.pow(-(o/3),3)),c=Math.acos(-(i/(2*Math.sqrt(Math.pow(-(o/3),3))))),u=2*Math.pow(a,1/3);return[u*Math.cos(c/3)-n/3,u*Math.cos((c+2*Math.PI)/3)-n/3,u*Math.cos((c+4*Math.PI)/3)-n/3]};var Za=e=>Ey(e),Hr=(e,n)=>(typeof e=="string"&&(n=e,e=void 0),Za(e).includes(n)),Ey=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let n=e.Ionic.platforms;return n==null&&(n=e.Ionic.platforms=OM(e),n.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),n},OM=e=>{let n=Me.get("platform");return Object.keys(Cy).filter(t=>{let r=n?.[t];return typeof r=="function"?r(e):Cy[t](e)})},kM=e=>Ya(e)&&!wy(e),Xd=e=>!!(kn(e,/iPad/i)||kn(e,/Macintosh/i)&&Ya(e)),FM=e=>kn(e,/iPhone/i),PM=e=>kn(e,/iPhone|iPod/i)||Xd(e),Iy=e=>kn(e,/android|sink/i),LM=e=>Iy(e)&&!kn(e,/mobile/i),VM=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return r>390&&r<520&&o>620&&o<800},jM=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return Xd(e)||LM(e)||r>460&&r<820&&o>780&&o<1400},Ya=e=>$M(e,"(any-pointer:coarse)"),BM=e=>!Ya(e),wy=e=>_y(e)||by(e),_y=e=>!!(e.cordova||e.phonegap||e.PhoneGap),by=e=>{let n=e.Capacitor;return!!(n?.isNative||n?.isNativePlatform&&n.isNativePlatform())},HM=e=>kn(e,/electron/i),UM=e=>{var n;return!!(!((n=e.matchMedia)===null||n===void 0)&&n.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},kn=(e,n)=>n.test(e.navigator.userAgent),$M=(e,n)=>{var t;return(t=e.matchMedia)===null||t===void 0?void 0:t.call(e,n).matches},Cy={ipad:Xd,iphone:FM,ios:PM,android:Iy,phablet:VM,tablet:jM,cordova:_y,capacitor:by,electron:HM,pwa:UM,mobile:Ya,mobileweb:kM,desktop:BM,hybrid:wy},Br,ef=e=>e&&yf(e)||Br,zM=(e={})=>{if(typeof window>"u")return;let n=window.document,t=window,r=t.Ionic=t.Ionic||{},o=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Df(t)),{persistConfig:!1}),r.config),Ef(t)),e);Me.reset(o),Me.getBoolean("persistConfig")&&Cf(t,o),Ey(t),r.config=Me,r.mode=Br=Me.get("mode",n.documentElement.getAttribute("mode")||(Hr(t,"ios")?"ios":"md")),Me.set("mode",Br),n.documentElement.setAttribute("mode",Br),n.documentElement.classList.add(Br),Me.getBoolean("_testing")&&Me.set("animated",!1);let i=a=>{var c;return(c=a.tagName)===null||c===void 0?void 0:c.startsWith("ION-")},s=a=>["ios","md"].includes(a);vf(a=>{for(;a;){let c=a.mode||a.getAttribute("mode");if(c){if(s(c))return c;i(a)&&ci('Invalid ionic mode: "'+c+'", expected: "ios" or "md"')}a=a.parentElement}return Br})};var GM=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(t=>t!=null).map(t=>t.trim()).filter(t=>t!==""):[],g2=e=>{let n={};return GM(e).forEach(t=>n[t]=!0),n};var D2=(e,n,t,r,o,i)=>ae(null,null,function*(){var s;if(e)return e.attachViewToDom(n,t,o,r);if(!i&&typeof t!="string"&&!(t instanceof HTMLElement))throw new Error("framework delegate is missing");let a=typeof t=="string"?(s=n.ownerDocument)===null||s===void 0?void 0:s.createElement(t):t;return r&&r.forEach(c=>a.classList.add(c)),o&&Object.assign(a,o),n.appendChild(a),yield new Promise(c=>Ot(a,c)),a}),C2=(e,n)=>{if(n){if(e){let t=n.parentElement;return e.removeViewFromDom(t,n)}n.remove()}return Promise.resolve()},E2=()=>{let e,n;return{attachViewToDom:(c,u,...l)=>ae(null,[c,u,...l],function*(o,i,s={},a=[]){var d,h;e=o;let f;if(i){let E=typeof i=="string"?(d=e.ownerDocument)===null||d===void 0?void 0:d.createElement(i):i;a.forEach(T=>E.classList.add(T)),Object.assign(E,s),e.appendChild(E),f=E,yield new Promise(T=>Ot(E,T))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(f=e.children[0]).classList.contains("ion-delegate-host")){let T=(h=e.ownerDocument)===null||h===void 0?void 0:h.createElement("div");T.classList.add("ion-delegate-host"),a.forEach(R=>T.classList.add(R)),T.append(...e.children),e.appendChild(T),f=T}let v=document.querySelector("ion-app")||document.body;return n=document.createComment("ionic teleport"),e.parentNode.insertBefore(n,e),v.appendChild(e),f??e}),removeViewFromDom:()=>(e&&n&&(n.parentNode.insertBefore(e,n),n.remove()),Promise.resolve())}};var ii='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',Sy=(e,n)=>{let t=e.querySelector(ii);Ny(t,n??e)},My=(e,n)=>{let t=Array.from(e.querySelectorAll(ii)),r=t.length>0?t[t.length-1]:null;Ny(r,n??e)},Ny=(e,n)=>{let t=e,r=e?.shadowRoot;if(r&&(t=r.querySelector(ii)||e),t){let o=t.closest("ion-radio-group");o?o.setFocus():ec(t)}else n.focus()},tf=0,WM=0,Qa=new WeakMap,Ry=e=>({create(t){return YM(e,t)},dismiss(t,r,o){return XM(document,t,r,e,o)},getTop(){return ae(this,null,function*(){return oi(document,e)})}});var qM=Ry("ion-modal");var ZM=Ry("ion-popover");var N2=e=>{typeof document<"u"&&JM(document);let n=tf++;e.overlayIndex=n},R2=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++WM}`),e.id),YM=(e,n)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let t=document.createElement(e);return t.classList.add("overlay-hidden"),Object.assign(t,Object.assign(Object.assign({},n),{hasController:!0})),Oy(document).appendChild(t),new Promise(r=>Ot(t,r))}):Promise.resolve(),QM=e=>e.classList.contains("overlay-hidden"),Ty=(e,n)=>{let t=e,r=e?.shadowRoot;r&&(t=r.querySelector(ii)||e),t?ec(t):n.focus()},KM=(e,n)=>{let t=oi(n,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),r=e.target;if(!t||!r||t.classList.contains(sT))return;let o=()=>{if(t===r)t.lastFocus=void 0;else if(r.tagName==="ION-TOAST")Ty(t.lastFocus,t);else{let s=bf(t);if(!s.contains(r))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(r)||r===s.querySelector("ion-backdrop"))t.lastFocus=r;else{let c=t.lastFocus;Sy(a,t),c===n.activeElement&&My(a,t),t.lastFocus=n.activeElement}}},i=()=>{if(t.contains(r))t.lastFocus=r;else if(r.tagName==="ION-TOAST")Ty(t.lastFocus,t);else{let s=t.lastFocus;Sy(t),s===n.activeElement&&My(t),t.lastFocus=n.activeElement}};t.shadowRoot?i():o()},JM=e=>{tf===0&&(tf=1,e.addEventListener("focus",n=>{KM(n,e)},!0),e.addEventListener("ionBackButton",n=>{let t=oi(e);t?.backdropDismiss&&n.detail.register(Mf,()=>{t.dismiss(void 0,Ay)})}),Sf()||e.addEventListener("keydown",n=>{if(n.key==="Escape"){let t=oi(e);t?.backdropDismiss&&t.dismiss(void 0,Ay)}}))},XM=(e,n,t,r,o)=>{let i=oi(e,r,o);return i?i.dismiss(n,t):Promise.reject("overlay does not exist")},eT=(e,n)=>(n===void 0&&(n="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(n)).filter(t=>t.overlayIndex>0)),Ka=(e,n)=>eT(e,n).filter(t=>!QM(t)),oi=(e,n,t)=>{let r=Ka(e,n);return t===void 0?r[r.length-1]:r.find(o=>o.id===t)},xy=(e=!1)=>{let t=Oy(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");t&&(e?t.setAttribute("aria-hidden","true"):t.removeAttribute("aria-hidden"))},x2=(e,n,t,r,o)=>ae(null,null,function*(){var i,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(xy(!0),document.body.classList.add(sc)),oT(e.el),Fy(e.el),e.presented=!0,e.willPresent.emit(),(i=e.willPresentShorthand)===null||i===void 0||i.emit();let a=ef(e),c=e.enterAnimation?e.enterAnimation:Me.get(n,a==="ios"?t:r);(yield ky(e,c,e.el,o))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&tT(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),tT=e=>ae(null,null,function*(){let n=document.activeElement;if(!n)return;let t=n?.shadowRoot;t&&(n=t.querySelector(ii)||n),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&n.focus()}),O2=(e,n,t,r,o,i,s)=>ae(null,null,function*(){var a,c;if(!e.presented)return!1;let l=(xt!==void 0?Ka(xt):[]).filter(h=>h.tagName!=="ION-TOAST");l.length===1&&l[0].id===e.el.id&&(xy(!1),document.body.classList.remove(sc)),e.presented=!1;try{Fy(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:n,role:t}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:n,role:t});let h=ef(e),f=e.leaveAnimation?e.leaveAnimation:Me.get(r,h==="ios"?o:i);t!==rT&&(yield ky(e,f,e.el,s)),e.didDismiss.emit({data:n,role:t}),(c=e.didDismissShorthand)===null||c===void 0||c.emit({data:n,role:t}),(Qa.get(e)||[]).forEach(E=>E.destroy()),Qa.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(h){If(`[${e.el.tagName.toLowerCase()}] - `,h)}return e.el.remove(),iT(),!0}),Oy=e=>e.querySelector("ion-app")||e.body,ky=(e,n,t,r)=>ae(null,null,function*(){t.classList.remove("overlay-hidden");let o=e.el,i=n(o,r);(!e.animated||!Me.getBoolean("animated",!0))&&i.duration(0),e.keyboardClose&&i.beforeAddWrite(()=>{let a=t.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=Qa.get(e)||[];return Qa.set(e,[...s,i]),yield i.play(),!0}),k2=(e,n)=>{let t,r=new Promise(o=>t=o);return nT(e,n,o=>{t(o.detail)}),r},nT=(e,n,t)=>{let r=o=>{_f(e,n,r),t(o)};wf(e,n,r)};var Ay="backdrop",rT="gesture",F2=39;var P2=()=>{let e,n=()=>{e&&(e(),e=void 0)};return{addClickListener:(r,o)=>{n();let i=o!==void 0?document.getElementById(o):null;if(!i){ci(`[${r.tagName.toLowerCase()}] - A trigger element with the ID "${o}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,r);return}e=((a,c)=>{let u=()=>{c.present()};return a.addEventListener("click",u),()=>{a.removeEventListener("click",u)}})(i,r)},removeClickListener:n}},Fy=e=>{xt!==void 0&&Hr("android")&&e.setAttribute("aria-hidden","true")},oT=e=>{var n;if(xt===void 0)return;let t=Ka(xt);for(let r=t.length-1;r>=0;r--){let o=t[r],i=(n=t[r+1])!==null&&n!==void 0?n:e;(i.hasAttribute("aria-hidden")||i.tagName!=="ION-TOAST")&&o.setAttribute("aria-hidden","true")}},iT=()=>{if(xt===void 0)return;let e=Ka(xt);for(let n=e.length-1;n>=0;n--){let t=e[n];if(t.removeAttribute("aria-hidden"),t.tagName!=="ION-TOAST")break}},sT="ion-disable-focus-trap";var aT=["tabsInner"];var cT=(()=>{class e{doc;_readyPromise;win;backButton=new U;keyboardDidShow=new U;keyboardDidHide=new U;pause=new U;resume=new U;resize=new U;constructor(t,r){this.doc=t,r.run(()=>{this.win=t.defaultView,this.backButton.subscribeWithPriority=function(i,s){return this.subscribe(a=>a.register(i,c=>r.run(()=>s(c))))},Ur(this.pause,t,"pause",r),Ur(this.resume,t,"resume",r),Ur(this.backButton,t,"ionBackButton",r),Ur(this.resize,this.win,"resize",r),Ur(this.keyboardDidShow,this.win,"ionKeyboardDidShow",r),Ur(this.keyboardDidHide,this.win,"ionKeyboardDidHide",r);let o;this._readyPromise=new Promise(i=>{o=i}),this.win?.cordova?t.addEventListener("deviceready",()=>{o("cordova")},{once:!0}):o("dom")})}is(t){return Hr(this.win,t)}platforms(){return Za(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(t){return uT(this.win.location.href,t)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(t){let r=this.win.navigator;return!!(r?.userAgent&&r.userAgent.indexOf(t)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(r){return new(r||e)(I(ee),I($))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),uT=(e,n)=>{n=n.replace(/[[\]\\]/g,"\\$&");let r=new RegExp("[\\?&]"+n+"=([^&#]*)").exec(e);return r?decodeURIComponent(r[1].replace(/\+/g," ")):null},Ur=(e,n,t,r)=>{n&&n.addEventListener(t,o=>{r.run(()=>{let i=o?.detail;e.next(i)})})},ai=(()=>{class e{location;serializer;router;topOutlet;direction=Py;animated=Ly;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(t,r,o,i){this.location=r,this.serializer=o,this.router=i,i&&i.events.subscribe(s=>{if(s instanceof Tt){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),t.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(t,r={}){return this.setDirection("forward",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateBack(t,r={}){return this.setDirection("back",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateRoot(t,r={}){return this.setDirection("root",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}back(t={animated:!0,animationDirection:"back"}){return this.setDirection("back",t.animated,t.animationDirection,t.animation),this.location.back()}pop(){return ae(this,null,function*(){let t=this.topOutlet;for(;t;){if(yield t.pop())return!0;t=t.parentOutlet}return!1})}setDirection(t,r,o,i){this.direction=t,this.animated=lT(t,r,o),this.animationBuilder=i}setTopOutlet(t){this.topOutlet=t}consumeTransition(){let t="root",r,o=this.animationBuilder;return this.direction==="auto"?(t=this.guessDirection,r=this.guessAnimation):(r=this.animated,t=this.direction),this.direction=Py,this.animated=Ly,this.animationBuilder=void 0,{direction:t,animation:r,animationBuilder:o}}navigate(t,r){if(Array.isArray(t))return this.router.navigate(t,r);{let o=this.serializer.parse(t.toString());return r.queryParams!==void 0&&(o.queryParams=g({},r.queryParams)),r.fragment!==void 0&&(o.fragment=r.fragment),this.router.navigateByUrl(o,r)}}static \u0275fac=function(r){return new(r||e)(I(cT),I(Mt),I(On),I(ke,8))};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),lT=(e,n,t)=>{if(n!==!1){if(t!==void 0)return t;if(e==="forward"||e==="back")return e;if(e==="root"&&n===!0)return"forward"}},Py="auto",Ly=void 0,Hy=(()=>{class e{get(t,r){let o=nf();return o?o.get(t,r):null}getBoolean(t,r){let o=nf();return o?o.getBoolean(t,r):!1}getNumber(t,r){let o=nf();return o?o.getNumber(t,r):0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),dT=new C("USERCONFIG"),nf=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},Ja=class{data;constructor(n={}){this.data=n,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(n){return this.data[n]}},fT=(()=>{class e{zone=p($);applicationRef=p(Qe);config=p(dT);create(t,r,o){return new of(t,r,this.applicationRef,this.zone,o,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),of=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(n,t,r,o,i,s){this.environmentInjector=n,this.injector=t,this.applicationRef=r,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(n,t,r,o){return this.zone.run(()=>new Promise(i=>{let s=g({},r);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=n);let a=hT(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,s,o,this.elementReferenceKey,this.enableSignalsSupport);i(a)}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(r=>{let o=this.elRefMap.get(t);if(o){o.destroy(),this.elRefMap.delete(t);let i=this.elEventsMap.get(t);i&&(i(),this.elEventsMap.delete(t))}r()}))}},hT=(e,n,t,r,o,i,s,a,c,u,l,d)=>{let h=re.create({providers:gT(c),parent:t}),f=Am(a,{environmentInjector:n,elementInjector:h}),v=f.instance,E=f.location.nativeElement;if(c)if(l&&v[l]!==void 0&&console.error(`[Ionic Error]: ${l} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${l}" property from ${a.name}.`),d===!0&&f.setInput!==void 0){let R=c,{modal:Fn,popover:nn}=R,pf=mf(R,["modal","popover"]);for(let gf in pf)f.setInput(gf,pf[gf]);Fn!==void 0&&Object.assign(v,{modal:Fn}),nn!==void 0&&Object.assign(v,{popover:nn})}else Object.assign(v,c);if(u)for(let Fn of u)E.classList.add(Fn);let T=Uy(e,v,E);return s.appendChild(E),r.attachView(f.hostView),o.set(E,f),i.set(E,T),E},pT=[tc,nc,rc,oc,ic],Uy=(e,n,t)=>e.run(()=>{let r=pT.filter(o=>typeof n[o]=="function").map(o=>{let i=s=>n[o](s.detail);return t.addEventListener(o,i),()=>t.removeEventListener(o,i)});return()=>r.forEach(o=>o())}),Vy=new C("NavParamsToken"),gT=e=>[{provide:Vy,useValue:e},{provide:Ja,useFactory:mT,deps:[Vy]}],mT=e=>new Ja(e),vT=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)}})})},yT=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},hf=(e,n,t)=>{t.forEach(r=>e[r]=qr(n,r))};function Xa(e){return function(t){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&vT(t,o),i&&yT(t,i),t}}var DT=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],CT=["present","dismiss","onDidDismiss","onWillDismiss"],gz=(()=>{let e=class sf{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),hf(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||sf)(m(Je),m(G),m($))};static \u0275dir=F({type:sf,selectors:[["ion-popover"]],contentQueries:function(r,o,i){if(r&1&&_o(i,Ze,5),r&2){let s;Er(s=Ir())&&(o.template=s.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"},standalone:!1})};return e=Gr([Xa({inputs:DT,methods:CT})],e),e})(),ET=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],IT=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],mz=(()=>{let e=class af{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),hf(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||af)(m(Je),m(G),m($))};static \u0275dir=F({type:af,selectors:[["ion-modal"]],contentQueries:function(r,o,i){if(r&1&&_o(i,Ze,5),r&2){let s;Er(s=Ir())&&(o.template=s.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"},standalone:!1})};return e=Gr([Xa({inputs:ET,methods:IT})],e),e})(),wT=(e,n,t)=>t==="root"?$y(e,n):t==="forward"?_T(e,n):bT(e,n),$y=(e,n)=>(e=e.filter(t=>t.stackId!==n.stackId),e.push(n),e),_T=(e,n)=>(e.indexOf(n)>=0?e=e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):e.push(n),e),bT=(e,n)=>e.indexOf(n)>=0?e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):$y(e,n),cf=(e,n)=>{let t=e.createUrlTree(["."],{relativeTo:n});return e.serializeUrl(t)},zy=(e,n)=>n?e.stackId!==n.stackId:!0,ST=(e,n)=>{if(!e)return;let t=Gy(n);for(let r=0;r<t.length;r++){if(r>=e.length)return t[r];if(t[r]!==e[r])return}},Gy=e=>e.split("/").map(n=>n.trim()).filter(n=>n!==""),Wy=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},uf=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(n,t,r,o,i,s){this.containerEl=t,this.router=r,this.navCtrl=o,this.zone=i,this.location=s,this.tabsPrefix=n!==void 0?Gy(n):void 0}createView(n,t){let r=cf(this.router,t),o=n?.location?.nativeElement,i=Uy(this.zone,n.instance,o);return{id:this.nextId++,stackId:ST(this.tabsPrefix,r),unlistenEvents:i,element:o,ref:n,url:r}}getExistingView(n){let t=cf(this.router,n),r=this.views.find(o=>o.url===t);return r&&r.ref.changeDetectorRef.reattach(),r}setActive(n){let t=this.navCtrl.consumeTransition(),{direction:r,animation:o,animationBuilder:i}=t,s=this.activeView,a=zy(n,s);a&&(r="back",o=void 0);let c=this.views.slice(),u,l=this.router;l.getCurrentNavigation?u=l.getCurrentNavigation():l.navigations?.value&&(u=l.navigations.value),u?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let d=this.views.includes(n),h=this.insertView(n,r);d||n.ref.changeDetectorRef.detectChanges();let f=n.animationBuilder;return i===void 0&&r==="back"&&!a&&f!==void 0&&(i=f),s&&(s.animationBuilder=i),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),n.ref.changeDetectorRef.reattach(),this.transition(n,s,o,this.canGoBack(1),!1,i).then(()=>MT(n,h,c,this.location,this.zone)).then(()=>({enteringView:n,direction:r,animation:o,tabSwitch:a})))))}canGoBack(n,t=this.getActiveStackId()){return this.getStack(t).length>n}pop(n,t=this.getActiveStackId()){return this.zone.run(()=>{let r=this.getStack(t);if(r.length<=n)return Promise.resolve(!1);let o=r[r.length-n-1],i=o.url,s=o.savedData;if(s){let c=s.get("primary");c?.route?._routerState?.snapshot.url&&(i=c.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(i,A(g({},o.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let n=this.activeView;if(n){let t=this.getStack(n.stackId),r=t[t.length-2],o=r.animationBuilder;return this.wait(()=>this.transition(r,n,"back",this.canGoBack(2),!0,o))}return Promise.resolve()}endBackTransition(n){n?(this.skipTransition=!0,this.pop(1)):this.activeView&&qy(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(n){let t=this.getStack(n);return t.length>0?t[t.length-1]:void 0}getRootUrl(n){let t=this.getStack(n);return t.length>0?t[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(Wy),this.activeView=void 0,this.views=[]}getStack(n){return this.views.filter(t=>t.stackId===n)}insertView(n,t){return this.activeView=n,this.views=wT(this.views,n,t),this.views.slice()}transition(n,t,r,o,i,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(t===n)return Promise.resolve(!1);let a=n?n.element:void 0,c=t?t.element:void 0,u=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),u.commit)?u.commit(a,c,{duration:r===void 0?0:void 0,direction:r,showGoBack:o,progressAnimation:i,animationBuilder:s}):Promise.resolve(!1)}wait(n){return ae(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let t=this.runningTask=n();return t.finally(()=>this.runningTask=void 0),t})}},MT=(e,n,t,r,o)=>typeof requestAnimationFrame=="function"?new Promise(i=>{requestAnimationFrame(()=>{qy(e,n,t,r,o),i()})}):Promise.resolve(),qy=(e,n,t,r,o)=>{o.run(()=>t.filter(i=>!n.includes(i)).forEach(Wy)),n.forEach(i=>{let a=r.path().split("?")[0].split("#")[0];if(i!==e&&i.url!==a){let c=i.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),i.ref.changeDetectorRef.detach()}})},TT=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new J(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=M;stackWillChange=new z;stackDidChange=new z;activateEvents=new z;deactivateEvents=new z;parentContexts=p(Rt);location=p(Ve);environmentInjector=p(Z);inputBinder=p(Zy,{optional:!0});supportsBindingToComponentInputs=!0;config=p(Hy);navCtrl=p(ai);set animation(t){this.nativeEl.animation=t}set animated(t){this.nativeEl.animated=t}set swipeGesture(t){this._swipeGesture=t,this.nativeEl.swipeHandler=t?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:r=>this.stackCtrl.endBackTransition(r)}:void 0}constructor(t,r,o,i,s,a,c,u){this.parentOutlet=u,this.nativeEl=i.nativeElement,this.name=t||M,this.tabsPrefix=r==="true"?cf(s,c):void 0,this.stackCtrl=new uf(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,o),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let t=this.getContext();t?.route&&this.activateWith(t.route,t.injector)}new Promise(t=>Ot(this.nativeEl,t)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(t,r){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let r=this.getContext();this.activatedView.savedData=new Map(r.children.contexts);let o=this.activatedView.savedData.get("primary");if(o&&r.route&&(o.route=g({},r.route)),this.activatedView.savedExtras={},r.route){let i=r.route.snapshot;this.activatedView.savedExtras.queryParams=i.queryParams,this.activatedView.savedExtras.fragment=i.fragment}}let t=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;let o,i=this.stackCtrl.getExistingView(t);if(i){o=this.activated=i.ref;let a=i.savedData;if(a){let c=this.getContext();c.children.contexts=a}this.updateActivatedRouteProxy(o.instance,t)}else{let a=t._futureSnapshot,c=this.parentContexts.getOrCreateContext(this.name).children,u=new J(null),l=this.createActivatedRouteProxy(u,t),d=new lf(l,c,this.location.injector),h=a.routeConfig.component??a.component;o=this.activated=this.outletContent.createComponent(h,{index:this.outletContent.length,injector:d,environmentInjector:r??this.environmentInjector}),u.next(o.instance),i=this.stackCtrl.createView(this.activated,t),this.proxyMap.set(o.instance,l),this.currentActivatedRoute$.next({component:o.instance,activatedRoute:t})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=i,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:i,tabSwitch:zy(i,s)}),this.stackCtrl.setActive(i).then(a=>{this.activateEvents.emit(o.instance),this.stackDidChange.emit(a)})}canGoBack(t=1,r){return this.stackCtrl.canGoBack(t,r)}pop(t=1,r){return this.stackCtrl.pop(t,r)}getLastUrl(t){let r=this.stackCtrl.getLastUrl(t);return r?r.url:void 0}getLastRouteView(t){return this.stackCtrl.getLastUrl(t)}getRootView(t){return this.stackCtrl.getRootUrl(t)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(t,r){let o=new Se;return o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,o._paramMap=this.proxyObservable(t,"paramMap"),o._queryParamMap=this.proxyObservable(t,"queryParamMap"),o.url=this.proxyObservable(t,"url"),o.params=this.proxyObservable(t,"params"),o.queryParams=this.proxyObservable(t,"queryParams"),o.fragment=this.proxyObservable(t,"fragment"),o.data=this.proxyObservable(t,"data"),o}proxyObservable(t,r){return t.pipe(ce(o=>!!o),de(o=>this.currentActivatedRoute$.pipe(ce(i=>i!==null&&i.component===o),de(i=>i&&i.activatedRoute[r]),yc())))}updateActivatedRouteProxy(t,r){let o=this.proxyMap.get(t);if(!o)throw new Error("Could not find activated route proxy for view");o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,this.currentActivatedRoute$.next({component:t,activatedRoute:r})}static \u0275fac=function(r){return new(r||e)(Yt("name"),Yt("tabs"),m(Mt),m(G),m(ke),m($),m(Se),m(e,12))};static \u0275dir=F({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1})}return e})(),lf=class{route;childContexts;parent;constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Se?this.route:n===Rt?this.childContexts:this.parent.get(n,t)}},Zy=new C(""),AT=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=Wn([r.queryParams,r.params,r.data]).pipe(de(([i,s,a],c)=>(a=g(g(g({},i),s),a),c===0?_(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=ad(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=D({token:e,factory:e.\u0275fac})}return e})(),vz=()=>({provide:Zy,useFactory:NT,deps:[ke]});function NT(e){return e?.componentInputBindingEnabled?new AT:null}var RT=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],yz=(()=>{let e=class df{routerOutlet;navCtrl;config;r;z;el;constructor(t,r,o,i,s,a){this.routerOutlet=t,this.navCtrl=r,this.config=o,this.r=i,this.z=s,a.detach(),this.el=this.r.nativeElement}onClick(t){let r=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),t.preventDefault()):r!=null&&(this.navCtrl.navigateBack(r,{animation:this.routerAnimation}),t.preventDefault())}static \u0275fac=function(r){return new(r||df)(m(TT,8),m(ai),m(Hy),m(G),m($),m(Je))};static \u0275dir=F({type:df,hostBindings:function(r,o){r&1&&le("click",function(s){return o.onClick(s)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"},standalone:!1})};return e=Gr([Xa({inputs:RT})],e),e})(),Dz=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let t=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],r=this.elementRef.nativeElement;t.includes(r.tagName)&&r.getAttribute("tabindex")==="0"&&r.removeAttribute("tabindex")}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(t){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),t.preventDefault()}static \u0275fac=function(r){return new(r||e)(m(St),m(ai),m(G),m(ke),m(Jo,8))};static \u0275dir=F({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(r,o){r&1&&le("click",function(s){return o.onClick(s)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[be]})}return e})(),Cz=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}static \u0275fac=function(r){return new(r||e)(m(St),m(ai),m(G),m(ke),m(Jo,8))};static \u0275dir=F({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(r,o){r&1&&le("click",function(){return o.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[be]})}return e})(),xT=["animated","animation","root","rootParams","swipeGesture"],OT=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],Ez=(()=>{let e=class ff{z;el;constructor(t,r,o,i,s,a){this.z=s,a.detach(),this.el=t.nativeElement,t.nativeElement.delegate=i.create(r,o),hf(this,this.el,["ionNavDidChange","ionNavWillChange"])}static \u0275fac=function(r){return new(r||ff)(m(G),m(Z),m(re),m(fT),m($),m(Je))};static \u0275dir=F({type:ff,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"},standalone:!1})};return e=Gr([Xa({inputs:xT,methods:OT})],e),e})(),Iz=(()=>{class e{navCtrl;tabsInner;ionTabsWillChange=new z;ionTabsDidChange=new z;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(t){this.navCtrl=t}ngAfterViewInit(){let t=this.tabs.length>0?this.tabs.first:void 0;t&&(this.hasTab=!0,this.setActiveTab(t.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&this.ionTabsWillChange.emit({tab:o})}onStackDidChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=o),this.ionTabsDidChange.emit({tab:o}))}select(t){let r=typeof t=="string",o=r?t:t.detail.tab;if(this.hasTab){this.setActiveTab(o),this.tabSwitch();return}let i=this.outlet.getActiveStackId()===o,s=`${this.outlet.tabsPrefix}/${o}`;if(r||t.stopPropagation(),i){let a=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(a)?.url===s)return;let u=this.outlet.getRootView(o),l=u&&s===u.url&&u.savedExtras;return this.navCtrl.navigateRoot(s,A(g({},l),{animated:!0,animationDirection:"back"}))}else{let a=this.outlet.getLastRouteView(o),c=a?.url||s,u=a?.savedExtras;return this.navCtrl.navigateRoot(c,A(g({},u),{animated:!0,animationDirection:"back"}))}}setActiveTab(t){let o=this.tabs.find(i=>i.tab===t);if(!o){console.error(`[Ionic Error]: Tab with id: "${t}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=o,this.ionTabsWillChange.emit({tab:t}),o.el.active=!0}tabSwitch(){let{selectedTab:t,leavingTab:r}=this;this.tabBar&&t&&(this.tabBar.selectedTab=t.tab),r?.tab!==t?.tab&&r?.el&&(r.el.active=!1),t&&this.ionTabsDidChange.emit({tab:t.tab})}getSelected(){return this.hasTab?this.selectedTab?.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(t=>{let r=t.el.getAttribute("slot");r!==this.tabBarSlot&&(this.tabBarSlot=r,this.relocateTabBar())})}relocateTabBar(){let t=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(t):this.tabsInner.nativeElement.after(t)}static \u0275fac=function(r){return new(r||e)(m(ai))};static \u0275dir=F({type:e,selectors:[["ion-tabs"]],viewQuery:function(r,o){if(r&1&&Jl(aT,7,G),r&2){let i;Er(i=Ir())&&(o.tabsInner=i.first)}},hostBindings:function(r,o){r&1&&le("ionTabButtonClick",function(s){return o.select(s)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"},standalone:!1})}return e})(),kT=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),wz=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(t,r){this.injector=t,this.elementRef=r}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,si(this.elementRef)}handleValueChange(t,r){t===this.elementRef.nativeElement&&(r!==this.lastValue&&(this.lastValue=r,this.onChange(r)),si(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement?(this.onTouched(),si(this.elementRef)):t.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(en)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>si(this.elementRef)));let r=t.control;r&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(i=>{if(typeof r[i]<"u"){let s=r[i].bind(r);r[i]=(...a)=>{s(...a),si(this.elementRef)}}})}static \u0275fac=function(r){return new(r||e)(m(re),m(G))};static \u0275dir=F({type:e,hostBindings:function(r,o){r&1&&le("ionBlur",function(s){return o._handleBlurEvent(s.target)})},standalone:!1})}return e})(),si=e=>{kT(()=>{let n=e.nativeElement,t=n.value!=null&&n.value.toString().length>0,r=FT(n);rf(n,r);let o=n.closest("ion-item");o&&(t?rf(o,[...r,"item-has-value"]):rf(o,r))})},FT=e=>{let n=e.classList,t=[];for(let r=0;r<n.length;r++){let o=n.item(r);o!==null&&PT(o,"ng-")&&t.push(`ion-${o.substring(3)}`)}return t},rf=(e,n)=>{let t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},PT=(e,n)=>e.substring(0,n.length)===n,jy=class{shouldDetach(n){return!1}shouldAttach(n){return!1}store(n,t){}retrieve(n){return null}shouldReuseRoute(n,t){if(n.routeConfig!==t.routeConfig)return!1;let r=n.params,o=t.params,i=Object.keys(r),s=Object.keys(o);if(i.length!==s.length)return!1;for(let a of i)if(o[a]!==r[a])return!1;return!0}},By=class{ctrl;constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,r){return this.ctrl.dismiss(n,t,r)}getTop(){return this.ctrl.getTop()}};export{Gr as a,W as b,zn as c,Yy as d,L as e,qr as f,tt as g,we as h,D as i,rt as j,p as k,Xi as l,Z as m,jh as n,Bh as o,re as p,ee as q,Ye as r,Yt as s,G as t,ag as u,vE as v,m as w,Ve as x,Hl as y,_t as z,F as A,De as B,em as C,$ as D,sm as E,Tn as F,um as G,Yl as H,Ql as I,Xs as J,Kl as K,ea as L,lm as M,dm as N,Iw as O,ww as P,le as Q,Sw as R,Tw as S,Aw as T,_o as U,Jl as V,Er as W,Ir as X,qw as Y,pm as Z,Xl as _,gm as $,Ke as aa,Je as ba,Mt as ca,jm as da,F_ as ea,P_ as fa,Bm as ga,Y_ as ha,Se as ia,jv as ja,ke as ka,RS as la,OS as ma,jS as na,l2 as oa,Hr as pa,ef as qa,zM as ra,g2 as sa,D2 as ta,C2 as ua,E2 as va,Sy as wa,qM as xa,ZM as ya,N2 as za,R2 as Aa,x2 as Ba,O2 as Ca,k2 as Da,Ay as Ea,rT as Fa,F2 as Ga,P2 as Ha,sT as Ia,jr as Ja,ey as Ka,Ga as La,e2 as Ma,t2 as Na,r2 as Oa,fM as Pa,gM as Qa,vM as Ra,DM as Sa,my as Ta,o2 as Ua,i2 as Va,bM as Wa,MM as Xa,s2 as Ya,a2 as Za,ai as _a,Hy as $a,dT as ab,fT as bb,Xa as cb,gz as db,mz as eb,TT as fb,vz as gb,yz as hb,Dz as ib,Cz as jb,Ez as kb,Iz as lb,kT as mb,wz as nb,si as ob,jy as pb,By as qb};
