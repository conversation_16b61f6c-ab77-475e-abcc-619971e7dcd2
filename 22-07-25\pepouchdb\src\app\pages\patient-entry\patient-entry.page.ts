import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { IonicModule, ToastController } from '@ionic/angular';
import {
  FormBuilder,
  FormGroup,
  FormArray,
  ReactiveFormsModule,
  FormsModule,
} from '@angular/forms';
import { IonRadioGroup } from '@ionic/angular/standalone';
import { mPatientData } from './patient-entry';
import { PouchdbService } from 'src/app/services/pouchdb.service';
import { addIcons } from 'ionicons';
import { createOutline, trashOutline } from 'ionicons/icons';
import { lastValueFrom } from 'rxjs';

// Interface for uploaded documents
interface UploadedDoc {
  preview: string;
  type: string;
  date: Date;
  time: string;
}

@Component({
  selector: 'app-patient-entry',
  templateUrl: './patient-entry.page.html',
  styleUrls: ['./patient-entry.page.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    IonicModule,
    CommonModule,
    HttpClientModule,
    // IonRadioGroup,
  ],
})

export class PatientEntryPage implements OnInit {
  patientForm!: FormGroup;
  activeSection: string = 'section0';
  videoDevices: MediaDeviceInfo[] = [];
  selectedCameraId: string = '';
  submittedPatients: mPatientData[] = [];

  mediaStream: any;
  photoPreviewUrl: string | null = null;
  editIndex: number | null = null;

  // Properties not in use so commented
  // photoPopupOpen: boolean = false;
  // selectedPhoto: string | null = null;

  // docPopupOpen: boolean = false;
  // selectedDoc: any = null;
  // hoverIndex: number | null = null;

  // showUploadControls: boolean = false;
  // isCaptureMode: boolean = false;

  countryList: any[] = [];
  stateList: any[] = [];
  districtList: string[] = [];
  masterData: any = {};


  currentStep = 1
  uploadedDocs: UploadedDoc[] = [];
  capturing = false;


  // process bar 

  nextStep() {
    if (this.currentStep < 4) this.currentStep++;
     if (this.currentStep === 3) {
      this.startCamera(this.selectedCameraId);
    }
  }

  prevStep() {
    if (this.currentStep > 1) this.currentStep--;
    if (this.currentStep === 3) {
      this.startCamera(this.selectedCameraId);
    } else {
      this.stopCamera();
    }
  }

  // doc upload 

  onDragOver(event: DragEvent) {
    event.preventDefault()
  }

  onFileDrop(event: DragEvent) {
    event.preventDefault()
    if (event.dataTransfer?.files) {
      // Handle file drop - for now just log
      console.log('Files dropped:', event.dataTransfer.files);
    }
  }

  
private addFiles(files: FileList) {
    Array.from(files).forEach(file => {
      const reader = new FileReader()
      reader.onload = () => {
        const now = new Date()
        this.uploadedDocs.push({
          preview: reader.result as string,
          type: '',
          date: now,
          time: now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        })

        // Also add to documents FormArray for form submission
        this.documents.push(
          this.fb.group({
            fileName: file.name,
            fileType: file.type,
            data: reader.result,
            type: this.patientForm.value.imageType || 'Document',
          })
        );
      }
      reader.readAsDataURL(file)
    })
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement
    if (input.files) this.addFiles(input.files)
  }

  editDoc(index: number) {
    this.uploadedDocs[index].type = 'Image'
  }

  deleteDoc(index: number) {
    this.uploadedDocs.splice(index, 1)
    // Also remove from documents FormArray if needed
    if (index < this.documents.length) {
      this.documents.removeAt(index);
    }
  }

  startCapture() {
    // This function is used in HTML template for capture image functionality
    console.log('Start capture clicked');
  }

 // Function not in use so commented
  retake() {
    this.capturing = true
  }

  // Function not in use so commented
  // save() {
  //   this.capturing = false
  // }

 

  // stateList: any[] = [];
  // districtList: string[] = [];

  @ViewChild('video', { static: false }) video!: ElementRef;
  @ViewChild('canvas', { static: false }) canvas!: ElementRef;
  @ViewChild('uploadVideo', { static: false }) uploadVideo!: ElementRef;

  constructor(
    public objPouchdbService: PouchdbService,
    private fb: FormBuilder,
    private toastController: ToastController,
    private http: HttpClient
  ) {
    addIcons({
      'create-outline': createOutline,
      'trash-outline': trashOutline,
    });
  }


  async ngOnInit() {
    // this.patientForm =this.fb.group(new mPatientData());
    this.patientForm = this.fb.group({
      _id: [''],
      _rev: [null],
      domainwisepid: [0],
      patientid: [0],
      first_name: [''],
      last_name: [''],
      date_of_birth: [''],
      age: [''],
      ageYears: [''],
      gender: [''],
      maritalstatus: [''],
      height: [''],
      weight: [''],
      mobile: [''],
      email: [''],
      head_of_household_fname: [''],
      head_of_household_lname: [''],
      country: [''],
      state: [''],
      district: [''],
      block: [''],
      village: [''],
      address: [''],
      projid: [''],
      head_of_household_mobile: [''],
      isAbhaPatient: [false],
      profile: this.fb.group({
        patientid: [0],
        imagepath: [''],
        S3URL: [''],
      }),
      pastrecord: [null],
      createdat: [''],
      createdby: [''],
      domain: [0],
      uid: [''],
      prefix: [null],
      EhealthId: [''],
      MRN: [''],
      password: [''],
      consentformcheckstatus: [0],
      fingerPrintTemplate: [''],
      health_number: [''],
      health_address: [''],
      unique_id: [null],
      nationalId: [null],
      ethnicity: [null],
      subscriptionDetails: this.fb.group({
        subscribedId: [0],
        familycardid: [null],
        freeSubcriptionAllocated: [0],
        completedFreeSubcrition: [0],
        remainingSubcription: [0],
        isActive: [null],
        subcriptionName: [null],
        subscriptionPlanActivatedOn: [null],
        subscriptionExpiredOn: [null],
        isExpaired: [0],
      }),
      localId: [''],
      patient_status: [null],
      patient_title: [null],
      postCode: [null],
      centerName: [null],
      status: [null],
      isSync: [false],
      imageType: ['select'],
      documents: this.fb.array([]), // FormArray for uploaded/captured images
      patientImage: [''],
      type: ['patient'],
      
    });
    

    // Load all patients on init
    await this.loadPatients();

    // Load master data on init
    await this.loadMasterData();

    // Enumerate video input devices (cameras)
    if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        this.videoDevices = devices.filter(
          (device) => device.kind === 'videoinput'
        );
        if (this.videoDevices.length > 0) {
          this.selectedCameraId = this.videoDevices[0].deviceId;
          // Removed automatic startCamera call to delay camera access request until user clicks start
          // this.startCamera(this.selectedCameraId);
        }
      } catch (error) {
        console.error('Error enumerating devices:', error);
      }
    } else {
      console.warn('Media Devices API not supported.');
    }
  }

  /**  Load Master Data JSON into PouchDB (if not already saved) */
  private async loadMasterData() {
    try {
      this.objPouchdbService.getMasterData().subscribe({
        next: (data) => {
          console.log(' Master Data already exists in PouchDB');
          this.processMasterData(data);
        },
        error: () => {
          console.log('⚠ Master Data not found → Loading from assets...');
          this.http
            .get('assets/RemediNovaAPI.json')
            .subscribe((jsonData: any) => {
              this.objPouchdbService
                .addOrUpdateMasterData(jsonData)
                .subscribe(() => {
                  console.log(' Master Data saved in PouchDB');
                  this.processMasterData(jsonData);
                });
            });
        },
      });
    } catch (err) {
      console.error(' Error loading master data:', err);
    }
  }

  /**  Process and extract master data from the JSON structure */
  private processMasterData(data: any) {
    try {
      console.log(' Processing master data:', data);

      // Extract tables from the nested data structure
      if (data.data && Array.isArray(data.data)) {
        console.log(' Found data array with', data.data.length, 'items');

        // Find each table in the data array
        data.data.forEach((item: any, index: number) => {
          if (item.tblcountry) {
            this.masterData.tblcountry = item.tblcountry;
            console.log(
              ' Found tblcountry at index',
              index,
              'with',
              item.tblcountry.length,
              'countries'
            );
          }
          if (item.tblstate) {
            this.masterData.tblstate = item.tblstate;
            console.log(
              ' Found tblstate at index',
              index,
              'with',
              item.tblstate.length,
              'states'
            );
          }
          if (item.tbldistrict) {
            this.masterData.tbldistrict = item.tbldistrict;
            console.log(
              ' Found tbldistrict at index',
              index,
              'with',
              item.tbldistrict.length,
              'districts'
            );
          }
        });
      } else {
        // If data is already processed and stored directly
        console.log(' Using direct data structure');
        this.masterData = data;
      }

      // Set country list for dropdown (filter out deleted countries)
      if (this.masterData.tblcountry) {
        this.countryList = this.masterData.tblcountry.filter(
          (country: any) => country.IsDeleted === '0'
        );
        console.log(' Countries loaded:', this.countryList.length);
        console.log(' Sample countries:', this.countryList.slice(0, 3));

        // Temporary alert to verify data loading
        if (this.countryList.length > 0) {
          console.log(' SUCCESS: Countries dropdown should now work!');
        }
      } else {
        console.log(' No tblcountry found in master data');
        console.log(
          ' Available keys in masterData:',
          Object.keys(this.masterData)
        );
      }

      console.log(' Master Data processed successfully');
      console.log(' Master data structure:', Object.keys(this.masterData));
    } catch (err) {
      console.error(' Error processing master data:', err);
    }
  }

  // Load all patients from PouchDB and update submittedPatients array
  // async loadPatients() {
  //   try {
  //     this.objPouchdbService.getAllPatients().subscribe(patients => {
  //       this.submittedPatients = patients;
  //     });
  //   } catch (error) {
  //     console.error('Failed to load patients:', error);
  //   }
  // }
  async loadPatients() {
    try {
      this.objPouchdbService
        .getRecordsByType<mPatientData>('patient')
        .subscribe({
          next: (patients) => (this.submittedPatients = patients),
          error: (err) => console.error('Failed to load patients:', err),
        });
    } catch (error) {
      console.error('Failed to load patients:', error);
    }
  }

  // FormArray for uploaded images
  get documents(): FormArray {
    return this.patientForm.get('documents') as FormArray;
  }

  toggleSection(section: string) {
    this.activeSection = this.activeSection === section ? '' : section;
  }

  onCountryChange(event: any) {
    const selectedCountryId = event.target.value;
    this.patientForm.patchValue({ state: '', district: '' });

    //  Use already loaded masterData
    if (this.masterData.tblstate) {
      this.stateList = this.masterData.tblstate.filter(
        (s: any) => s.CountryId === selectedCountryId && s.IsDeleted === '0'
      );
      console.log(
        ' States loaded for country:',
        selectedCountryId,
        this.stateList.length
      );
    } else {
      this.stateList = [];
      console.log(' No states data available');
    }

    this.districtList = [];
  }

  onStateChange(event: any) {
    const selectedStateId = event.target.value;
    this.patientForm.patchValue({ district: '' });

    //  Use already loaded masterData
    if (this.masterData.tbldistrict) {
      this.districtList = this.masterData.tbldistrict
        .filter(
          (d: any) => d.StateId === selectedStateId && d.IsDeleted === '0'
        )
        .map((d: any) => d.District);
      console.log(
        ' Districts loaded for state:',
        selectedStateId,
        this.districtList.length
      );
    } else {
      this.districtList = [];
      console.log(' No districts data available');
    }
  }

  async startCamera(deviceId?: string) {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      alert('Camera API not supported');
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: deviceId ? { deviceId: { exact: deviceId } } : true,
      });

      this.mediaStream = stream; // Assign stream to mediaStream property

      this.video.nativeElement.srcObject = stream;
      await this.video.nativeElement.play();
    } catch (err) {
      console.error('Error starting camera:', err);
    }
  }
  switchCamera(event: any) {
    this.selectedCameraId = event.target.value;
    if (this.selectedCameraId) {
      this.startCamera(this.selectedCameraId);
    }
  }

  // Function not in use so commented
  // switchUploadCamera(event: any) {
  //   this.selectedCameraId = event.target.value;
  //   if (this.selectedCameraId) {
  //     this.startUploadCamera(this.selectedCameraId);
  //   }
  // }

  // Function not in use so commented
  // async startUploadCamera(deviceId?: string) {
  //   if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
  //     alert('Camera API not supported');
  //     return;
  //   }

  //   try {
  //     const stream = await navigator.mediaDevices.getUserMedia({
  //       video: deviceId ? { deviceId: { exact: deviceId } } : true,
  //     });

  //     this.mediaStream = stream; // Assign stream to mediaStream property

  //     if (this.uploadVideo && this.uploadVideo.nativeElement) {
  //       this.uploadVideo.nativeElement.srcObject = stream;
  //       await this.uploadVideo.nativeElement.play();
  //     }
  //   } catch (err) {
  //     console.error('Error starting upload camera:', err);
  //   }
  // }

  captureImage() {
    const videoEl = this.video.nativeElement;
    const canvasEl = this.canvas.nativeElement;

    // Dynamically set canvas dimensions to match the video stream
    canvasEl.width = videoEl.videoWidth;
    canvasEl.height = videoEl.videoHeight;

    const context = canvasEl.getContext('2d');
    if (!context) {
      console.error('Failed to get canvas context.');
      return;
    }

    // Draw the current video frame onto the canvas
    context.drawImage(videoEl, 0, 0, canvasEl.width, canvasEl.height);

    // Convert the canvas to a base64 PNG image
    const imageData = canvasEl.toDataURL('image/png');

    // Push the image to the documents FormArray
    this.documents.push(
      this.fb.group({
        fileName: `Capture-${Date.now()}.png`,
        fileType: 'image/png',
        data: imageData,
        type: this.patientForm.value.imageType || 'Front',
      })
    );

    // Update the form and preview
    this.patientForm.patchValue({ patientImage: imageData });
    this.photoPreviewUrl = imageData;

    // Stop the camera after capturing
    this.stopCamera();

    // Clear the video element's srcObject to release the camera
    if (this.video && this.video.nativeElement) {
      this.video.nativeElement.srcObject = null;
    }
  }

  stopCamera() {
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach((track: any) => track.stop());
      this.mediaStream = null;
    }
  }



  removeDocument(index: number) {
    this.documents.removeAt(index);
  }

  // patient CRUD operations ------------------------------------
  async savePatients() {
    try {
      this.patientForm.patchValue({
        patientImage: this.photoPreviewUrl,
        documents: this.documents.value,
      });

      const patientData: mPatientData = this.patientForm.value;
      patientData.type = 'patient'; // ✅ Ensure type is always patient

      if (this.editIndex === null) {
        if (!patientData._rev) delete patientData._rev;
        if (!patientData._id || patientData._id.trim() === '')
          delete patientData._id;

        await lastValueFrom(this.objPouchdbService.addRecord(patientData)); // ✅ changed
        await this.loadPatients();

        const toast = await this.toastController.create({
          message: `Patient saved successfully. Total entries: ${this.submittedPatients.length}`,
          duration: 3000,
          color: 'success',
        });
        await toast.present();
      } else {
        if (!patientData._id) throw new Error('Invalid _id for update');

        const latest = await lastValueFrom(
          this.objPouchdbService.getRecordById<mPatientData>(patientData._id) // ✅ changed
        );
        patientData._rev = latest._rev;

        await lastValueFrom(this.objPouchdbService.updateRecord(patientData)); // ✅ changed
        this.editIndex = null;
        await this.loadPatients();

        const toast = await this.toastController.create({
          message: `Patient edited successfully. Total entries: ${this.submittedPatients.length}`,
          duration: 3000,
          color: 'success',
        });
        await toast.present();
      }

      this.patientForm.reset();
      this.photoPreviewUrl = null;
    } catch (error) {
      console.error('Failed to save patient:', error);
      const toast = await this.toastController.create({
        message: 'Failed to save patient.',
        duration: 3000,
        color: 'danger',
      });
      await toast.present();
    }
  }

  // async savePatients() {
  //   try {
  //     this.patientForm.patchValue({
  //       patientImage: this.photoPreviewUrl,
  //       documents: this.documents.value,
  //     });

  //     const patientData: mPatientData = this.patientForm.value;
  //     console.log('Saving patient data:', patientData);

  //     if (this.editIndex === null) {
  //       //  Fix for new entry
  //       if (!patientData._rev) {
  //         delete patientData._rev;
  //       }
  //       if (!patientData._id || patientData._id.trim() === '') {
  //         delete patientData._id;
  //       }

  //       await this.objPouchdbService.addPatient(patientData);
  //       await this.loadPatients();
  //       const toast = await this.toastController.create({
  //         message: `Patient saved successfully. Total entries: ${this.submittedPatients.length}`,
  //         duration: 3000,
  //         color: 'success',
  //       });
  //       await toast.present();
  //     } else {
  //       //  Update existing patient
  //       if (!patientData._id || patientData._id.trim() === '') {
  //         throw new Error('Invalid patient _id for update');
  //       }

  //       const latestPatient = await lastValueFrom(this.objPouchdbService.getPatientById(patientData._id));
  //       patientData._rev = latestPatient._rev;

  //       await this.objPouchdbService.updatePatient(patientData);
  //       this.editIndex = null;
  //       await this.loadPatients();
  //       const toast = await this.toastController.create({
  //         message: `Patient edited successfully. Total entries: ${this.submittedPatients.length}`,
  //         duration: 3000,
  //         color: 'success',
  //       });
  //       await toast.present();
  //     }

  //     console.log(`Total patients in DB: ${this.submittedPatients.length}`);

  //     //  Reset form completely
  //     this.patientForm.reset();
  //     this.photoPreviewUrl = null;

  //   } catch (error) {
  //     console.error('Failed to save patient:', error);
  //     const toast = await this.toastController.create({
  //       message: 'Failed to save patient.',
  //       duration: 3000,
  //       color: 'danger',
  //     });
  //     await toast.present();
  //   }
  // }

  // Load patient data into form for editing
  editPatients(patient: mPatientData, index: number) {
    this.editIndex = index;
    this.patientForm.patchValue({
      ...patient,
      _id: patient._id || '',
      _rev: patient._rev || '',
    });
    // Use profile.imagepath for photo preview if available
    this.photoPreviewUrl = patient.profile?.imagepath || null;
  }

  // Delete patient and refresh list
  // async delete(patient: mPatientData) {
  //   try {
  //     await this.objPouchdbService.deletePatient(patient);
  //     await this.loadPatients();
  //     const toast = await this.toastController.create({
  //       message: `Patient deleted successfully. Total entries: ${this.submittedPatients.length}`,
  //       duration: 3000,
  //       color: 'success',
  //     });
  //     await toast.present();
  //   } catch (error) {
  //     console.error('Failed to delete patient:', error);
  //     const toast = await this.toastController.create({
  //       message: 'Failed to delete patient.',
  //       duration: 3000,
  //       color: 'danger',
  //     });
  //     await toast.present();
  //   }
  // }

  async delete(patient: mPatientData) {
    try {
      await lastValueFrom(this.objPouchdbService.deleteRecord(patient)); // ✅ changed
      await this.loadPatients();

      const toast = await this.toastController.create({
        message: `Patient deleted successfully. Total entries: ${this.submittedPatients.length}`,
        duration: 3000,
        color: 'success',
      });
      await toast.present();
    } catch (error) {
      console.error('Failed to delete patient:', error);
      const toast = await this.toastController.create({
        message: 'Failed to delete patient.',
        duration: 3000,
        color: 'danger',
      });
      await toast.present();
    }
  }

    // Function not in use so commented
  // openPhotoPopup(photoUrl: string) {
  //   this.selectedPhoto = photoUrl;
  //   this.photoPopupOpen = true;
  // }

  // Function not in use so commented
  // closePhotoPopup() {
  //   this.photoPopupOpen = false;
  //   this.selectedPhoto = null;
  // }

  // Function not in use so commented
  // openDocPopup(doc: any) {
  //   this.selectedDoc = doc;
  //   this.docPopupOpen = true;
  // }

  // Function not in use so commented
  // closeDocPopup() {
  //   this.docPopupOpen = false;
  //   this.selectedDoc = null;
  // }

  // Function not in use so commented
  // downloadFullPatientRecord(data: any) {
  //   const jsonData = JSON.stringify(data, null, 2);
  //   const blob = new Blob([jsonData], { type: 'application/json' });
  //   const url = URL.createObjectURL(blob);
  //   const a = document.createElement('a');
  //   const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  //   a.href = url;
  //   a.download = `patient-${timestamp}.json`;
  //   document.body.appendChild(a);
  //   a.click();
  //   document.body.removeChild(a);

  //   console.log(`⬇ Patient record downloaded: patient-${timestamp}.json`);
  // }

  // Function not in use so commented
  // onBrowseClick() {
  //   this.showUploadControls = true;
  //   this.isCaptureMode = false;
  // }

  // Function not in use so commented
  // async onCaptureClick() {
  //   this.showUploadControls = true;
  //   this.isCaptureMode = true;
  //   try {
  //     const stream = await navigator.mediaDevices.getUserMedia({ video: true });
  //     this.mediaStream = stream;
  //     if (this.uploadVideo && this.uploadVideo.nativeElement) {
  //       this.uploadVideo.nativeElement.srcObject = stream;
  //       await this.uploadVideo.nativeElement.play();
  //     }
  //   } catch (err) {
  //     console.error('Error starting upload capture camera:', err);
  //   }
  // }

  // Function not in use so commented
  // captureUploadImage() {
  //   if (!this.uploadVideo || !this.canvas || !this.canvas.nativeElement) {
  //     console.error('Upload video or canvas element not found.');
  //     return;
  //   }
  //   const videoEl = this.uploadVideo.nativeElement;
  //   const canvasEl = this.canvas.nativeElement;

  //   canvasEl.width = videoEl.videoWidth;
  //   canvasEl.height = videoEl.videoHeight;

  //   const context = canvasEl.getContext('2d');
  //   if (!context) {
  //     console.error('Failed to get canvas context.');
  //     return;
  //   }

  //   context.drawImage(videoEl, 0, 0, canvasEl.width, canvasEl.height);

  //   const imageData = canvasEl.toDataURL('image/png');

  //   this.documents.push(
  //     this.fb.group({
  //       fileName: `UploadCapture-${Date.now()}.png`,
  //       fileType: 'image/png',
  //       data: imageData,
  //       type: this.patientForm.value.imageType || 'select',
  //     })
  //   );

  //   this.patientForm.patchValue({ patientImage: imageData });
  //   this.photoPreviewUrl = imageData;

  //   this.stopUploadCamera();
  // }

  // Function not in use so commented
  // stopUploadCamera() {
  //   if (this.mediaStream) {
  //     this.mediaStream.getTracks().forEach((track: any) => track.stop());
  //     this.mediaStream = null;
  //   }
  // }
}
