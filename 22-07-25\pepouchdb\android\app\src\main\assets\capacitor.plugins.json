[{"pkg": "@capacitor-community/sqlite", "classpath": "com.getcapacitor.community.database.sqlite.CapacitorSQLitePlugin"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/haptics", "classpath": "com.capacitorjs.plugins.haptics.HapticsPlugin"}, {"pkg": "@capacitor/keyboard", "classpath": "com.capacitorjs.plugins.keyboard.KeyboardPlugin"}, {"pkg": "@capacitor/splash-screen", "classpath": "com.capacitorjs.plugins.splashscreen.SplashScreenPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}]