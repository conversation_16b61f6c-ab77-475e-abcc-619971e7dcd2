{"name": "ionicons", "version": "7.4.0", "description": "Premium icons for Ionic.", "files": ["components/", "dist/", "icons/"], "main": "./dist/index.cjs.js", "module": "./dist/index.js", "types": "dist/types/index.d.ts", "unpkg": "dist/ionicons.js", "collection": "dist/collection/collection-manifest.json", "collection:main": "dist/collection/index.js", "scripts": {"build": "tsc -p scripts/tsconfig.json && npm run build.files && npm run build.component && npm run collection.copy", "build.files": "node scripts/build.js", "build.component": "stencil build", "collection.copy": "node scripts/collection-copy.js", "start": "stencil build --dev --watch --serve", "test": "npm run test.spec", "test.spec": "stencil test --spec"}, "dependencies": {"@stencil/core": "^4.0.3"}, "devDependencies": {"@playwright/test": "^1.33.0", "@types/fs-extra": "^11.0.1", "@types/jest": "^27.0.3", "@types/node": "^20.2.1", "@types/svgo": "^1.3.3", "fs-extra": "^11.1.1", "jest": "^27.0.3", "jest-cli": "^27.0.3", "lerna": "^6.6.2", "puppeteer": "^20.2.1", "semver": "^7.5.1", "serve": "^14.2.0", "svgo": "1.3.2", "typescript": "^5.0.4"}, "keywords": ["icon pack", "ionic", "icon", "svg", "mobile", "web component", "component", "custom element", "material design", "ios"], "homepage": "http://ionicons.com/", "author": {"name": "<PERSON>", "web": "https://twitter.com/benjsperry"}, "contributors": [{"name": "<PERSON>", "web": "http://twitter.com/adamdbradley"}], "repository": {"type": "git", "url": "https://github.com/ionic-team/ionicons.git"}, "bugs": {"url": "https://github.com/ionic-team/ionicons/issues"}, "license": "MIT", "sideEffects": ["icons/imports/"], "web-types": "dist/ionic.web-types.json"}