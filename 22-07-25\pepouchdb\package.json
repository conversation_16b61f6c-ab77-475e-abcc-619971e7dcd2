{"name": "pepouchdb", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^20.0.0", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/platform-browser": "^20.0.0", "@angular/platform-browser-dynamic": "^20.0.0", "@angular/router": "^20.0.0", "@capacitor-community/sqlite": "^7.0.1", "@capacitor/android": "^7.4.2", "@capacitor/app": "7.0.1", "@capacitor/core": "^7.4.2", "@capacitor/haptics": "7.0.1", "@capacitor/keyboard": "7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@ionic/angular": "^8.0.0", "@ionic/pwa-elements": "^3.3.0", "ionicons": "^7.0.0", "pouchdb": "^9.0.0", "pouchdb-adapter-cordova-sqlite": "^2.0.8", "pouchdb-adapter-idb": "^9.0.0", "pouchdb-browser": "^9.0.0", "pouchdb-find": "^9.0.0", "pouchdb-replication": "^9.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^20.0.0", "@angular-eslint/builder": "^20.0.0", "@angular-eslint/eslint-plugin": "^20.0.0", "@angular-eslint/eslint-plugin-template": "^20.0.0", "@angular-eslint/schematics": "^20.0.0", "@angular-eslint/template-parser": "^20.0.0", "@angular/cli": "^20.0.0", "@angular/compiler-cli": "^20.0.0", "@angular/language-service": "^20.0.0", "@capacitor/cli": "^7.4.2", "@ionic/angular-toolkit": "^12.0.0", "@types/jasmine": "~5.1.0", "@types/pouchdb-adapter-cordova-sqlite": "^1.0.4", "@types/pouchdb-browser": "^6.1.5", "@types/pouchdb-find": "^7.3.3", "@types/pouchdb-replication": "^6.4.7", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "eslint": "^9.16.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.0"}, "description": "An Ionic project"}