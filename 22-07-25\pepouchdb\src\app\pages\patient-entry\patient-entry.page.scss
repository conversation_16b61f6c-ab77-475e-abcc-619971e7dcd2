@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');

/* ---------- Popup Overlay ---------- */
.popup-overlay {
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* ---------- Container ---------- */

.container {
  width: 94%;
  height: 90vh;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

.header-container{
  display: flex;
  justify-content: space-between;
  padding: 16px 32px 16px 32px;
  border-bottom:1px solid #E5E7EB;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.title{
  font-family: 'DM Sans', sans-serif;
  font-weight: 500;
  font-size: 20px;
  line-height: 130%;
  letter-spacing: 0;
  vertical-align: middle;
  color: #111827;
  margin: 0;
}

.close-btn{
  height: 15px;
  width: 15px;
}

.form-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* ---------- Progress Steps ---------- */
.progress-steps {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  padding: 16px 32px 32px 32px;
  border-bottom:1px solid #E5E7EB;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
  position: relative;
  margin-top: 19px;
}


.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  opacity: 0.5;
  position: relative;
  z-index: 2;
}

.step:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 6px;
  left: 50%;
  right: -50%;
  height: 3px;
  background: #d3d3d3;
  z-index: 1;
    width: 135px;
}

.step.active {
  opacity: 1;
}

.step.active:not(:last-child)::after {
  background: #F59E0B;
}

.step.completed {
  opacity: 1;
}

.step.completed .dot {
  background: #10B981;
}

.step.completed:not(:last-child)::after {
  background: #10B981;
}

.step.completed .label .step-status {
  color: #10B981;
}

.step.active .dot {
  background: #F59E0B;
}

.step .dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #d3d3d3;
  position: relative;
  z-index: 3;
}

.label .step-title {
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.4px;
  vertical-align: middle;
}

.label .step-status {
  font-family: 'DM Sans', sans-serif;
  font-weight: 400; /* Regular */
  font-size: 11px;
  line-height: 140%; /* or 1.4 */
  letter-spacing: 0.2px;
  vertical-align: middle;
  color: #92400E;
}

/* ---------- Form Layout ---------- */

input[type="date"] {
  font-size: 14px;
  color: #374151;
}
input::placeholder {
  font-size: 14px;
  color: #374151;
}
select option[disabled] {
  color:#374151;
  font-size: 14px;
}
select {
  font-size: 14px;
   color:#374151;
}
.form-section {
  margin-top: 10px;
  padding: 0 32px 32px 32px;
  overflow-y: auto;
  flex: 1;

  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for Firefox */
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.form-section h3 {
  font-family: 'DM Sans', sans-serif;
font-weight: 400; /* Regular weight */
font-size: 16px;
line-height: 150%; /* or 1.5 */
letter-spacing: 0;
vertical-align: middle;
color: #4A4A48;
margin-bottom: 24px;
margin-top: 22px;

}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 200px;
}

.form-group.full-width {
  flex: 1 1 100%;
}

label {
font-family: 'DM Sans', sans-serif;
font-weight: 500;
font-size: 13px;
line-height: 140%;
letter-spacing: 0.2px;
color: #374151;
    margin-top: 29px;
    margin-bottom: 11px;
    margin-left:6px;
}

input, select {
 width: 387px;
height: 48px;
border-radius: 8px;
gap: 10px;
opacity: 1;
padding: 12px 16px;
border-width: 1px;
border-style: solid;
border-color: #D1D5DB;

}
textarea{
   width: 1193px;
   min-width: 320px;
height: 96px;
min-height: 96px;
max-height: 160px;
border-radius: 8px;
gap: 10px;
opacity: 1;
padding: 12px 16px;
border-width: 1px;
border-style: solid;
border-color: #D1D5DB;
}

input:focus, select:focus {
  border-color: #999;
}

/* ---------- Form Actions ---------- */
.form-actions {
  display: flex;
  justify-content:space-between;
     margin-top: 33px;
    border-top: 1px solid #E5E7EB;
    padding: 20px;
}


.btn-back {
  background: none;
  border: none;
  color: #1976d2;
  font-size: 14px;
  cursor: pointer;
  width: 120px;
    display: flex;
    gap: 16px;
    font-family: 'DM Sans', system-ui, -apple-system, sans-serif;
  font-weight: 600;           /* SemiBold */
  font-size: 14px;
  line-height: 140%;          /* or 1.4 */
  letter-spacing: 0.4px;
  text-align: center;
}
.btn-back img{
  width: 14px;
  height: 14px;

}

.btn-next {

  width: 120px;
  min-width: 120px;
  height: 48px;
  min-height: 48px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 1;
  padding: 12px 24px;
  border: 1px solid #007AFF;        /* border-style and color required */
  background-color: transparent;
  color: #007AFF;        /* adjust as needed */
  box-sizing: border-box;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600; /* SemiBold */
  font-size: 14px;
  line-height: 140%; /* or 1.4 */
  letter-spacing: 0.4px;
}



.btn-next:hover {
  background: #c2d6ec;
}

.btn-register {
  background: #1976d2;
  color: #fff;
  padding: 10px 20px;
  font-size: 14px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-register:hover {
  background: #1565c0;
}

/* ---------- Image Capture Section ---------- */
.image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
}

.img-2{
   width: 402.67px; /* rounded for readability */
  height: 366px;
  display: flex;
      flex-direction: column;
  gap: 16px;
  opacity: 1;
}

.image-box {
  width: 402.67px;   /* rounded for simplicity */
  height: 302px;
  border-radius: 8px;
  opacity: 1;
  background-color: #D1D5DB;
}

.actions {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content:space-between;
}

.btn-retake {
  width: 120px;
  min-width: 120px;
  height: 48px;
  min-height: 48px;
  border-radius: 8px;
  display: inline-flex;         /* needed for gap to work */
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;          /* shorthand for top/bottom and left/right */
  box-sizing: border-box;      /* keeps size consistent with padding */
  border: 1px solid #ccc;      /* includes width, style, and default color */
  opacity: 1;
  background: transparent;
}

.capture-controls {
  display: flex;
  gap: 0;
  width: 96px;
  height: 48px;
}

.btn-camera {
  width: 48px;
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  opacity: 1;
  cursor: pointer;
  background-color: #007AFF;
}

.btn-dropdown {
   width: 48px;
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border: 1px solid #007AFF;
  opacity: 1;
  background: transparent;
}

.btn-camera img,
.btn-dropdown img {
  max-width: 24px;
  max-height: 24px;
}

/* ---------- Upload Section ---------- */
.upload-box, .upload-box-full {
  border: 1px dashed #c7c7c7;
  border-radius: 6px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  background: #fff;
  transition: background 0.3s ease;
  margin-top: 20px;
}

.upload-box:hover, .upload-box-full:hover {
  background: #f9f9f9;
}

.upload-icon {
  font-size: 40px;
  color: #1976d2;
}

.upload-text {
  font-family: 'DM Sans', system-ui, -apple-system, sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.4px;
  text-align: center;
  vertical-align: middle;
  color: #374151;
}

.upload-subtext {
  margin-top: 4px;
  font-size: 14px;
  color: #555;
   width: 392px;
  height: 21px;
  display: flex;             /* required for gap to take effect */
  gap: 8px;
  align-items: center;      /* optional, for vertical centering */
  opacity: 1;
  transform: rotate(0deg);
}
.doc-info{
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 16px;

}
.choose-file, .capture-image {
  color: #1976d2;
  cursor: pointer;
  text-decoration: underline;
}


/* ---------- Upload Documents Layout ---------- */
.upload-docs-wrapper {
  display: flex;
  gap: 24px;
  margin-top: 20px;
      /* width: 1225px; */
    height: 366px;
    background: transparent;

}

.uploaded-docs {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.uploaded-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8f9fb;
  border-radius: 6px;
  padding: 8px 12px;
}

.doc-preview {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  object-fit: cover;
}

.doc-type {
  flex: 1;
  padding: 6px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.doc-actions {
  display: flex;
  align-items: center;
     gap: 8px;
    position: relative;
    bottom: 34px;
}

.icon-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
}

/* Upload Box (Right Side) - Only used when documents are uploaded */
.upload-box {
  flex: 1;
}

/* Responsive */
@media (max-width: 768px) {
  .upload-docs-wrapper {
    flex-direction: column;
  }

  .upload-box {
    width: 100%;
  }
}

/* ---------- Uploaded Items List ---------- */
.uploaded-item {
     display: flex
;
    align-items: center;
    gap: 16px;
    background: #f8f9fb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 10px;
    width: 545px;
    height: 122px;
    background: #F9FAFB;
}

.doc-preview {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
}

.doc-info p {
  margin: 0;
}

.doc-type{

  width: 340px;
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #ccc;
  box-sizing: border-box;
  opacity: 1;
  transform: rotate(0deg);
}

.doc-name {
  font-weight: 600;
}

.doc-meta {
  font-size: 12px;
  color: gray;
}

.icon-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
}

/* ---------- Camera Capture Box ---------- */
.camera-box {
  margin-top: 20px;
  text-align: center;
}

.preview-box {
  width: 300px;
  height: 200px;
  background: #d5d8de;
  margin: 0 auto 12px auto;
  border-radius: 6px;
}

.capture-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.btn-outline {
  background: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
}

/* ---------- Responsive ---------- */
@media (max-width: 1024px) {
  .form-row {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .progress-steps {
    flex-direction: column;
    gap: 12px;
  }
  .form-actions {
    flex-direction: column-reverse;
    align-items: center;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .image-box {
    width: 90%;
    height: 160px;
  }
  .preview-box {
    width: 90%;
    height: 160px;
  }

  // .header-container {
  // }

  .progress-steps {
    padding: 16px;
  }

  .form-section {
    padding: 0 16px 16px 16px;
  }
}
