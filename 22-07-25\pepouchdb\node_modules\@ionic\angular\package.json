{"name": "@ionic/angular", "version": "8.6.5", "description": "Angular specific wrappers for @ionic/core", "keywords": ["ionic", "framework", "angular", "mobile", "app", "webapp", "capacitor", "<PERSON><PERSON>", "progressive web app", "pwa"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ionic-team/ionic-framework.git"}, "bugs": {"url": "https://github.com/ionic-team/ionic-framework/issues"}, "homepage": "https://ionicframework.com/", "exports": {"./css/*": {"style": "./css/*"}, "./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/ionic-angular.mjs", "esm": "./esm2022/ionic-angular.mjs", "default": "./fesm2022/ionic-angular.mjs"}, "./common": {"types": "./common/index.d.ts", "esm2022": "./esm2022/common/ionic-angular-common.mjs", "esm": "./esm2022/common/ionic-angular-common.mjs", "default": "./fesm2022/ionic-angular-common.mjs"}, "./standalone": {"types": "./standalone/index.d.ts", "esm2022": "./esm2022/standalone/ionic-angular-standalone.mjs", "esm": "./esm2022/standalone/ionic-angular-standalone.mjs", "default": "./fesm2022/ionic-angular-standalone.mjs"}}, "dependencies": {"@ionic/core": "8.6.5", "ionicons": "^7.0.0", "jsonc-parser": "^3.0.0", "tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": ">=16.0.0", "@angular/forms": ">=16.0.0", "@angular/router": ">=16.0.0", "rxjs": ">=7.5.0", "zone.js": ">=0.13.0"}, "schematics": "./schematics/collection.json", "module": "fesm2022/ionic-angular.mjs", "typings": "index.d.ts", "sideEffects": false}