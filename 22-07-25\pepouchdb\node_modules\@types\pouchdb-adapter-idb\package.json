{"name": "@types/pouchdb-adapter-idb", "version": "6.1.7", "description": "TypeScript definitions for pouchdb-adapter-idb", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-idb", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "spaulg", "url": "https://github.com/spaulg"}, {"name": "<PERSON>", "githubUsername": "geppy", "url": "https://github.com/geppy"}, {"name": "<PERSON><PERSON>", "githubUsername": "fredgalvao", "url": "https://github.com/fredgalvao"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pouchdb-adapter-idb"}, "scripts": {}, "dependencies": {"@types/pouchdb-core": "*"}, "typesPublisherContentHash": "f2064ef2839e1e95f0fc719d8781da6da8a288c300ab4d87eecaced42ba69af2", "typeScriptVersion": "4.5"}