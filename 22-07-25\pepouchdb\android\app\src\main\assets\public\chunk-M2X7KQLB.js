var M=class{constructor(){this.gestureId=0,this.requestedStart=new Map,this.disabledGestures=new Map,this.disabledScroll=new Set}createGesture(t){var s;return new L(this,this.newID(),t.name,(s=t.priority)!==null&&s!==void 0?s:0,!!t.disableScroll)}createBlocker(t={}){return new O(this,this.newID(),t.disable,!!t.disableScroll)}start(t,s,r){return this.canStart(t)?(this.requestedStart.set(s,r),!0):(this.requestedStart.delete(s),!1)}capture(t,s,r){if(!this.start(t,s,r))return!1;let a=this.requestedStart,n=-1e4;if(a.forEach(c=>{n=Math.max(n,c)}),n===r){this.capturedId=s,a.clear();let c=new CustomEvent("ionGestureCaptured",{detail:{gestureName:t}});return document.dispatchEvent(c),!0}return a.delete(s),!1}release(t){this.requestedStart.delete(t),this.capturedId===t&&(this.capturedId=void 0)}disableGesture(t,s){let r=this.disabledGestures.get(t);r===void 0&&(r=new Set,this.disabledGestures.set(t,r)),r.add(s)}enableGesture(t,s){let r=this.disabledGestures.get(t);r!==void 0&&r.delete(s)}disableScroll(t){this.disabledScroll.add(t),this.disabledScroll.size===1&&document.body.classList.add(x)}enableScroll(t){this.disabledScroll.delete(t),this.disabledScroll.size===0&&document.body.classList.remove(x)}canStart(t){return!(this.capturedId!==void 0||this.isDisabled(t))}isCaptured(){return this.capturedId!==void 0}isScrollDisabled(){return this.disabledScroll.size>0}isDisabled(t){let s=this.disabledGestures.get(t);return!!(s&&s.size>0)}newID(){return this.gestureId++,this.gestureId}},L=class{constructor(t,s,r,a,n){this.id=s,this.name=r,this.disableScroll=n,this.priority=a*1e6+s,this.ctrl=t}canStart(){return this.ctrl?this.ctrl.canStart(this.name):!1}start(){return this.ctrl?this.ctrl.start(this.name,this.id,this.priority):!1}capture(){if(!this.ctrl)return!1;let t=this.ctrl.capture(this.name,this.id,this.priority);return t&&this.disableScroll&&this.ctrl.disableScroll(this.id),t}release(){this.ctrl&&(this.ctrl.release(this.id),this.disableScroll&&this.ctrl.enableScroll(this.id))}destroy(){this.release(),this.ctrl=void 0}},O=class{constructor(t,s,r,a){this.id=s,this.disable=r,this.disableScroll=a,this.ctrl=t}block(){if(this.ctrl){if(this.disable)for(let t of this.disable)this.ctrl.disableGesture(t,this.id);this.disableScroll&&this.ctrl.disableScroll(this.id)}}unblock(){if(this.ctrl){if(this.disable)for(let t of this.disable)this.ctrl.enableGesture(t,this.id);this.disableScroll&&this.ctrl.enableScroll(this.id)}}destroy(){this.unblock(),this.ctrl=void 0}},x="backdrop-no-scroll",R=new M;var X=(e,t,s,r)=>{let a=W(e)?{capture:!1,passive:!!r.passive}:!1,n,c;return e.__zone_symbol__addEventListener?(n="__zone_symbol__addEventListener",c="__zone_symbol__removeEventListener"):(n="addEventListener",c="removeEventListener"),e[n](t,s,a),()=>{e[c](t,s,a)}},W=e=>{if(_===void 0)try{let t=Object.defineProperty({},"passive",{get:()=>{_=!0}});e.addEventListener("optsTest",()=>{},t)}catch{_=!1}return!!_},_,j=2e3,B=(e,t,s,r,a)=>{let n,c,h,o,d,l,m,S=0,p=f=>{S=Date.now()+j,t(f)&&(!c&&s&&(c=X(e,"touchmove",s,a)),h||(h=X(f.target,"touchend",i,a)),o||(o=X(f.target,"touchcancel",i,a)))},v=f=>{S>Date.now()||t(f)&&(!l&&s&&(l=X(q(e),"mousemove",s,a)),m||(m=X(q(e),"mouseup",y,a)))},i=f=>{b(),r&&r(f)},y=f=>{E(),r&&r(f)},b=()=>{c&&c(),h&&h(),o&&o(),c=h=o=void 0},E=()=>{l&&l(),m&&m(),l=m=void 0},T=()=>{b(),E()},Y=(f=!0)=>{f?(n||(n=X(e,"touchstart",p,a)),d||(d=X(e,"mousedown",v,a))):(n&&n(),d&&d(),n=d=void 0,T())};return{enable:Y,stop:T,destroy:()=>{Y(!1),r=s=t=void 0}}},q=e=>e instanceof Document?e:e.ownerDocument,H=(e,t,s)=>{let r=s*(Math.PI/180),a=e==="x",n=Math.cos(r),c=t*t,h=0,o=0,d=!1,l=0;return{start(m,S){h=m,o=S,l=0,d=!0},detect(m,S){if(!d)return!1;let p=m-h,v=S-o,i=p*p+v*v;if(i<c)return!1;let y=Math.sqrt(i),b=(a?p:v)/y;return b>n?l=1:b<-n?l=-1:l=0,d=!1,!0},isGesture(){return l!==0},getDirection(){return l}}},N=e=>{let t=!1,s=!1,r=!0,a=!1,n=Object.assign({disableScroll:!1,direction:"x",gesturePriority:0,passive:!0,maxAngle:40,threshold:10},e),c=n.canStart,h=n.onWillStart,o=n.onStart,d=n.onEnd,l=n.notCaptured,m=n.onMove,S=n.threshold,p=n.passive,v=n.blurOnStart,i={type:"pan",startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,event:void 0,data:void 0},y=H(n.direction,n.threshold,n.maxAngle),b=R.createGesture({name:e.gestureName,priority:e.gesturePriority,disableScroll:e.disableScroll}),E=u=>{let D=A(u);return s||!r||(z(u,i),i.startX=i.currentX,i.startY=i.currentY,i.startTime=i.currentTime=D,i.velocityX=i.velocityY=i.deltaX=i.deltaY=0,i.event=u,c&&c(i)===!1)||(b.release(),!b.start())?!1:(s=!0,S===0?g():(y.start(i.startX,i.startY),!0))},T=u=>{if(t){!a&&r&&(a=!0,C(i,u),requestAnimationFrame(Y));return}C(i,u),y.detect(i.currentX,i.currentY)&&(!y.isGesture()||!g())&&k()},Y=()=>{t&&(a=!1,m&&m(i))},g=()=>b.capture()?(t=!0,r=!1,i.startX=i.currentX,i.startY=i.currentY,i.startTime=i.currentTime,h?h(i).then(P):P(),!0):!1,f=()=>{if(typeof document<"u"){let u=document.activeElement;u?.blur&&u.blur()}},P=()=>{v&&f(),o&&o(i),r=!0},G=()=>{t=!1,s=!1,a=!1,r=!0,b.release()},I=u=>{let D=t,F=r;if(G(),!!F){if(C(i,u),D){d&&d(i);return}l&&l(i)}},w=B(n.el,E,T,I,{passive:p}),k=()=>{G(),w.stop(),l&&l(i)};return{enable(u=!0){u||(t&&I(void 0),G()),w.enable(u)},destroy(){b.destroy(),w.destroy()}}},C=(e,t)=>{if(!t)return;let s=e.currentX,r=e.currentY,a=e.currentTime;z(t,e);let n=e.currentX,c=e.currentY,o=(e.currentTime=A(t))-a;if(o>0&&o<100){let d=(n-s)/o,l=(c-r)/o;e.velocityX=d*.7+e.velocityX*.3,e.velocityY=l*.7+e.velocityY*.3}e.deltaX=n-e.startX,e.deltaY=c-e.startY,e.event=t},z=(e,t)=>{let s=0,r=0;if(e){let a=e.changedTouches;if(a&&a.length>0){let n=a[0];s=n.clientX,r=n.clientY}else e.pageX!==void 0&&(s=e.pageX,r=e.pageY)}t.currentX=s,t.currentY=r},A=e=>e.timeStamp||Date.now();export{x as a,R as b,N as c};
