import{a as d,f as u}from"./chunk-C5RQ2IC2.js";import{a}from"./chunk-42C7ZIID.js";import{g as l}from"./chunk-2R6CW7ES.js";var h=()=>d.get("experimentalCloseWatcher",!1)&&a!==void 0&&"CloseWatcher"in a,E=()=>{document.addEventListener("backbutton",()=>{})},k=()=>{let r=document,c=!1,s=()=>{if(c)return;let o=0,n=[],f=new CustomEvent("ionBackButton",{bubbles:!1,detail:{register(t,e){n.push({priority:t,handler:e,id:o++})}}});r.dispatchEvent(f);let p=t=>l(null,null,function*(){try{if(t?.handler){let e=t.handler(i);e!=null&&(yield e)}}catch(e){u("[ion-app] - Exception in startHardwareBackButton:",e)}}),i=()=>{if(n.length>0){let t={priority:Number.MIN_SAFE_INTEGER,handler:()=>{},id:-1};n.forEach(e=>{e.priority>=t.priority&&(t=e)}),c=!0,n=n.filter(e=>e.id!==t.id),p(t).then(()=>c=!1)}};i()};if(h()){let o,n=()=>{o?.destroy(),o=new a.CloseWatcher,o.onclose=()=>{s(),n()}};n()}else r.addEventListener("backbutton",s)},m=100,v=99;export{h as a,E as b,k as c,m as d,v as e};
