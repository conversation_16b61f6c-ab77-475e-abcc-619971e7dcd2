import{a as r}from"./chunk-CKP3SGE2.js";import{a as d,c as u}from"./chunk-EHNA26RN.js";import{g as l}from"./chunk-2R6CW7ES.js";var h=()=>d.get("experimentalCloseWatcher",!1)&&r!==void 0&&"CloseWatcher"in r,E=()=>{document.addEventListener("backbutton",()=>{})},k=()=>{let c=document,a=!1,s=()=>{if(a)return;let o=0,n=[],f=new CustomEvent("ionBackButton",{bubbles:!1,detail:{register(t,e){n.push({priority:t,handler:e,id:o++})}}});c.dispatchEvent(f);let p=t=>l(null,null,function*(){try{if(t?.handler){let e=t.handler(i);e!=null&&(yield e)}}catch(e){u("[ion-app] - Exception in startHardwareBackButton:",e)}}),i=()=>{if(n.length>0){let t={priority:Number.MIN_SAFE_INTEGER,handler:()=>{},id:-1};n.forEach(e=>{e.priority>=t.priority&&(t=e)}),a=!0,n=n.filter(e=>e.id!==t.id),p(t).then(()=>a=!1)}};i()};if(h()){let o,n=()=>{o?.destroy(),o=new r.CloseWatcher,o.onclose=()=>{s(),n()}};n()}else c.addEventListener("backbutton",s)},m=100,v=99;export{h as a,E as b,k as c,m as d,v as e};
