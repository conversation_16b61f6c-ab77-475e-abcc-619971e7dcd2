import{b as he}from"./chunk-JIT2VFLB.js";import{e as de,f as ve,g as ge}from"./chunk-PK2JKDOR.js";import{g as J3,r as Y3}from"./chunk-EHNA26RN.js";import{$ as se,$a as R3,A as r0,B as R1,C as S1,D as F,E as w3,F as ie,G as p1,H,I as j,J as k1,Ja as J0,K as f3,Ka as C3,L as x3,La as ce,M as ae,Ma as _3,Na as B3,Oa as b3,P as $1,Pa as S3,Q as _1,Qa as A3,R as f1,Ra as L3,S as N,Sa as I3,T as q,Ta as j3,U as w2,Ua as H3,V as k0,Va as V3,W as Y1,Wa as O3,X as e0,Xa as D3,Y as U,Ya as E3,Z as N1,Za as T3,_ as g0,_a as P3,a as W,aa as z0,ab as le,b as w0,ba as G,bb as D0,c as m2,ca as M3,d as v3,da as k3,db as q3,e as f0,ea as O0,eb as F3,f as g3,fa as re,fb as N3,g as x0,ga as f2,gb as $3,h as te,hb as U3,i as Q0,ia as z3,ib as G3,j as h3,jb as K3,k as M0,ka as y3,kb as W3,lb as Z3,m as X0,mb as Q3,n as O1,nb as Y0,o as D1,ob as X3,p as s0,q as u3,qb as x2,r as v0,s as ne,t as R,u as oe,v as s1,w as m,x as p3,y as P,z as m3}from"./chunk-U77DO3YD.js";import{a as H0,b as V0,e as ee,f as Z0,g as X}from"./chunk-2R6CW7ES.js";var Qt=ee((un,pn)=>{"use strict";(function(e){if(typeof un=="object")pn.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var o;try{o=window}catch{o=self}o.SparkMD5=e()}})(function(e){"use strict";var o=function(u,h){return u+h&4294967295},t=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function n(u,h,r,v,w,f){return h=o(o(h,u),o(v,f)),o(h<<w|h>>>32-w,r)}function i(u,h){var r=u[0],v=u[1],w=u[2],f=u[3];r+=(v&w|~v&f)+h[0]-680876936|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[1]-389564586|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[2]+606105819|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[3]-1044525330|0,v=(v<<22|v>>>10)+w|0,r+=(v&w|~v&f)+h[4]-176418897|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[5]+1200080426|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[6]-1473231341|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[7]-45705983|0,v=(v<<22|v>>>10)+w|0,r+=(v&w|~v&f)+h[8]+1770035416|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[9]-1958414417|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[10]-42063|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[11]-1990404162|0,v=(v<<22|v>>>10)+w|0,r+=(v&w|~v&f)+h[12]+1804603682|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[13]-40341101|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[14]-1502002290|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[15]+1236535329|0,v=(v<<22|v>>>10)+w|0,r+=(v&f|w&~f)+h[1]-165796510|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[6]-1069501632|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[11]+643717713|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[0]-373897302|0,v=(v<<20|v>>>12)+w|0,r+=(v&f|w&~f)+h[5]-701558691|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[10]+38016083|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[15]-660478335|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[4]-405537848|0,v=(v<<20|v>>>12)+w|0,r+=(v&f|w&~f)+h[9]+568446438|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[14]-1019803690|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[3]-187363961|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[8]+1163531501|0,v=(v<<20|v>>>12)+w|0,r+=(v&f|w&~f)+h[13]-1444681467|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[2]-51403784|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[7]+1735328473|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[12]-1926607734|0,v=(v<<20|v>>>12)+w|0,r+=(v^w^f)+h[5]-378558|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[8]-2022574463|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[11]+1839030562|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[14]-35309556|0,v=(v<<23|v>>>9)+w|0,r+=(v^w^f)+h[1]-1530992060|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[4]+1272893353|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[7]-155497632|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[10]-1094730640|0,v=(v<<23|v>>>9)+w|0,r+=(v^w^f)+h[13]+681279174|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[0]-358537222|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[3]-722521979|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[6]+76029189|0,v=(v<<23|v>>>9)+w|0,r+=(v^w^f)+h[9]-640364487|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[12]-421815835|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[15]+530742520|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[2]-995338651|0,v=(v<<23|v>>>9)+w|0,r+=(w^(v|~f))+h[0]-198630844|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[7]+1126891415|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[14]-1416354905|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[5]-57434055|0,v=(v<<21|v>>>11)+w|0,r+=(w^(v|~f))+h[12]+1700485571|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[3]-1894986606|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[10]-1051523|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[1]-2054922799|0,v=(v<<21|v>>>11)+w|0,r+=(w^(v|~f))+h[8]+1873313359|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[15]-30611744|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[6]-1560198380|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[13]+1309151649|0,v=(v<<21|v>>>11)+w|0,r+=(w^(v|~f))+h[4]-145523070|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[11]-1120210379|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[2]+718787259|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[9]-343485551|0,v=(v<<21|v>>>11)+w|0,u[0]=r+u[0]|0,u[1]=v+u[1]|0,u[2]=w+u[2]|0,u[3]=f+u[3]|0}function a(u){var h=[],r;for(r=0;r<64;r+=4)h[r>>2]=u.charCodeAt(r)+(u.charCodeAt(r+1)<<8)+(u.charCodeAt(r+2)<<16)+(u.charCodeAt(r+3)<<24);return h}function s(u){var h=[],r;for(r=0;r<64;r+=4)h[r>>2]=u[r]+(u[r+1]<<8)+(u[r+2]<<16)+(u[r+3]<<24);return h}function c(u){var h=u.length,r=[1732584193,-271733879,-1732584194,271733878],v,w,f,O,E,V;for(v=64;v<=h;v+=64)i(r,a(u.substring(v-64,v)));for(u=u.substring(v-64),w=u.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],v=0;v<w;v+=1)f[v>>2]|=u.charCodeAt(v)<<(v%4<<3);if(f[v>>2]|=128<<(v%4<<3),v>55)for(i(r,f),v=0;v<16;v+=1)f[v]=0;return O=h*8,O=O.toString(16).match(/(.*?)(.{0,8})$/),E=parseInt(O[2],16),V=parseInt(O[1],16)||0,f[14]=E,f[15]=V,i(r,f),r}function d(u){var h=u.length,r=[1732584193,-271733879,-1732584194,271733878],v,w,f,O,E,V;for(v=64;v<=h;v+=64)i(r,s(u.subarray(v-64,v)));for(u=v-64<h?u.subarray(v-64):new Uint8Array(0),w=u.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],v=0;v<w;v+=1)f[v>>2]|=u[v]<<(v%4<<3);if(f[v>>2]|=128<<(v%4<<3),v>55)for(i(r,f),v=0;v<16;v+=1)f[v]=0;return O=h*8,O=O.toString(16).match(/(.*?)(.{0,8})$/),E=parseInt(O[2],16),V=parseInt(O[1],16)||0,f[14]=E,f[15]=V,i(r,f),r}function x(u){var h="",r;for(r=0;r<4;r+=1)h+=t[u>>r*8+4&15]+t[u>>r*8&15];return h}function k(u){var h;for(h=0;h<u.length;h+=1)u[h]=x(u[h]);return u.join("")}k(c("hello"))!=="5d41402abc4b2a76b9719d911017c592"&&(o=function(u,h){var r=(u&65535)+(h&65535),v=(u>>16)+(h>>16)+(r>>16);return v<<16|r&65535}),typeof ArrayBuffer<"u"&&!ArrayBuffer.prototype.slice&&function(){function u(h,r){return h=h|0||0,h<0?Math.max(h+r,0):Math.min(h,r)}ArrayBuffer.prototype.slice=function(h,r){var v=this.byteLength,w=u(h,v),f=v,O,E,V,n1;return r!==e&&(f=u(r,v)),w>f?new ArrayBuffer(0):(O=f-w,E=new ArrayBuffer(O),V=new Uint8Array(E),n1=new Uint8Array(this,w,O),V.set(n1),E)}}();function l(u){return/[\u0080-\uFFFF]/.test(u)&&(u=unescape(encodeURIComponent(u))),u}function M(u,h){var r=u.length,v=new ArrayBuffer(r),w=new Uint8Array(v),f;for(f=0;f<r;f+=1)w[f]=u.charCodeAt(f);return h?w:v}function _(u){return String.fromCharCode.apply(null,new Uint8Array(u))}function b(u,h,r){var v=new Uint8Array(u.byteLength+h.byteLength);return v.set(new Uint8Array(u)),v.set(new Uint8Array(h),u.byteLength),r?v:v.buffer}function A(u){var h=[],r=u.length,v;for(v=0;v<r-1;v+=2)h.push(parseInt(u.substr(v,2),16));return String.fromCharCode.apply(String,h)}function y(){this.reset()}return y.prototype.append=function(u){return this.appendBinary(l(u)),this},y.prototype.appendBinary=function(u){this._buff+=u,this._length+=u.length;var h=this._buff.length,r;for(r=64;r<=h;r+=64)i(this._hash,a(this._buff.substring(r-64,r)));return this._buff=this._buff.substring(r-64),this},y.prototype.end=function(u){var h=this._buff,r=h.length,v,w=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],f;for(v=0;v<r;v+=1)w[v>>2]|=h.charCodeAt(v)<<(v%4<<3);return this._finish(w,r),f=k(this._hash),u&&(f=A(f)),this.reset(),f},y.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},y.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash.slice()}},y.prototype.setState=function(u){return this._buff=u.buff,this._length=u.length,this._hash=u.hash,this},y.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},y.prototype._finish=function(u,h){var r=h,v,w,f;if(u[r>>2]|=128<<(r%4<<3),r>55)for(i(this._hash,u),r=0;r<16;r+=1)u[r]=0;v=this._length*8,v=v.toString(16).match(/(.*?)(.{0,8})$/),w=parseInt(v[2],16),f=parseInt(v[1],16)||0,u[14]=w,u[15]=f,i(this._hash,u)},y.hash=function(u,h){return y.hashBinary(l(u),h)},y.hashBinary=function(u,h){var r=c(u),v=k(r);return h?A(v):v},y.ArrayBuffer=function(){this.reset()},y.ArrayBuffer.prototype.append=function(u){var h=b(this._buff.buffer,u,!0),r=h.length,v;for(this._length+=u.byteLength,v=64;v<=r;v+=64)i(this._hash,s(h.subarray(v-64,v)));return this._buff=v-64<r?new Uint8Array(h.buffer.slice(v-64)):new Uint8Array(0),this},y.ArrayBuffer.prototype.end=function(u){var h=this._buff,r=h.length,v=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],w,f;for(w=0;w<r;w+=1)v[w>>2]|=h[w]<<(w%4<<3);return this._finish(v,r),f=k(this._hash),u&&(f=A(f)),this.reset(),f},y.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},y.ArrayBuffer.prototype.getState=function(){var u=y.prototype.getState.call(this);return u.buff=_(u.buff),u},y.ArrayBuffer.prototype.setState=function(u){return u.buff=M(u.buff,!0),y.prototype.setState.call(this,u)},y.ArrayBuffer.prototype.destroy=y.prototype.destroy,y.ArrayBuffer.prototype._finish=y.prototype._finish,y.ArrayBuffer.hash=function(u,h){var r=d(new Uint8Array(u)),v=k(r);return h?A(v):v},y})});var xn=ee(Jt=>{"use strict";Jt.stringify=function(o){var t=[];t.push({obj:o});for(var n="",i,a,s,c,d,x,k,l,M,_,b;i=t.pop();)if(a=i.obj,s=i.prefix||"",c=i.val||"",n+=s,c)n+=c;else if(typeof a!="object")n+=typeof a>"u"?null:JSON.stringify(a);else if(a===null)n+="null";else if(Array.isArray(a)){for(t.push({val:"]"}),d=a.length-1;d>=0;d--)x=d===0?"":",",t.push({obj:a[d],prefix:x});t.push({val:"["})}else{k=[];for(l in a)a.hasOwnProperty(l)&&k.push(l);for(t.push({val:"}"}),d=k.length-1;d>=0;d--)M=k[d],_=a[M],b=d>0?",":"",b+=JSON.stringify(M)+":",t.push({obj:_,prefix:b});t.push({val:"{"})}return n};function E0(e,o,t){var n=t[t.length-1];e===n.element&&(t.pop(),n=t[t.length-1]);var i=n.element,a=n.index;if(Array.isArray(i))i.push(e);else if(a===o.length-2){var s=o.pop();i[s]=e}else o.push(e)}Jt.parse=function(e){for(var o=[],t=[],n=0,i,a,s,c,d,x,k,l,M;;){if(i=e[n++],i==="}"||i==="]"||typeof i>"u"){if(o.length===1)return o.pop();E0(o.pop(),o,t);continue}switch(i){case" ":case"	":case`
`:case":":case",":break;case"n":n+=3,E0(null,o,t);break;case"t":n+=3,E0(!0,o,t);break;case"f":n+=4,E0(!1,o,t);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":case"-":for(a="",n--;;)if(s=e[n++],/[\d\.\-e\+]/.test(s))a+=s;else{n--;break}E0(parseFloat(a),o,t);break;case'"':for(c="",d=void 0,x=0;k=e[n++],k!=='"'||d==="\\"&&x%2===1;)c+=k,d=k,d==="\\"?x++:x=0;E0(JSON.parse('"'+c+'"'),o,t);break;case"[":l={element:[],index:o.length},o.push(l.element),t.push(l);break;case"{":M={element:{},index:o.length},o.push(M.element),t.push(M);break;default:throw new Error("unexpectedly reached end of input: "+i)}}}});var e4=ee((C7,Yt)=>{"use strict";var T0=typeof Reflect=="object"?Reflect:null,Mn=T0&&typeof T0.apply=="function"?T0.apply:function(o,t,n){return Function.prototype.apply.call(o,t,n)},y2;T0&&typeof T0.ownKeys=="function"?y2=T0.ownKeys:Object.getOwnPropertySymbols?y2=function(o){return Object.getOwnPropertyNames(o).concat(Object.getOwnPropertySymbols(o))}:y2=function(o){return Object.getOwnPropertyNames(o)};function N8(e){console&&console.warn&&console.warn(e)}var zn=Number.isNaN||function(o){return o!==o};function x1(){x1.init.call(this)}Yt.exports=x1;Yt.exports.once=K8;x1.EventEmitter=x1;x1.prototype._events=void 0;x1.prototype._eventsCount=0;x1.prototype._maxListeners=void 0;var kn=10;function C2(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(x1,"defaultMaxListeners",{enumerable:!0,get:function(){return kn},set:function(e){if(typeof e!="number"||e<0||zn(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");kn=e}});x1.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};x1.prototype.setMaxListeners=function(o){if(typeof o!="number"||o<0||zn(o))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+o+".");return this._maxListeners=o,this};function yn(e){return e._maxListeners===void 0?x1.defaultMaxListeners:e._maxListeners}x1.prototype.getMaxListeners=function(){return yn(this)};x1.prototype.emit=function(o){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var i=o==="error",a=this._events;if(a!==void 0)i=i&&a.error===void 0;else if(!i)return!1;if(i){var s;if(t.length>0&&(s=t[0]),s instanceof Error)throw s;var c=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw c.context=s,c}var d=a[o];if(d===void 0)return!1;if(typeof d=="function")Mn(d,this,t);else for(var x=d.length,k=Sn(d,x),n=0;n<x;++n)Mn(k[n],this,t);return!0};function Cn(e,o,t,n){var i,a,s;if(C2(t),a=e._events,a===void 0?(a=e._events=Object.create(null),e._eventsCount=0):(a.newListener!==void 0&&(e.emit("newListener",o,t.listener?t.listener:t),a=e._events),s=a[o]),s===void 0)s=a[o]=t,++e._eventsCount;else if(typeof s=="function"?s=a[o]=n?[t,s]:[s,t]:n?s.unshift(t):s.push(t),i=yn(e),i>0&&s.length>i&&!s.warned){s.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(o)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=o,c.count=s.length,N8(c)}return e}x1.prototype.addListener=function(o,t){return Cn(this,o,t,!1)};x1.prototype.on=x1.prototype.addListener;x1.prototype.prependListener=function(o,t){return Cn(this,o,t,!0)};function $8(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function _n(e,o,t){var n={fired:!1,wrapFn:void 0,target:e,type:o,listener:t},i=$8.bind(n);return i.listener=t,n.wrapFn=i,i}x1.prototype.once=function(o,t){return C2(t),this.on(o,_n(this,o,t)),this};x1.prototype.prependOnceListener=function(o,t){return C2(t),this.prependListener(o,_n(this,o,t)),this};x1.prototype.removeListener=function(o,t){var n,i,a,s,c;if(C2(t),i=this._events,i===void 0)return this;if(n=i[o],n===void 0)return this;if(n===t||n.listener===t)--this._eventsCount===0?this._events=Object.create(null):(delete i[o],i.removeListener&&this.emit("removeListener",o,n.listener||t));else if(typeof n!="function"){for(a=-1,s=n.length-1;s>=0;s--)if(n[s]===t||n[s].listener===t){c=n[s].listener,a=s;break}if(a<0)return this;a===0?n.shift():U8(n,a),n.length===1&&(i[o]=n[0]),i.removeListener!==void 0&&this.emit("removeListener",o,c||t)}return this};x1.prototype.off=x1.prototype.removeListener;x1.prototype.removeAllListeners=function(o){var t,n,i;if(n=this._events,n===void 0)return this;if(n.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):n[o]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete n[o]),this;if(arguments.length===0){var a=Object.keys(n),s;for(i=0;i<a.length;++i)s=a[i],s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(t=n[o],typeof t=="function")this.removeListener(o,t);else if(t!==void 0)for(i=t.length-1;i>=0;i--)this.removeListener(o,t[i]);return this};function Bn(e,o,t){var n=e._events;if(n===void 0)return[];var i=n[o];return i===void 0?[]:typeof i=="function"?t?[i.listener||i]:[i]:t?G8(i):Sn(i,i.length)}x1.prototype.listeners=function(o){return Bn(this,o,!0)};x1.prototype.rawListeners=function(o){return Bn(this,o,!1)};x1.listenerCount=function(e,o){return typeof e.listenerCount=="function"?e.listenerCount(o):bn.call(e,o)};x1.prototype.listenerCount=bn;function bn(e){var o=this._events;if(o!==void 0){var t=o[e];if(typeof t=="function")return 1;if(t!==void 0)return t.length}return 0}x1.prototype.eventNames=function(){return this._eventsCount>0?y2(this._events):[]};function Sn(e,o){for(var t=new Array(o),n=0;n<o;++n)t[n]=e[n];return t}function U8(e,o){for(;o+1<e.length;o++)e[o]=e[o+1];e.pop()}function G8(e){for(var o=new Array(e.length),t=0;t<o.length;++t)o[t]=e[t].listener||e[t];return o}function K8(e,o){return new Promise(function(t,n){function i(s){e.removeListener(o,a),n(s)}function a(){typeof e.removeListener=="function"&&e.removeListener("error",i),t([].slice.call(arguments))}An(e,o,a,{once:!0}),o!=="error"&&W8(e,i,{once:!0})})}function W8(e,o,t){typeof e.on=="function"&&An(e,"error",o,t)}function An(e,o,t,n){if(typeof e.on=="function")n.once?e.once(o,t):e.on(o,t);else if(typeof e.addEventListener=="function")e.addEventListener(o,function i(a){n.once&&e.removeEventListener(o,i),t(a)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}});var on=(e,o)=>X(null,null,function*(){if(!(typeof window>"u"))return yield J3(),Y3(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16,"router-animation"]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16,"html-attributes"],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16,"counter-formatter"],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16,"root-params"],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16,"component-props"],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16,"counter-formatter"],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16,"component-props"],"beforeLeave":[16,"before-leave"],"beforeEnter":[16,"before-enter"]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-input-otp",[[38,"ion-input-otp",{"autocapitalize":[1],"color":[513],"disabled":[516],"fill":[1],"inputmode":[1],"length":[2],"pattern":[1],"readonly":[516],"separators":[1],"shape":[1],"size":[1],"type":[1],"value":[1032],"inputValues":[32],"hasFocus":[32],"previousInputValues":[32],"setFocus":[64]},null,{"value":["valueChanged"],"separators":["processSeparators"],"length":["processSeparators"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16,"pin-formatter"],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16,"format-options"],"readonly":[4],"isDateEnabled":[16,"is-date-enabled"],"showAdjacentDays":[4,"show-adjacent-days"],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16,"title-selected-dates-formatter"],"multiple":[4],"highlightedDates":[16,"highlighted-dates"],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16,"component-props"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16,"presenting-element"],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},[[9,"resize","onWindowResize"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"component":[1],"componentProps":[16,"component-props"],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16,"router-animation"],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32],"isInteractive":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16,"swipe-handler"],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"],"aria-checked":["onAriaChanged"],"aria-label":["onAriaChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),o)});(function(){if(typeof window<"u"&&window.Reflect!==void 0&&window.customElements!==void 0){var e=HTMLElement;window.HTMLElement=function(){return Reflect.construct(e,[],this.constructor)},HTMLElement.prototype=e.prototype,HTMLElement.prototype.constructor=HTMLElement,Object.setPrototypeOf(HTMLElement,e)}})();var K=["*"],Oo=["outletContent"],Do=["outlet"],Eo=[[["","slot","top"]],"*",[["ion-tab"]]],To=["[slot=top]","*","ion-tab"];function Po(e,o){if(e&1){let t=$1();H(0,"ion-router-outlet",5,1),_1("stackWillChange",function(i){O1(t);let a=f1();return D1(a.onStackWillChange(i))})("stackDidChange",function(i){O1(t);let a=f1();return D1(a.onStackDidChange(i))}),j()}}function Ro(e,o){e&1&&q(0,2,["*ngIf","tabs.length > 0"])}function qo(e,o){if(e&1&&(H(0,"div",1),ae(1,2),j()),e&2){let t=f1();s1(),p1("ngTemplateOutlet",t.template)}}function Fo(e,o){if(e&1&&ae(0,1),e&2){let t=f1();p1("ngTemplateOutlet",t.template)}}var No=(()=>{class e extends Y0{constructor(t,n){super(t,n)}writeValue(t){this.elementRef.nativeElement.checked=this.lastValue=t,X3(this.elementRef)}_handleIonChange(t){this.handleValueChange(t,t.checked)}static \u0275fac=function(n){return new(n||e)(m(s0),m(R))};static \u0275dir=r0({type:e,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(n,i){n&1&&_1("ionChange",function(s){return i._handleIonChange(s.target)})},standalone:!1,features:[z0([{provide:J0,useExisting:e,multi:!0}]),R1]})}return e})(),$o=(()=>{class e extends Y0{el;constructor(t,n){super(t,n),this.el=n}handleInputEvent(t){this.handleValueChange(t,t.value)}registerOnChange(t){this.el.nativeElement.tagName==="ION-INPUT"||this.el.nativeElement.tagName==="ION-INPUT-OTP"?super.registerOnChange(n=>{t(n===""?null:parseFloat(n))}):super.registerOnChange(t)}static \u0275fac=function(n){return new(n||e)(m(s0),m(R))};static \u0275dir=r0({type:e,selectors:[["ion-input","type","number"],["ion-input-otp",3,"type","text"],["ion-range"]],hostBindings:function(n,i){n&1&&_1("ionInput",function(s){return i.handleInputEvent(s.target)})},standalone:!1,features:[z0([{provide:J0,useExisting:e,multi:!0}]),R1]})}return e})(),Uo=(()=>{class e extends Y0{constructor(t,n){super(t,n)}_handleChangeEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(n){return new(n||e)(m(s0),m(R))};static \u0275dir=r0({type:e,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(n,i){n&1&&_1("ionChange",function(s){return i._handleChangeEvent(s.target)})},standalone:!1,features:[z0([{provide:J0,useExisting:e,multi:!0}]),R1]})}return e})(),Go=(()=>{class e extends Y0{constructor(t,n){super(t,n)}_handleInputEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(n){return new(n||e)(m(s0),m(R))};static \u0275dir=r0({type:e,selectors:[["ion-input",3,"type","number"],["ion-input-otp","type","text"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(n,i){n&1&&_1("ionInput",function(s){return i._handleInputEvent(s.target)})},standalone:!1,features:[z0([{provide:J0,useExisting:e,multi:!0}]),R1]})}return e})(),Ko=(e,o)=>{let t=e.prototype;o.forEach(n=>{Object.defineProperty(t,n,{get(){return this.el[n]},set(i){this.z.runOutsideAngular(()=>this.el[n]=i)},configurable:!0})})},Wo=(e,o)=>{let t=e.prototype;o.forEach(n=>{t[n]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[n].apply(this.el,i))}})},m1=(e,o,t)=>{t.forEach(n=>e[n]=g3(o,n))};function Z(e){return function(t){let{defineCustomElementFn:n,inputs:i,methods:a}=e;return n!==void 0&&n(),i&&Ko(t,i),a&&Wo(t,a),t}}var Zo=(()=>{let e=class ue{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||ue)(m(G),m(R),m(F))};static \u0275cmp=P({type:ue,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],e),e})(),Qo=(()=>{let e=class pe{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionChange"])}static \u0275fac=function(n){return new(n||pe)(m(G),m(R),m(F))};static \u0275cmp=P({type:pe,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],e),e})(),Xo=(()=>{let e=class me{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(n){return new(n||me)(m(G),m(R),m(F))};static \u0275cmp=P({type:me,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),Jo=(()=>{let e=class we{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(n){return new(n||we)(m(G),m(R),m(F))};static \u0275cmp=P({type:we,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),Yo=(()=>{let e=class fe{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||fe)(m(G),m(R),m(F))};static \u0275cmp=P({type:fe,selectors:[["ion-app"]],standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({methods:["setFocus"]})],e),e})(),ei=(()=>{let e=class xe{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||xe)(m(G),m(R),m(F))};static \u0275cmp=P({type:xe,selectors:[["ion-avatar"]],standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({})],e),e})(),ti=(()=>{let e=class Me{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionBackdropTap"])}static \u0275fac=function(n){return new(n||Me)(m(G),m(R),m(F))};static \u0275cmp=P({type:Me,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["stopPropagation","tappable","visible"]})],e),e})(),ni=(()=>{let e=class ke{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||ke)(m(G),m(R),m(F))};static \u0275cmp=P({type:ke,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","mode"]})],e),e})(),oi=(()=>{let e=class ze{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(n){return new(n||ze)(m(G),m(R),m(F))};static \u0275cmp=P({type:ze,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],e),e})(),ii=(()=>{let e=class ye{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionCollapsedClick"])}static \u0275fac=function(n){return new(n||ye)(m(G),m(R),m(F))};static \u0275cmp=P({type:ye,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],e),e})(),ai=(()=>{let e=class Ce{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(n){return new(n||Ce)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ce,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],e),e})(),si=(()=>{let e=class _e{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||_e)(m(G),m(R),m(F))};static \u0275cmp=P({type:_e,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["collapse"]})],e),e})(),ri=(()=>{let e=class Be{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Be)(m(G),m(R),m(F))};static \u0275cmp=P({type:Be,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),ci=(()=>{let e=class be{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||be)(m(G),m(R),m(F))};static \u0275cmp=P({type:be,selectors:[["ion-card-content"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["mode"]})],e),e})(),li=(()=>{let e=class Se{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Se)(m(G),m(R),m(F))};static \u0275cmp=P({type:Se,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","mode","translucent"]})],e),e})(),di=(()=>{let e=class Ae{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Ae)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ae,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","mode"]})],e),e})(),vi=(()=>{let e=class Le{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Le)(m(G),m(R),m(F))};static \u0275cmp=P({type:Le,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","mode"]})],e),e})(),gi=(()=>{let e=class Ie{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(n){return new(n||Ie)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ie,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],e),e})(),hi=(()=>{let e=class je{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||je)(m(G),m(R),m(F))};static \u0275cmp=P({type:je,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","disabled","mode","outline"]})],e),e})(),ui=(()=>{let e=class He{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||He)(m(G),m(R),m(F))};static \u0275cmp=P({type:He,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],e),e})(),pi=(()=>{let e=class Ve{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(n){return new(n||Ve)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ve,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],e),e})(),mi=(()=>{let e=class Oe{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}static \u0275fac=function(n){return new(n||Oe)(m(G),m(R),m(F))};static \u0275cmp=P({type:Oe,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showAdjacentDays:"showAdjacentDays",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showAdjacentDays","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],e),e})(),wi=(()=>{let e=class De{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||De)(m(G),m(R),m(F))};static \u0275cmp=P({type:De,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","datetime","disabled","mode"]})],e),e})(),fi=(()=>{let e=class Ee{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Ee)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ee,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],e),e})(),xi=(()=>{let e=class Te{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(n){return new(n||Te)(m(G),m(R),m(F))};static \u0275cmp=P({type:Te,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],e),e})(),Mi=(()=>{let e=class Pe{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Pe)(m(G),m(R),m(F))};static \u0275cmp=P({type:Pe,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["activated","side"]})],e),e})(),ki=(()=>{let e=class Re{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Re)(m(G),m(R),m(F))};static \u0275cmp=P({type:Re,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["collapse","mode","translucent"]})],e),e})(),zi=(()=>{let e=class qe{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||qe)(m(G),m(R),m(F))};static \u0275cmp=P({type:qe,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["fixed"]})],e),e})(),yi=(()=>{let e=class Fe{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Fe)(m(G),m(R),m(F))};static \u0275cmp=P({type:Fe,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["collapse","mode","translucent"]})],e),e})(),Ci=(()=>{let e=class Ne{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Ne)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ne,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],e),e})(),_i=(()=>{let e=class $e{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}static \u0275fac=function(n){return new(n||$e)(m(G),m(R),m(F))};static \u0275cmp=P({type:$e,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["alt","src"]})],e),e})(),Bi=(()=>{let e=class Ue{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionInfinite"])}static \u0275fac=function(n){return new(n||Ue)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ue,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["disabled","position","threshold"],methods:["complete"]})],e),e})(),bi=(()=>{let e=class Ge{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Ge)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ge,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["loadingSpinner","loadingText"]})],e),e})(),Si=(()=>{let e=class Ke{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}static \u0275fac=function(n){return new(n||Ke)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ke,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),Ai=(()=>{let e=class We{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionInput","ionChange","ionComplete","ionBlur","ionFocus"])}static \u0275fac=function(n){return new(n||We)(m(G),m(R),m(F))};static \u0275cmp=P({type:We,selectors:[["ion-input-otp"]],inputs:{autocapitalize:"autocapitalize",color:"color",disabled:"disabled",fill:"fill",inputmode:"inputmode",length:"length",pattern:"pattern",readonly:"readonly",separators:"separators",shape:"shape",size:"size",type:"type",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["autocapitalize","color","disabled","fill","inputmode","length","pattern","readonly","separators","shape","size","type","value"],methods:["setFocus"]})],e),e})(),Li=(()=>{let e=class Ze{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Ze)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ze,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","hideIcon","mode","showIcon"]})],e),e})(),Ii=(()=>{let e=class Qe{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Qe)(m(G),m(R),m(F))};static \u0275cmp=P({type:Qe,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),ji=(()=>{let e=class Xe{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Xe)(m(G),m(R),m(F))};static \u0275cmp=P({type:Xe,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","mode","sticky"]})],e),e})(),Hi=(()=>{let e=class Je{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Je)(m(G),m(R),m(F))};static \u0275cmp=P({type:Je,selectors:[["ion-item-group"]],standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({})],e),e})(),Vi=(()=>{let e=class Ye{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Ye)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ye,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],e),e})(),Oi=(()=>{let e=class et{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionSwipe"])}static \u0275fac=function(n){return new(n||et)(m(G),m(R),m(F))};static \u0275cmp=P({type:et,selectors:[["ion-item-options"]],inputs:{side:"side"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["side"]})],e),e})(),Di=(()=>{let e=class tt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionDrag"])}static \u0275fac=function(n){return new(n||tt)(m(G),m(R),m(F))};static \u0275cmp=P({type:tt,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],e),e})(),Ei=(()=>{let e=class nt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||nt)(m(G),m(R),m(F))};static \u0275cmp=P({type:nt,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","mode","position"]})],e),e})(),Ti=(()=>{let e=class ot{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||ot)(m(G),m(R),m(F))};static \u0275cmp=P({type:ot,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],e),e})(),Pi=(()=>{let e=class it{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||it)(m(G),m(R),m(F))};static \u0275cmp=P({type:it,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","lines","mode"]})],e),e})(),Ri=(()=>{let e=class at{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(n){return new(n||at)(m(G),m(R),m(F))};static \u0275cmp=P({type:at,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),qi=(()=>{let e=class st{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}static \u0275fac=function(n){return new(n||st)(m(G),m(R),m(F))};static \u0275cmp=P({type:st,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],e),e})(),Fi=(()=>{let e=class rt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||rt)(m(G),m(R),m(F))};static \u0275cmp=P({type:rt,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["autoHide","color","disabled","menu","mode","type"]})],e),e})(),Ni=(()=>{let e=class ct{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||ct)(m(G),m(R),m(F))};static \u0275cmp=P({type:ct,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["autoHide","menu"]})],e),e})(),$i=(()=>{let e=class lt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||lt)(m(G),m(R),m(F))};static \u0275cmp=P({type:lt,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["component","componentProps","routerAnimation","routerDirection"]})],e),e})(),Ui=(()=>{let e=class dt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||dt)(m(G),m(R),m(F))};static \u0275cmp=P({type:dt,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","mode"]})],e),e})(),Gi=(()=>{let e=class vt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||vt)(m(G),m(R),m(F))};static \u0275cmp=P({type:vt,selectors:[["ion-picker"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["mode"]})],e),e})(),Ki=(()=>{let e=class gt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionChange"])}static \u0275fac=function(n){return new(n||gt)(m(G),m(R),m(F))};static \u0275cmp=P({type:gt,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],e),e})(),Wi=(()=>{let e=class ht{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||ht)(m(G),m(R),m(F))};static \u0275cmp=P({type:ht,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","disabled","value"]})],e),e})(),Zi=(()=>{let e=class ut{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(n){return new(n||ut)(m(G),m(R),m(F))};static \u0275cmp=P({type:ut,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],e),e})(),Qi=(()=>{let e=class pt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||pt)(m(G),m(R),m(F))};static \u0275cmp=P({type:pt,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["buffer","color","mode","reversed","type","value"]})],e),e})(),Xi=(()=>{let e=class mt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(n){return new(n||mt)(m(G),m(R),m(F))};static \u0275cmp=P({type:mt,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],e),e})(),Ji=(()=>{let e=class wt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionChange"])}static \u0275fac=function(n){return new(n||wt)(m(G),m(R),m(F))};static \u0275cmp=P({type:wt,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],e),e})(),Yi=(()=>{let e=class ft{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}static \u0275fac=function(n){return new(n||ft)(m(G),m(R),m(F))};static \u0275cmp=P({type:ft,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],e),e})(),e8=(()=>{let e=class xt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionRefresh","ionPull","ionStart"])}static \u0275fac=function(n){return new(n||xt)(m(G),m(R),m(F))};static \u0275cmp=P({type:xt,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],e),e})(),t8=(()=>{let e=class Mt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Mt)(m(G),m(R),m(F))};static \u0275cmp=P({type:Mt,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],e),e})(),n8=(()=>{let e=class kt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||kt)(m(G),m(R),m(F))};static \u0275cmp=P({type:kt,selectors:[["ion-reorder"]],standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({})],e),e})(),o8=(()=>{let e=class zt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionItemReorder"])}static \u0275fac=function(n){return new(n||zt)(m(G),m(R),m(F))};static \u0275cmp=P({type:zt,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["disabled"],methods:["complete"]})],e),e})(),i8=(()=>{let e=class yt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||yt)(m(G),m(R),m(F))};static \u0275cmp=P({type:yt,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["type"],methods:["addRipple"]})],e),e})(),a8=(()=>{let e=class Ct{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Ct)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ct,selectors:[["ion-row"]],standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({})],e),e})(),s8=(()=>{let e=class _t{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}static \u0275fac=function(n){return new(n||_t)(m(G),m(R),m(F))};static \u0275cmp=P({type:_t,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),r8=(()=>{let e=class Bt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionChange"])}static \u0275fac=function(n){return new(n||Bt)(m(G),m(R),m(F))};static \u0275cmp=P({type:Bt,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],e),e})(),c8=(()=>{let e=class bt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||bt)(m(G),m(R),m(F))};static \u0275cmp=P({type:bt,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["contentId","disabled","layout","mode","type","value"]})],e),e})(),l8=(()=>{let e=class St{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||St)(m(G),m(R),m(F))};static \u0275cmp=P({type:St,selectors:[["ion-segment-content"]],standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({})],e),e})(),d8=(()=>{let e=class At{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionSegmentViewScroll"])}static \u0275fac=function(n){return new(n||At)(m(G),m(R),m(F))};static \u0275cmp=P({type:At,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["disabled"]})],e),e})(),v8=(()=>{let e=class Lt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}static \u0275fac=function(n){return new(n||Lt)(m(G),m(R),m(F))};static \u0275cmp=P({type:Lt,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],e),e})(),g8=(()=>{let e=class It{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||It)(m(G),m(R),m(F))};static \u0275cmp=P({type:It,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["header","multiple","options"]})],e),e})(),h8=(()=>{let e=class jt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||jt)(m(G),m(R),m(F))};static \u0275cmp=P({type:jt,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["disabled","value"]})],e),e})(),u8=(()=>{let e=class Ht{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Ht)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ht,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["animated"]})],e),e})(),p8=(()=>{let e=class Vt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Vt)(m(G),m(R),m(F))};static \u0275cmp=P({type:Vt,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","duration","name","paused"]})],e),e})(),m8=(()=>{let e=class Ot{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionSplitPaneVisible"])}static \u0275fac=function(n){return new(n||Ot)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ot,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["contentId","disabled","when"]})],e),e})(),sn=(()=>{let e=class Dt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Dt)(m(G),m(R),m(F))};static \u0275cmp=P({type:Dt,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["component","tab"],methods:["setActive"]})],e),e})(),Et=(()=>{let e=class Tt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Tt)(m(G),m(R),m(F))};static \u0275cmp=P({type:Tt,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","mode","selectedTab","translucent"]})],e),e})(),w8=(()=>{let e=class Pt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Pt)(m(G),m(R),m(F))};static \u0275cmp=P({type:Pt,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],e),e})(),f8=(()=>{let e=class Rt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Rt)(m(G),m(R),m(F))};static \u0275cmp=P({type:Rt,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","mode"]})],e),e})(),x8=(()=>{let e=class qt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}static \u0275fac=function(n){return new(n||qt)(m(G),m(R),m(F))};static \u0275cmp=P({type:qt,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],e),e})(),M8=(()=>{let e=class Ft{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Ft)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ft,selectors:[["ion-thumbnail"]],standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({})],e),e})(),k8=(()=>{let e=class Nt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Nt)(m(G),m(R),m(F))};static \u0275cmp=P({type:Nt,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","size"]})],e),e})(),z8=(()=>{let e=class $t{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(n){return new(n||$t)(m(G),m(R),m(F))};static \u0275cmp=P({type:$t,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),y8=(()=>{let e=class Ut{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement,m1(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(n){return new(n||Ut)(m(G),m(R),m(F))};static \u0275cmp=P({type:Ut,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],e),e})(),C8=(()=>{let e=class Gt{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||Gt)(m(G),m(R),m(F))};static \u0275cmp=P({type:Gt,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})};return e=W([Z({inputs:["color","mode"]})],e),e})(),M2=(()=>{class e extends N3{parentOutlet;outletContent;constructor(t,n,i,a,s,c,d,x){super(t,n,i,a,s,c,d,x),this.parentOutlet=x}static \u0275fac=function(n){return new(n||e)(ne("name"),ne("tabs"),m(M3),m(R),m(y3),m(F),m(z3),m(e,12))};static \u0275cmp=P({type:e,selectors:[["ion-router-outlet"]],viewQuery:function(n,i){if(n&1&&k0(Oo,7,p3),n&2){let a;Y1(a=e0())&&(i.outletContent=a.first)}},standalone:!1,features:[R1],ngContentSelectors:K,decls:3,vars:0,consts:[["outletContent",""]],template:function(n,i){n&1&&(N(),f3(0,null,0),q(2),x3())},encapsulation:2})}return e})(),_8=(()=>{class e extends Z3{outlet;tabBar;tabBars;tabs;static \u0275fac=(()=>{let t;return function(i){return(t||(t=v0(e)))(i||e)}})();static \u0275cmp=P({type:e,selectors:[["ion-tabs"]],contentQueries:function(n,i,a){if(n&1&&(w2(a,Et,5),w2(a,Et,4),w2(a,sn,4)),n&2){let s;Y1(s=e0())&&(i.tabBar=s.first),Y1(s=e0())&&(i.tabBars=s),Y1(s=e0())&&(i.tabs=s)}},viewQuery:function(n,i){if(n&1&&k0(Do,5,M2),n&2){let a;Y1(a=e0())&&(i.outlet=a.first)}},standalone:!1,features:[R1],ngContentSelectors:To,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(n,i){n&1&&(N(Eo),q(0),H(1,"div",2,0),S1(3,Po,2,0,"ion-router-outlet",3)(4,Ro,1,0,"ng-content",4),j(),q(5,1)),n&2&&(s1(3),p1("ngIf",i.tabs.length===0),s1(),p1("ngIf",i.tabs.length>0))},dependencies:[O0,M2],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]})}return e})(),B8=(()=>{class e extends U3{constructor(t,n,i,a,s,c){super(t,n,i,a,s,c)}static \u0275fac=function(n){return new(n||e)(m(M2,8),m(P3),m(R3),m(R),m(F),m(G))};static \u0275cmp=P({type:e,selectors:[["ion-back-button"]],standalone:!1,features:[R1],ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})}return e})(),b8=(()=>{class e extends W3{constructor(t,n,i,a,s,c){super(t,n,i,a,s,c)}static \u0275fac=function(n){return new(n||e)(m(R),m(X0),m(s0),m(D0),m(F),m(G))};static \u0275cmp=P({type:e,selectors:[["ion-nav"]],standalone:!1,features:[R1],ngContentSelectors:K,decls:1,vars:0,template:function(n,i){n&1&&(N(),q(0))},encapsulation:2,changeDetection:0})}return e})(),S8=(()=>{class e extends G3{static \u0275fac=(()=>{let t;return function(i){return(t||(t=v0(e)))(i||e)}})();static \u0275dir=r0({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],standalone:!1,features:[R1]})}return e})(),A8=(()=>{class e extends K3{static \u0275fac=(()=>{let t;return function(i){return(t||(t=v0(e)))(i||e)}})();static \u0275dir=r0({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],standalone:!1,features:[R1]})}return e})(),L8=(()=>{class e extends F3{static \u0275fac=(()=>{let t;return function(i){return(t||(t=v0(e)))(i||e)}})();static \u0275cmp=P({type:e,selectors:[["ion-modal"]],standalone:!1,features:[R1],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(n,i){n&1&&S1(0,qo,2,1,"div",0),n&2&&p1("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[O0,re],encapsulation:2,changeDetection:0})}return e})(),I8=(()=>{class e extends q3{static \u0275fac=(()=>{let t;return function(i){return(t||(t=v0(e)))(i||e)}})();static \u0275cmp=P({type:e,selectors:[["ion-popover"]],standalone:!1,features:[R1],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(n,i){n&1&&S1(0,Fo,1,1,"ng-container",0),n&2&&p1("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[O0,re],encapsulation:2,changeDetection:0})}return e})(),j8={provide:ce,useExisting:te(()=>rn),multi:!0},rn=(()=>{class e extends O3{static \u0275fac=(()=>{let t;return function(i){return(t||(t=v0(e)))(i||e)}})();static \u0275dir=r0({type:e,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(n,i){n&2&&ie("max",i._enabled?i.max:null)},standalone:!1,features:[z0([j8]),R1]})}return e})(),H8={provide:ce,useExisting:te(()=>cn),multi:!0},cn=(()=>{class e extends D3{static \u0275fac=(()=>{let t;return function(i){return(t||(t=v0(e)))(i||e)}})();static \u0275dir=r0({type:e,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(n,i){n&2&&ie("min",i._enabled?i.min:null)},standalone:!1,features:[z0([H8]),R1]})}return e})();var V8=(()=>{class e extends x2{angularDelegate=M0(D0);injector=M0(s0);environmentInjector=M0(X0);constructor(){super(de)}create(t){return super.create(V0(H0({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(n){return new(n||e)};static \u0275prov=Q0({token:e,factory:e.\u0275fac})}return e})();var Kt=class extends x2{angularDelegate=M0(D0);injector=M0(s0);environmentInjector=M0(X0);constructor(){super(ve)}create(o){return super.create(V0(H0({},o),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},ln=(()=>{class e extends x2{constructor(){super(ge)}static \u0275fac=function(n){return new(n||e)};static \u0275prov=Q0({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),O8=(e,o,t)=>()=>{let n=o.defaultView;if(n&&typeof window<"u"){he(V0(H0({},e),{_zoneGate:a=>t.run(a)}));let i="__zone_symbol__addEventListener"in o.body?"__zone_symbol__addEventListener":"addEventListener";return on(n,{exclude:["ion-tabs"],syncQueue:!0,raf:Q3,jmp:a=>t.runOutsideAngular(a),ael(a,s,c,d){a[i](s,c,d)},rel(a,s,c,d){a.removeEventListener(s,c,d)}})}},D8=[Zo,Qo,Xo,Jo,Yo,ei,ti,ni,oi,ii,ai,si,ri,ci,li,di,vi,gi,hi,ui,pi,mi,wi,fi,xi,Mi,ki,zi,yi,Ci,_i,Bi,bi,Si,Ai,Li,Ii,ji,Hi,Vi,Oi,Di,Ei,Ti,Pi,Ri,qi,Fi,Ni,$i,Ui,Gi,Ki,Wi,Zi,Qi,Xi,Ji,Yi,e8,t8,n8,o8,i8,a8,s8,r8,c8,l8,d8,v8,g8,h8,u8,p8,m8,sn,Et,w8,f8,x8,M8,k8,z8,y8,C8],a7=[...D8,L8,I8,No,$o,Uo,Go,_8,M2,B8,b8,S8,A8,cn,rn],dn=(()=>{class e{static forRoot(t={}){return{ngModule:e,providers:[{provide:le,useValue:t},{provide:w3,useFactory:O8,multi:!0,deps:[le,u3,F]},D0,$3()]}}static \u0275fac=function(n){return new(n||e)};static \u0275mod=m3({type:e});static \u0275inj=h3({providers:[V8,Kt],imports:[f2]})}return e})();var Wt,T8=function(){if(typeof window>"u")return new Map;if(!Wt){var e=window;e.Ionicons=e.Ionicons||{},Wt=e.Ionicons.map=e.Ionicons.map||new Map}return Wt},Zt=function(e){Object.keys(e).forEach(function(o){vn(o,e[o]);var t=o.replace(/([a-z0-9]|(?=[A-Z]))([A-Z0-9])/g,"$1-$2").toLowerCase();o!==t&&vn(t,e[o])})},vn=function(e,o){var t=T8(),n=t.get(e);n===void 0?t.set(e,o):n!==o&&console.warn('[Ionicons Warning]: Multiple icons were mapped to name "'.concat(e,'". Ensure that multiple icons are not mapped to the same icon name.'))};var gn="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M384 224v184a40 40 0 01-40 40H104a40 40 0 01-40-40V168a40 40 0 0140-40h167.48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M459.94 53.25a16.06 16.06 0 00-23.22-.56L424.35 65a8 8 0 000 11.31l11.34 11.32a8 8 0 0011.34 0l12.06-12c6.1-6.09 6.67-16.01.85-22.38zM399.34 90L218.82 270.2a9 9 0 00-2.31 3.93L208.16 299a3.91 3.91 0 004.86 4.86l24.85-8.35a9 9 0 003.93-2.31L422 112.66a9 9 0 000-12.66l-9.95-10a9 9 0 00-12.71 0z'/></svg>";var hn="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M112 112l20 320c.95 18.49 14.4 32 32 32h184c17.67 0 30.87-13.51 32-32l20-320' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M80 112h352' class='ionicon-stroke-width'/><path d='M192 112V72h0a23.93 23.93 0 0124-24h80a23.93 23.93 0 0124 24h0v40M256 176v224M184 176l8 224M328 176l-8 224' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var A2=Z0(Qt());var k2,P8=new Uint8Array(16);function Xt(){if(!k2&&(k2=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto<"u"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!k2))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return k2(P8)}var mn=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function R8(e){return typeof e=="string"&&mn.test(e)}var wn=R8;var E1=[];for(z2=0;z2<256;++z2)E1.push((z2+256).toString(16).substr(1));var z2;function q8(e){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,t=(E1[e[o+0]]+E1[e[o+1]]+E1[e[o+2]]+E1[e[o+3]]+"-"+E1[e[o+4]]+E1[e[o+5]]+"-"+E1[e[o+6]]+E1[e[o+7]]+"-"+E1[e[o+8]]+E1[e[o+9]]+"-"+E1[e[o+10]]+E1[e[o+11]]+E1[e[o+12]]+E1[e[o+13]]+E1[e[o+14]]+E1[e[o+15]]).toLowerCase();if(!wn(t))throw TypeError("Stringified UUID is invalid");return t}var fn=q8;function F8(e,o,t){e=e||{};var n=e.random||(e.rng||Xt)();if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,o){t=t||0;for(var i=0;i<16;++i)o[t+i]=n[i];return o}return fn(n)}var e2=F8;var D4=Z0(xn()),i0=Z0(e4());function Z8(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer||typeof Blob<"u"&&e instanceof Blob}function Q8(e){return e instanceof ArrayBuffer?e.slice(0):e.slice(0,e.size,e.type)}var o6=Function.prototype.toString,X8=o6.call(Object);function J8(e){var o=Object.getPrototypeOf(e);if(o===null)return!0;var t=o.constructor;return typeof t=="function"&&t instanceof t&&o6.call(t)==X8}function V1(e){var o,t,n;if(!e||typeof e!="object")return e;if(Array.isArray(e)){for(o=[],t=0,n=e.length;t<n;t++)o[t]=V1(e[t]);return o}if(e instanceof Date&&isFinite(e))return e.toISOString();if(Z8(e))return Q8(e);if(!J8(e))return e;o={};for(t in e)if(Object.prototype.hasOwnProperty.call(e,t)){var i=V1(e[t]);typeof i<"u"&&(o[t]=i)}return o}function i6(e){var o=!1;return function(...t){if(o)throw new Error("once called more than once");o=!0,e.apply(this,t)}}function a6(e){return function(...o){o=V1(o);var t=this,n=typeof o[o.length-1]=="function"?o.pop():!1,i=new Promise(function(a,s){var c;try{var d=i6(function(x,k){x?s(x):a(k)});o.push(d),c=e.apply(t,o),c&&typeof c.then=="function"&&a(c)}catch(x){s(x)}});return n&&i.then(function(a){n(null,a)},n),i}}function Y8(e,o,t){if(e.constructor.listeners("debug").length){for(var n=["api",e.name,o],i=0;i<t.length-1;i++)n.push(t[i]);e.constructor.emit("debug",n);var a=t[t.length-1];t[t.length-1]=function(s,c){var d=["api",e.name,o];d=d.concat(s?["error",s]:["success",c]),e.constructor.emit("debug",d),a(s,c)}}}function A1(e,o){return a6(function(...t){if(this._closed)return Promise.reject(new Error("database is closed"));if(this._destroyed)return Promise.reject(new Error("database is destroyed"));var n=this;return Y8(n,e,t),this.taskqueue.isReady?o.apply(this,t):new Promise(function(i,a){n.taskqueue.addTask(function(s){s?a(s):i(n[e].apply(n,t))})})})}function a2(e,o){for(var t={},n=0,i=o.length;n<i;n++){var a=o[n];a in e&&(t[a]=e[a])}return t}var ea=6;function Ln(e){return e}function ta(e){return[{ok:e}]}function s6(e,o,t){var n=o.docs,i=new Map;n.forEach(function(A){i.has(A.id)?i.get(A.id).push(A):i.set(A.id,[A])});var a=i.size,s=0,c=new Array(a);function d(){var A=[];c.forEach(function(y){y.docs.forEach(function(u){A.push({id:y.id,docs:[u]})})}),t(null,{results:A})}function x(){++s===a&&d()}function k(A,y,u){c[A]={id:y,docs:u},x()}var l=[];i.forEach(function(A,y){l.push(y)});var M=0;function _(){if(!(M>=l.length)){var A=Math.min(M+ea,l.length),y=l.slice(M,A);b(y,M),M+=y.length}}function b(A,y){A.forEach(function(u,h){var r=y+h,v=i.get(u),w=a2(v[0],["atts_since","attachments"]);w.open_revs=v.map(function(O){return O.rev}),w.open_revs=w.open_revs.filter(Ln);var f=Ln;w.open_revs.length===0&&(delete w.open_revs,f=ta),["revs","attachments","binary","ajax","latest"].forEach(function(O){O in o&&(w[O]=o[O])}),e.get(u,w,function(O,E){var V;O?V=[{error:O}]:V=f(E),k(r,u,V),_()})})}_()}var u4;try{localStorage.setItem("_pouch_check_localstorage",1),u4=!!localStorage.getItem("_pouch_check_localstorage")}catch{u4=!1}function L2(){return u4}var a0=typeof queueMicrotask=="function"?queueMicrotask:function(o){Promise.resolve().then(o)},p4=class extends i0.default{constructor(){super(),this._listeners={},L2()&&addEventListener("storage",o=>{this.emit(o.key)})}addListener(o,t,n,i){if(this._listeners[t])return;var a=!1,s=this;function c(){if(!s._listeners[t])return;if(a){a="waiting";return}a=!0;var d=a2(i,["style","include_docs","attachments","conflicts","filter","doc_ids","view","since","query_params","binary","return_docs"]);function x(){a=!1}n.changes(d).on("change",function(k){k.seq>i.since&&!i.cancelled&&(i.since=k.seq,i.onChange(k))}).on("complete",function(){a==="waiting"&&a0(c),a=!1}).on("error",x)}this._listeners[t]=c,this.on(o,c)}removeListener(o,t){t in this._listeners&&(super.removeListener(o,this._listeners[t]),delete this._listeners[t])}notifyLocalWindows(o){L2()&&(localStorage[o]=localStorage[o]==="a"?"b":"a")}notify(o){this.emit(o),this.notifyLocalWindows(o)}};function K1(e){if(typeof console<"u"&&typeof console[e]=="function"){var o=Array.prototype.slice.call(arguments,1);console[e].apply(console,o)}}function na(e,o){var t=6e5;e=parseInt(e,10)||0,o=parseInt(o,10),o!==o||o<=e?o=(e||1)<<1:o=o+1,o>t&&(e=t>>1,o=t);var n=Math.random(),i=o-e;return~~(i*n+e)}function oa(e){var o=0;return e||(o=2e3),na(e,o)}function m4(e,o){K1("info","The above "+e+" is totally normal. "+o)}var z1=class extends Error{constructor(o,t,n){super(),this.status=o,this.name=t,this.message=n,this.error=!0}toString(){return JSON.stringify({status:this.status,name:this.name,message:this.message,reason:this.reason})}},B7=new z1(401,"unauthorized","Name or password is incorrect."),ia=new z1(400,"bad_request","Missing JSON list of 'docs'"),U1=new z1(404,"not_found","missing"),F0=new z1(409,"conflict","Document update conflict"),r6=new z1(400,"bad_request","_id field must contain a string"),aa=new z1(412,"missing_id","_id is required for puts"),sa=new z1(400,"bad_request","Only reserved document ids may start with underscore."),b7=new z1(412,"precondition_failed","Database not open"),E4=new z1(500,"unknown_error","Database encountered an unknown error"),c6=new z1(500,"badarg","Some query argument is invalid"),S7=new z1(400,"invalid_request","Request was invalid"),ra=new z1(400,"query_parse_error","Some query parameter is invalid"),In=new z1(500,"doc_validation","Bad special document member"),R2=new z1(400,"bad_request","Something wrong with the request"),t4=new z1(400,"bad_request","Document must be a JSON object"),A7=new z1(404,"not_found","Database not found"),T4=new z1(500,"indexed_db_went_bad","unknown"),L7=new z1(500,"web_sql_went_bad","unknown"),I7=new z1(500,"levelDB_went_went_bad","unknown"),j7=new z1(403,"forbidden","Forbidden by design doc validate_doc_update function"),b2=new z1(400,"bad_request","Invalid rev format"),H7=new z1(412,"file_exists","The database could not be created, the file already exists."),ca=new z1(412,"missing_stub","A pre-existing attachment stub wasn't found"),V7=new z1(413,"invalid_url","Provided URL is invalid");function c1(e,o){function t(n){for(var i=Object.getOwnPropertyNames(e),a=0,s=i.length;a<s;a++)typeof e[i[a]]!="function"&&(this[i[a]]=e[i[a]]);this.stack===void 0&&(this.stack=new Error().stack),n!==void 0&&(this.reason=n)}return t.prototype=z1.prototype,new t(o)}function N0(e){if(typeof e!="object"){var o=e;e=E4,e.data=o}return"error"in e&&e.error==="conflict"&&(e.name="conflict",e.status=409),"name"in e||(e.name=e.error||"unknown"),"status"in e||(e.status=500),"message"in e||(e.message=e.message||e.reason),"stack"in e||(e.stack=new Error().stack),e}function la(e,o,t){try{return!e(o,t)}catch(i){var n="Filter function threw: "+i.toString();return c1(R2,n)}}function P4(e){var o={},t=e.filter&&typeof e.filter=="function";return o.query=e.query_params,function(i){i.doc||(i.doc={});var a=t&&la(e.filter,i.doc,o);if(typeof a=="object")return a;if(a)return!1;if(!e.include_docs)delete i.doc;else if(!e.attachments)for(var s in i.doc._attachments)Object.prototype.hasOwnProperty.call(i.doc._attachments,s)&&(i.doc._attachments[s].stub=!0);return!0}}function l6(e){var o;if(e?typeof e!="string"?o=c1(r6):/^_/.test(e)&&!/^_(design|local)/.test(e)&&(o=c1(sa)):o=c1(aa),o)throw o}function o0(e){return typeof e._remote=="boolean"?e._remote:typeof e.type=="function"?(K1("warn","db.type() is deprecated and will be removed in a future version of PouchDB"),e.type()==="http"):!1}function da(e,o){return"listenerCount"in e?e.listenerCount(o):i0.default.listenerCount(e,o)}function w4(e){if(!e)return null;var o=e.split("/");return o.length===2?o:o.length===1?[e,e]:null}function jn(e){var o=w4(e);return o?o.join("/"):null}var Hn=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],Vn="queryKey",va=/(?:^|&)([^&=]*)=?([^&]*)/g,ga=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/;function d6(e){for(var o=ga.exec(e),t={},n=14;n--;){var i=Hn[n],a=o[n]||"",s=["user","password"].indexOf(i)!==-1;t[i]=s?decodeURIComponent(a):a}return t[Vn]={},t[Hn[12]].replace(va,function(c,d,x){d&&(t[Vn][d]=x)}),t}function R4(e,o){var t=[],n=[];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(t.push(i),n.push(o[i]));return t.push(e),Function.apply(null,t).apply(null,n)}function I2(e,o,t){return e.get(o).catch(function(n){if(n.status!==404)throw n;return{}}).then(function(n){var i=n._rev,a=t(n);return a?(a._id=o,a._rev=i,ha(e,a,t)):{updated:!1,rev:i}})}function ha(e,o,t){return e.put(o).then(function(n){return{updated:!0,rev:n.rev}},function(n){if(n.status!==409)throw n;return I2(e,o._id,t)})}var q4=function(e){return atob(e)},s2=function(e){return btoa(e)};function F4(e,o){e=e||[],o=o||{};try{return new Blob(e,o)}catch(a){if(a.name!=="TypeError")throw a;for(var t=typeof BlobBuilder<"u"?BlobBuilder:typeof MSBlobBuilder<"u"?MSBlobBuilder:typeof MozBlobBuilder<"u"?MozBlobBuilder:WebKitBlobBuilder,n=new t,i=0;i<e.length;i+=1)n.append(e[i]);return n.getBlob(o.type)}}function ua(e){for(var o=e.length,t=new ArrayBuffer(o),n=new Uint8Array(t),i=0;i<o;i++)n[i]=e.charCodeAt(i);return t}function N4(e,o){return F4([ua(e)],{type:o})}function $4(e,o){return N4(q4(e),o)}function pa(e){for(var o="",t=new Uint8Array(e),n=t.byteLength,i=0;i<n;i++)o+=String.fromCharCode(t[i]);return o}function v6(e,o){var t=new FileReader,n=typeof t.readAsBinaryString=="function";t.onloadend=function(i){var a=i.target.result||"";if(n)return o(a);o(pa(a))},n?t.readAsBinaryString(e):t.readAsArrayBuffer(e)}function g6(e,o){v6(e,function(t){o(t)})}function U4(e,o){g6(e,function(t){o(s2(t))})}function ma(e,o){var t=new FileReader;t.onloadend=function(n){var i=n.target.result||new ArrayBuffer(0);o(i)},t.readAsArrayBuffer(e)}var wa=self.setImmediate||self.setTimeout,fa=32768;function xa(e){return s2(e)}function Ma(e,o,t,n,i){(t>0||n<o.size)&&(o=o.slice(t,n)),ma(o,function(a){e.append(a),i()})}function ka(e,o,t,n,i){(t>0||n<o.length)&&(o=o.substring(t,n)),e.appendBinary(o),i()}function G4(e,o){var t=typeof e=="string",n=t?e.length:e.size,i=Math.min(fa,n),a=Math.ceil(n/i),s=0,c=t?new A2.default:new A2.default.ArrayBuffer,d=t?ka:Ma;function x(){wa(l)}function k(){var M=c.end(!0),_=xa(M);o(_),c.destroy()}function l(){var M=s*i,_=M+i;s++,s<a?d(c,e,M,_,x):d(c,e,M,_,k)}l()}function h6(e){return A2.default.hash(e)}function u6(e,o){if(!o)return e2().replace(/-/g,"").toLowerCase();var t=Object.assign({},e);return delete t._rev_tree,h6(JSON.stringify(t))}var q2=e2;function A0(e){for(var o,t,n,i=e.rev_tree.slice(),a;a=i.pop();){var s=a.ids,c=s[2],d=a.pos;if(c.length){for(var x=0,k=c.length;x<k;x++)i.push({pos:d+1,ids:c[x]});continue}var l=!!s[1].deleted,M=s[0];(!o||(n!==l?n:t!==d?t<d:o<M))&&(o=M,t=d,n=l)}return t+"-"+o}function L0(e,o){for(var t=e.slice(),n;n=t.pop();)for(var i=n.pos,a=n.ids,s=a[2],c=o(s.length===0,i,a[0],n.ctx,a[1]),d=0,x=s.length;d<x;d++)t.push({pos:i+1,ids:s[d],ctx:c})}function za(e,o){return e.pos-o.pos}function K4(e){var o=[];L0(e,function(i,a,s,c,d){i&&o.push({rev:a+"-"+s,pos:a,opts:d})}),o.sort(za).reverse();for(var t=0,n=o.length;t<n;t++)delete o[t].pos;return o}function W4(e){for(var o=A0(e),t=K4(e.rev_tree),n=[],i=0,a=t.length;i<a;i++){var s=t[i];s.rev!==o&&!s.opts.deleted&&n.push(s.rev)}return n}function ya(e){var o=[];return L0(e.rev_tree,function(t,n,i,a,s){s.status==="available"&&!t&&(o.push(n+"-"+i),s.status="missing")}),o}function Ca(e,o){let t=[],n=e.slice(),i;for(;i=n.pop();){let{pos:a,ids:s}=i,c=`${a}-${s[0]}`,d=s[2];if(t.push(c),c===o){if(d.length!==0)throw new Error("The requested revision is not a leaf");return t.reverse()}(d.length===0||d.length>1)&&(t=[]);for(let x=0,k=d.length;x<k;x++)n.push({pos:a+1,ids:d[x]})}if(t.length===0)throw new Error("The requested revision does not exist");return t.reverse()}function p6(e){for(var o=[],t=e.slice(),n;n=t.pop();){var i=n.pos,a=n.ids,s=a[0],c=a[1],d=a[2],x=d.length===0,k=n.history?n.history.slice():[];k.push({id:s,opts:c}),x&&o.push({pos:i+1-k.length,ids:k});for(var l=0,M=d.length;l<M;l++)t.push({pos:i+1,ids:d[l],history:k})}return o.reverse()}function _a(e,o){return e.pos-o.pos}function Ba(e,o,t){for(var n=0,i=e.length,a;n<i;)a=n+i>>>1,t(e[a],o)<0?n=a+1:i=a;return n}function ba(e,o,t){var n=Ba(e,o,t);e.splice(n,0,o)}function On(e,o){for(var t,n,i=o,a=e.length;i<a;i++){var s=e[i],c=[s.id,s.opts,[]];n?(n[2].push(c),n=c):t=n=c}return t}function Sa(e,o){return e[0]<o[0]?-1:1}function Dn(e,o){for(var t=[{tree1:e,tree2:o}],n=!1;t.length>0;){var i=t.pop(),a=i.tree1,s=i.tree2;(a[1].status||s[1].status)&&(a[1].status=a[1].status==="available"||s[1].status==="available"?"available":"missing");for(var c=0;c<s[2].length;c++){if(!a[2][0]){n="new_leaf",a[2][0]=s[2][c];continue}for(var d=!1,x=0;x<a[2].length;x++)a[2][x][0]===s[2][c][0]&&(t.push({tree1:a[2][x],tree2:s[2][c]}),d=!0);d||(n="new_branch",ba(a[2],s[2][c],Sa))}}return{conflicts:n,tree:e}}function m6(e,o,t){var n=[],i=!1,a=!1,s;if(!e.length)return{tree:[o],conflicts:"new_leaf"};for(var c=0,d=e.length;c<d;c++){var x=e[c];if(x.pos===o.pos&&x.ids[0]===o.ids[0])s=Dn(x.ids,o.ids),n.push({pos:x.pos,ids:s.tree}),i=i||s.conflicts,a=!0;else if(t!==!0){var k=x.pos<o.pos?x:o,l=x.pos<o.pos?o:x,M=l.pos-k.pos,_=[],b=[];for(b.push({ids:k.ids,diff:M,parent:null,parentIdx:null});b.length>0;){var A=b.pop();if(A.diff===0){A.ids[0]===l.ids[0]&&_.push(A);continue}for(var y=A.ids[2],u=0,h=y.length;u<h;u++)b.push({ids:y[u],diff:A.diff-1,parent:A.ids,parentIdx:u})}var r=_[0];r?(s=Dn(r.ids,l.ids),r.parent[2][r.parentIdx]=s.tree,n.push({pos:k.pos,ids:k.ids}),i=i||s.conflicts,a=!0):n.push(x)}else n.push(x)}return a||n.push(o),n.sort(_a),{tree:n,conflicts:i||"internal_node"}}function Aa(e,o){for(var t=p6(e),n,i,a=0,s=t.length;a<s;a++){var c=t[a],d=c.ids,x;if(d.length>o){n||(n={});var k=d.length-o;x={pos:c.pos+k,ids:On(d,k)};for(var l=0;l<k;l++){var M=c.pos+l+"-"+d[l].id;n[M]=!0}}else x={pos:c.pos,ids:On(d,0)};i?i=m6(i,x,!0).tree:i=[x]}return n&&L0(i,function(_,b,A){delete n[b+"-"+A]}),{tree:i,revs:n?Object.keys(n):[]}}function w6(e,o,t){var n=m6(e,o),i=Aa(n.tree,t);return{tree:i.tree,stemmedRevs:i.revs,conflicts:n.conflicts}}function La(e,o){for(var t=e.slice(),n=o.split("-"),i=parseInt(n[0],10),a=n[1],s;s=t.pop();){if(s.pos===i&&s.ids[0]===a)return!0;for(var c=s.ids[2],d=0,x=c.length;d<x;d++)t.push({pos:s.pos+1,ids:c[d]})}return!1}function Ia(e){return e.ids}function c0(e,o){o||(o=A0(e));for(var t=o.substring(o.indexOf("-")+1),n=e.rev_tree.map(Ia),i;i=n.pop();){if(i[0]===t)return!!i[1].deleted;n=n.concat(i[2])}}function S0(e){return typeof e=="string"&&e.startsWith("_local/")}function ja(e,o){for(var t=o.rev_tree.slice(),n;n=t.pop();){var i=n.pos,a=n.ids,s=a[0],c=a[1],d=a[2],x=d.length===0,k=n.history?n.history.slice():[];if(k.push({id:s,pos:i,opts:c}),x)for(var l=0,M=k.length;l<M;l++){var _=k[l],b=_.pos+"-"+_.id;if(b===e)return i+"-"+s}for(var A=0,y=d.length;A<y;A++)t.push({pos:i+1,ids:d[A],history:k})}throw new Error("Unable to resolve latest revision for id "+o.id+", rev "+e)}function Ha(e,o,t,n){try{e.emit("change",o,t,n)}catch(i){K1("error",'Error in .on("change", function):',i)}}function Va(e,o,t){var n=[{rev:e._rev}];t.style==="all_docs"&&(n=K4(o.rev_tree).map(function(a){return{rev:a.rev}}));var i={id:o.id,changes:n,doc:e};return c0(o,e._rev)&&(i.deleted=!0),t.conflicts&&(i.doc._conflicts=W4(o),i.doc._conflicts.length||delete i.doc._conflicts),i}var f4=class extends i0.default{constructor(o,t,n){super(),this.db=o,t=t?V1(t):{};var i=t.complete=i6((c,d)=>{c?da(this,"error")>0&&this.emit("error",c):this.emit("complete",d),this.removeAllListeners(),o.removeListener("destroyed",a)});n&&(this.on("complete",function(c){n(null,c)}),this.on("error",n));let a=()=>{this.cancel()};o.once("destroyed",a),t.onChange=(c,d,x)=>{this.isCancelled||Ha(this,c,d,x)};var s=new Promise(function(c,d){t.complete=function(x,k){x?d(x):c(k)}});this.once("cancel",function(){o.removeListener("destroyed",a),t.complete(null,{status:"cancelled"})}),this.then=s.then.bind(s),this.catch=s.catch.bind(s),this.then(function(c){i(null,c)},i),o.taskqueue.isReady?this.validateChanges(t):o.taskqueue.addTask(c=>{c?t.complete(c):this.isCancelled?this.emit("cancel"):this.validateChanges(t)})}cancel(){this.isCancelled=!0,this.db.taskqueue.isReady&&this.emit("cancel")}validateChanges(o){var t=o.complete;d1._changesFilterPlugin?d1._changesFilterPlugin.validate(o,n=>{if(n)return t(n);this.doChanges(o)}):this.doChanges(o)}doChanges(o){var t=o.complete;if(o=V1(o),"live"in o&&!("continuous"in o)&&(o.continuous=o.live),o.processChange=Va,o.since==="latest"&&(o.since="now"),o.since||(o.since=0),o.since==="now"){this.db.info().then(i=>{if(this.isCancelled){t(null,{status:"cancelled"});return}o.since=i.update_seq,this.doChanges(o)},t);return}if(d1._changesFilterPlugin){if(d1._changesFilterPlugin.normalize(o),d1._changesFilterPlugin.shouldFilter(this,o))return d1._changesFilterPlugin.filter(this,o)}else["doc_ids","filter","selector","view"].forEach(function(i){i in o&&K1("warn",'The "'+i+'" option was passed in to changes/replicate, but pouchdb-changes-filter plugin is not installed, so it was ignored. Please install the plugin to enable filtering.')});"descending"in o||(o.descending=!1),o.limit=o.limit===0?1:o.limit,o.complete=t;var n=this.db._changes(o);if(n&&typeof n.cancel=="function"){let i=this.cancel;this.cancel=(...a)=>{n.cancel(),i.apply(this,a)}}}};function n4(e,o){return function(t,n){t||n[0]&&n[0].error?(t=t||n[0],t.docId=o,e(t)):e(null,n.length?n[0]:n)}}function Oa(e){for(var o=0;o<e.length;o++){var t=e[o];if(t._deleted)delete t._attachments;else if(t._attachments)for(var n=Object.keys(t._attachments),i=0;i<n.length;i++){var a=n[i];t._attachments[a]=a2(t._attachments[a],["data","digest","content_type","length","revpos","stub"])}}}function Da(e,o){if(e._id===o._id){let t=e._revisions?e._revisions.start:0,n=o._revisions?o._revisions.start:0;return t-n}return e._id<o._id?-1:1}function Ea(e){var o={},t=[];return L0(e,function(n,i,a,s){var c=i+"-"+a;return n&&(o[c]=0),s!==void 0&&t.push({from:s,to:c}),c}),t.reverse(),t.forEach(function(n){o[n.from]===void 0?o[n.from]=1+o[n.to]:o[n.from]=Math.min(o[n.from],1+o[n.to])}),o}function Ta(e){var o="limit"in e?e.keys.slice(e.skip,e.limit+e.skip):e.skip>0?e.keys.slice(e.skip):e.keys;e.keys=o,e.skip=0,delete e.limit,e.descending&&(o.reverse(),e.descending=!1)}function f6(e){var o=e._compactionQueue[0],t=o.opts,n=o.callback;e.get("_local/compaction").catch(function(){return!1}).then(function(i){i&&i.last_seq&&(t.last_seq=i.last_seq),e._compact(t,function(a,s){a?n(a):n(null,s),a0(function(){e._compactionQueue.shift(),e._compactionQueue.length&&f6(e)})})})}function Pa(e,o,t){return e.get("_local/purges").then(function(n){let i=n.purgeSeq+1;return n.purges.push({docId:o,rev:t,purgeSeq:i}),n.purges.length>self.purged_infos_limit&&n.purges.splice(0,n.purges.length-self.purged_infos_limit),n.purgeSeq=i,n}).catch(function(n){if(n.status!==404)throw n;return{_id:"_local/purges",purges:[{docId:o,rev:t,purgeSeq:0}],purgeSeq:0}}).then(function(n){return e.put(n)})}function Ra(e){return e.charAt(0)==="_"?e+" is not a valid attachment name, attachment names cannot start with '_'":!1}function o4(e){return e===null||typeof e!="object"||Array.isArray(e)}var qa=/^\d+-[^-]*$/;function i4(e){return typeof e=="string"&&qa.test(e)}var j2=class extends i0.default{_setup(){this.post=A1("post",function(o,t,n){if(typeof t=="function"&&(n=t,t={}),o4(o))return n(c1(t4));this.bulkDocs({docs:[o]},t,n4(n,o._id))}).bind(this),this.put=A1("put",function(o,t,n){if(typeof t=="function"&&(n=t,t={}),o4(o))return n(c1(t4));if(l6(o._id),"_rev"in o&&!i4(o._rev))return n(c1(b2));if(S0(o._id)&&typeof this._putLocal=="function")return o._deleted?this._removeLocal(o,n):this._putLocal(o,n);let i=s=>{typeof this._put=="function"&&t.new_edits!==!1?this._put(o,t,s):this.bulkDocs({docs:[o]},t,n4(s,o._id))};t.force&&o._rev?(a(),i(function(s){var c=s?null:{ok:!0,id:o._id,rev:o._rev};n(s,c)})):i(n);function a(){var s=o._rev.split("-"),c=s[1],d=parseInt(s[0],10),x=d+1,k=u6();o._revisions={start:x,ids:[k,c]},o._rev=x+"-"+k,t.new_edits=!1}}).bind(this),this.putAttachment=A1("putAttachment",function(o,t,n,i,a){var s=this;typeof a=="function"&&(a=i,i=n,n=null),typeof a>"u"&&(a=i,i=n,n=null),a||K1("warn","Attachment",t,"on document",o,"is missing content_type");function c(d){var x="_rev"in d?parseInt(d._rev,10):0;return d._attachments=d._attachments||{},d._attachments[t]={content_type:a,data:i,revpos:++x},s.put(d)}return s.get(o).then(function(d){if(d._rev!==n)throw c1(F0);return c(d)},function(d){if(d.reason===U1.message)return c({_id:o});throw d})}).bind(this),this.removeAttachment=A1("removeAttachment",function(o,t,n,i){this.get(o,(a,s)=>{if(a){i(a);return}if(s._rev!==n){i(c1(F0));return}if(!s._attachments)return i();delete s._attachments[t],Object.keys(s._attachments).length===0&&delete s._attachments,this.put(s,i)})}).bind(this),this.remove=A1("remove",function(o,t,n,i){var a;typeof t=="string"?(a={_id:o,_rev:t},typeof n=="function"&&(i=n,n={})):(a=o,typeof t=="function"?(i=t,n={}):(i=n,n=t)),n=n||{},n.was_delete=!0;var s={_id:a._id,_rev:a._rev||n.rev};if(s._deleted=!0,S0(s._id)&&typeof this._removeLocal=="function")return this._removeLocal(a,i);this.bulkDocs({docs:[s]},n,n4(i,s._id))}).bind(this),this.revsDiff=A1("revsDiff",function(o,t,n){typeof t=="function"&&(n=t,t={});var i=Object.keys(o);if(!i.length)return n(null,{});var a=0,s=new Map;function c(x,k){s.has(x)||s.set(x,{missing:[]}),s.get(x).missing.push(k)}function d(x,k){var l=o[x].slice(0);L0(k,function(M,_,b,A,y){var u=_+"-"+b,h=l.indexOf(u);h!==-1&&(l.splice(h,1),y.status!=="available"&&c(x,u))}),l.forEach(function(M){c(x,M)})}i.forEach(function(x){this._getRevisionTree(x,function(k,l){if(k&&k.status===404&&k.message==="missing")s.set(x,{missing:o[x]});else{if(k)return n(k);d(x,l)}if(++a===i.length){var M={};return s.forEach(function(_,b){M[b]=_}),n(null,M)}})},this)}).bind(this),this.bulkGet=A1("bulkGet",function(o,t){s6(this,o,t)}).bind(this),this.compactDocument=A1("compactDocument",function(o,t,n){this._getRevisionTree(o,(i,a)=>{if(i)return n(i);var s=Ea(a),c=[],d=[];Object.keys(s).forEach(function(x){s[x]>t&&c.push(x)}),L0(a,function(x,k,l,M,_){var b=k+"-"+l;_.status==="available"&&c.indexOf(b)!==-1&&d.push(b)}),this._doCompaction(o,d,n)})}).bind(this),this.compact=A1("compact",function(o,t){typeof o=="function"&&(t=o,o={}),o=o||{},this._compactionQueue=this._compactionQueue||[],this._compactionQueue.push({opts:o,callback:t}),this._compactionQueue.length===1&&f6(this)}).bind(this),this.get=A1("get",function(o,t,n){if(typeof t=="function"&&(n=t,t={}),t=t||{},typeof o!="string")return n(c1(r6));if(S0(o)&&typeof this._getLocal=="function")return this._getLocal(o,n);var i=[];let a=()=>{var d=[],x=i.length;if(!x)return n(null,d);i.forEach(k=>{this.get(o,{rev:k,revs:t.revs,latest:t.latest,attachments:t.attachments,binary:t.binary},function(l,M){if(l)d.push({missing:k});else{for(var _,b=0,A=d.length;b<A;b++)if(d[b].ok&&d[b].ok._rev===M._rev){_=!0;break}_||d.push({ok:M})}x--,x||n(null,d)})})};if(t.open_revs){if(t.open_revs==="all")this._getRevisionTree(o,function(d,x){if(d)return n(d);i=K4(x).map(function(k){return k.rev}),a()});else if(Array.isArray(t.open_revs)){i=t.open_revs;for(var s=0;s<i.length;s++){var c=i[s];if(!i4(c))return n(c1(b2))}a()}else return n(c1(E4,"function_clause"));return}return this._get(o,t,(d,x)=>{if(d)return d.docId=o,n(d);var k=x.doc,l=x.metadata,M=x.ctx;if(t.conflicts){var _=W4(l);_.length&&(k._conflicts=_)}if(c0(l,k._rev)&&(k._deleted=!0),t.revs||t.revs_info){for(var b=k._rev.split("-"),A=parseInt(b[0],10),y=b[1],u=p6(l.rev_tree),h=null,r=0;r<u.length;r++){var v=u[r];let e1=v.ids.findIndex(i1=>i1.id===y);var w=e1===A-1;(w||!h&&e1!==-1)&&(h=v)}if(!h)return d=new Error("invalid rev tree"),d.docId=o,n(d);let Y=k._rev.split("-")[1],r1=h.ids.findIndex(e1=>e1.id===Y)+1;var f=h.ids.length-r1;if(h.ids.splice(r1,f),h.ids.reverse(),t.revs&&(k._revisions={start:h.pos+h.ids.length-1,ids:h.ids.map(function(e1){return e1.id})}),t.revs_info){var O=h.pos+h.ids.length;k._revs_info=h.ids.map(function(e1){return O--,{rev:O+"-"+e1.id,status:e1.opts.status}})}}if(t.attachments&&k._attachments){var E=k._attachments,V=Object.keys(E).length;if(V===0)return n(null,k);Object.keys(E).forEach(Y=>{this._getAttachment(k._id,Y,E[Y],{binary:t.binary,metadata:l,ctx:M},function(r1,e1){var i1=k._attachments[Y];i1.data=e1,delete i1.stub,delete i1.length,--V||n(null,k)})})}else{if(k._attachments)for(var n1 in k._attachments)Object.prototype.hasOwnProperty.call(k._attachments,n1)&&(k._attachments[n1].stub=!0);n(null,k)}})}).bind(this),this.getAttachment=A1("getAttachment",function(o,t,n,i){n instanceof Function&&(i=n,n={}),this._get(o,n,(a,s)=>{if(a)return i(a);if(s.doc._attachments&&s.doc._attachments[t])n.ctx=s.ctx,n.binary=!0,n.metadata=s.metadata,this._getAttachment(o,t,s.doc._attachments[t],n,i);else return i(c1(U1))})}).bind(this),this.allDocs=A1("allDocs",function(o,t){if(typeof o=="function"&&(t=o,o={}),o.skip=typeof o.skip<"u"?o.skip:0,o.start_key&&(o.startkey=o.start_key),o.end_key&&(o.endkey=o.end_key),"keys"in o){if(!Array.isArray(o.keys))return t(new TypeError("options.keys must be an array"));var n=["startkey","endkey","key"].filter(function(i){return i in o})[0];if(n){t(c1(ra,"Query parameter `"+n+"` is not compatible with multi-get"));return}if(!o0(this)&&(Ta(o),o.keys.length===0))return this._allDocs({limit:0},t)}return this._allDocs(o,t)}).bind(this),this.close=A1("close",function(o){return this._closed=!0,this.emit("closed"),this._close(o)}).bind(this),this.info=A1("info",function(o){this._info((t,n)=>{if(t)return o(t);n.db_name=n.db_name||this.name,n.auto_compaction=!!(this.auto_compaction&&!o0(this)),n.adapter=this.adapter,o(null,n)})}).bind(this),this.id=A1("id",function(o){return this._id(o)}).bind(this),this.bulkDocs=A1("bulkDocs",function(o,t,n){if(typeof t=="function"&&(n=t,t={}),t=t||{},Array.isArray(o)&&(o={docs:o}),!o||!o.docs||!Array.isArray(o.docs))return n(c1(ia));for(var i=0;i<o.docs.length;++i){let d=o.docs[i];if(o4(d))return n(c1(t4));if("_rev"in d&&!i4(d._rev))return n(c1(b2))}var a;if(o.docs.forEach(function(d){d._attachments&&Object.keys(d._attachments).forEach(function(x){a=a||Ra(x),d._attachments[x].content_type||K1("warn","Attachment",x,"on document",d._id,"is missing content_type")})}),a)return n(c1(R2,a));"new_edits"in t||("new_edits"in o?t.new_edits=o.new_edits:t.new_edits=!0);var s=this;!t.new_edits&&!o0(s)&&o.docs.sort(Da),Oa(o.docs);var c=o.docs.map(function(d){return d._id});this._bulkDocs(o,t,function(d,x){if(d)return n(d);if(t.new_edits||(x=x.filter(function(M){return M.error})),!o0(s))for(var k=0,l=x.length;k<l;k++)x[k].id=x[k].id||c[k];n(null,x)})}).bind(this),this.registerDependentDatabase=A1("registerDependentDatabase",function(o,t){var n=V1(this.__opts);this.__opts.view_adapter&&(n.adapter=this.__opts.view_adapter);var i=new this.constructor(o,n);function a(s){return s.dependentDbs=s.dependentDbs||{},s.dependentDbs[o]?!1:(s.dependentDbs[o]=!0,s)}I2(this,"_local/_pouch_dependentDbs",a).then(function(){t(null,{db:i})}).catch(t)}).bind(this),this.destroy=A1("destroy",function(o,t){typeof o=="function"&&(t=o,o={});var n="use_prefix"in this?this.use_prefix:!0;let i=()=>{this._destroy(o,(a,s)=>{if(a)return t(a);this._destroyed=!0,this.emit("destroyed"),t(null,s||{ok:!0})})};if(o0(this))return i();this.get("_local/_pouch_dependentDbs",(a,s)=>{if(a)return a.status!==404?t(a):i();var c=s.dependentDbs,d=this.constructor,x=Object.keys(c).map(k=>{var l=n?k.replace(new RegExp("^"+d.prefix),""):k;return new d(l,this.__opts).destroy()});Promise.all(x).then(i,t)})}).bind(this)}_compact(o,t){var n={return_docs:!1,last_seq:o.last_seq||0,since:o.last_seq||0},i=[],a,s=0;let c=k=>{this.activeTasks.update(a,{completed_items:++s}),i.push(this.compactDocument(k.id,0))},d=k=>{this.activeTasks.remove(a,k),t(k)},x=k=>{var l=k.last_seq;Promise.all(i).then(()=>I2(this,"_local/compaction",M=>!M.last_seq||M.last_seq<l?(M.last_seq=l,M):!1)).then(()=>{this.activeTasks.remove(a),t(null,{ok:!0})}).catch(d)};this.info().then(k=>{a=this.activeTasks.add({name:"database_compaction",total_items:k.update_seq-n.last_seq}),this.changes(n).on("change",c).on("complete",x).on("error",d)})}changes(o,t){return typeof o=="function"&&(t=o,o={}),o=o||{},o.return_docs="return_docs"in o?o.return_docs:!o.live,new f4(this,o,t)}type(){return typeof this._type=="function"?this._type():this.adapter}};j2.prototype.purge=A1("_purge",function(e,o,t){if(typeof this._purge>"u")return t(c1(E4,"Purge is not implemented in the "+this.adapter+" adapter."));var n=this;n._getRevisionTree(e,(i,a)=>{if(i)return t(i);if(!a)return t(c1(U1));let s;try{s=Ca(a,o)}catch(c){return t(c.message||c)}n._purge(e,s,(c,d)=>{if(c)return t(c);Pa(n,e,o).then(function(){return t(null,d)})})})});var x4=class{constructor(){this.isReady=!1,this.failed=!1,this.queue=[]}execute(){var o;if(this.failed)for(;o=this.queue.shift();)o(this.failed);else for(;o=this.queue.shift();)o()}fail(o){this.failed=o,this.execute()}ready(o){this.isReady=!0,this.db=o,this.execute()}addTask(o){this.queue.push(o),this.failed&&this.execute()}};function Fa(e,o){var t=e.match(/([a-z-]*):\/\/(.*)/);if(t)return{name:/https?/.test(t[1])?t[1]+"://"+t[2]:t[2],adapter:t[1]};var n=d1.adapters,i=d1.preferredAdapters,a=d1.prefix,s=o.adapter;if(!s)for(var c=0;c<i.length;++c){if(s=i[c],s==="idb"&&"websql"in n&&L2()&&localStorage["_pouch__websqldb_"+a+e]){K1("log",'PouchDB is downgrading "'+e+'" to WebSQL to avoid data loss, because it was already opened with WebSQL.');continue}break}var d=n[s],x=d&&"use_prefix"in d?d.use_prefix:!0;return{name:x?a+e:e,adapter:s}}function Na(e,o){e.prototype=Object.create(o.prototype,{constructor:{value:e}})}function x6(e,o){let t=function(...n){if(!(this instanceof t))return new t(...n);o.apply(this,n)};return Na(t,e),t}function $a(e){function o(n){e.removeListener("closed",t),n||e.constructor.emit("destroyed",e.name)}function t(){e.removeListener("destroyed",o),e.constructor.emit("unref",e)}e.once("destroyed",o),e.once("closed",t),e.constructor.emit("ref",e)}var H2=class extends j2{constructor(o,t){super(),this._setup(o,t)}_setup(o,t){if(super._setup(),t=t||{},o&&typeof o=="object"&&(t=o,o=t.name,delete t.name),t.deterministic_revs===void 0&&(t.deterministic_revs=!0),this.__opts=t=V1(t),this.auto_compaction=t.auto_compaction,this.purged_infos_limit=t.purged_infos_limit||1e3,this.prefix=d1.prefix,typeof o!="string")throw new Error("Missing/invalid DB name");var n=(t.prefix||"")+o,i=Fa(n,t);if(t.name=i.name,t.adapter=t.adapter||i.adapter,this.name=o,this._adapter=t.adapter,d1.emit("debug",["adapter","Picked adapter: ",t.adapter]),!d1.adapters[t.adapter]||!d1.adapters[t.adapter].valid())throw new Error("Invalid Adapter: "+t.adapter);if(t.view_adapter&&(!d1.adapters[t.view_adapter]||!d1.adapters[t.view_adapter].valid()))throw new Error("Invalid View Adapter: "+t.view_adapter);this.taskqueue=new x4,this.adapter=t.adapter,d1.adapters[t.adapter].call(this,t,a=>{if(a)return this.taskqueue.fail(a);$a(this),this.emit("created",this),d1.emit("created",this.name),this.taskqueue.ready(this)})}},d1=x6(H2,function(e,o){H2.prototype._setup.call(this,e,o)}),M6=fetch,R0=Headers,M4=class{constructor(){this.tasks={}}list(){return Object.values(this.tasks)}add(o){let t=e2();return this.tasks[t]={id:t,name:o.name,total_items:o.total_items,created_at:new Date().toJSON()},t}get(o){return this.tasks[o]}remove(o,t){return delete this.tasks[o],this.tasks}update(o,t){let n=this.tasks[o];if(typeof n<"u"){let i={id:n.id,name:n.name,created_at:n.created_at,total_items:t.total_items||n.total_items,completed_items:t.completed_items||n.completed_items,updated_at:new Date().toJSON()};this.tasks[o]=i}return this.tasks}};d1.adapters={};d1.preferredAdapters=[];d1.prefix="_pouch_";var En=new i0.default;function Ua(e){Object.keys(i0.default.prototype).forEach(function(t){typeof i0.default.prototype[t]=="function"&&(e[t]=En[t].bind(En))});var o=e._destructionListeners=new Map;e.on("ref",function(n){o.has(n.name)||o.set(n.name,[]),o.get(n.name).push(n)}),e.on("unref",function(n){if(o.has(n.name)){var i=o.get(n.name),a=i.indexOf(n);a<0||(i.splice(a,1),i.length>1?o.set(n.name,i):o.delete(n.name))}}),e.on("destroyed",function(n){if(o.has(n)){var i=o.get(n);o.delete(n),i.forEach(function(a){a.emit("destroyed",!0)})}})}Ua(d1);d1.adapter=function(e,o,t){o.valid()&&(d1.adapters[e]=o,t&&d1.preferredAdapters.push(e))};d1.plugin=function(e){if(typeof e=="function")e(d1);else{if(typeof e!="object"||Object.keys(e).length===0)throw new Error('Invalid plugin: got "'+e+'", expected an object or a function');Object.keys(e).forEach(function(o){d1.prototype[o]=e[o]})}return this.__defaults&&(d1.__defaults=Object.assign({},this.__defaults)),d1};d1.defaults=function(e){let o=x6(d1,function(t,n){n=n||{},t&&typeof t=="object"&&(n=t,t=n.name,delete n.name),n=Object.assign({},o.__defaults,n),d1.call(this,t,n)});return o.preferredAdapters=d1.preferredAdapters.slice(),Object.keys(d1).forEach(function(t){t in o||(o[t]=d1[t])}),o.__defaults=Object.assign({},this.__defaults,e),o};d1.fetch=function(e,o){return M6(e,o)};d1.prototype.activeTasks=d1.activeTasks=new M4;var Ga="9.0.0";function Z4(e,o){for(var t=e,n=0,i=o.length;n<i;n++){var a=o[n];if(t=t[a],!t)break}return t}function Ka(e,o){return e<o?-1:e>o?1:0}function Q4(e){for(var o=[],t="",n=0,i=e.length;n<i;n++){var a=e[n];n>0&&e[n-1]==="\\"&&(a==="$"||a===".")?t=t.substring(0,t.length-1)+a:a==="."?(o.push(t),t=""):t+=a}return o.push(t),o}var Wa=["$or","$nor","$not"];function k6(e){return Wa.indexOf(e)>-1}function z6(e){return Object.keys(e)[0]}function Za(e){return e[z6(e)]}function o2(e){var o={},t={$or:!0,$nor:!0};return e.forEach(function(n){Object.keys(n).forEach(function(i){var a=n[i];if(typeof a!="object"&&(a={$eq:a}),k6(i))if(a instanceof Array){if(t[i]){t[i]=!1,o[i]=a;return}var s=[];o[i].forEach(function(d){Object.keys(a).forEach(function(x){var k=a[x],l=Math.max(Object.keys(d).length,Object.keys(k).length),M=o2([d,k]);Object.keys(M).length<=l||s.push(M)})}),o[i]=s}else o[i]=o2([a]);else{var c=o[i]=o[i]||{};Object.keys(a).forEach(function(d){var x=a[d];if(d==="$gt"||d==="$gte")return Qa(d,x,c);if(d==="$lt"||d==="$lte")return Xa(d,x,c);if(d==="$ne")return Ja(x,c);if(d==="$eq")return Ya(x,c);if(d==="$regex")return e5(x,c);c[d]=x})}})}),o}function Qa(e,o,t){typeof t.$eq<"u"||(typeof t.$gte<"u"?e==="$gte"?o>t.$gte&&(t.$gte=o):o>=t.$gte&&(delete t.$gte,t.$gt=o):typeof t.$gt<"u"?e==="$gte"?o>t.$gt&&(delete t.$gt,t.$gte=o):o>t.$gt&&(t.$gt=o):t[e]=o)}function Xa(e,o,t){typeof t.$eq<"u"||(typeof t.$lte<"u"?e==="$lte"?o<t.$lte&&(t.$lte=o):o<=t.$lte&&(delete t.$lte,t.$lt=o):typeof t.$lt<"u"?e==="$lte"?o<t.$lt&&(delete t.$lt,t.$lte=o):o<t.$lt&&(t.$lt=o):t[e]=o)}function Ja(e,o){"$ne"in o?o.$ne.push(e):o.$ne=[e]}function Ya(e,o){delete o.$gt,delete o.$gte,delete o.$lt,delete o.$lte,delete o.$ne,o.$eq=e}function e5(e,o){"$regex"in o?o.$regex.push(e):o.$regex=[e]}function y6(e){for(var o in e){if(Array.isArray(e))for(var t in e)e[t].$and&&(e[t]=o2(e[t].$and));var n=e[o];typeof n=="object"&&y6(n)}return e}function C6(e,o){for(var t in e){t==="$and"&&(o=!0);var n=e[t];typeof n=="object"&&(o=C6(n,o))}return o}function t5(e){var o=V1(e);C6(o,!1)&&(o=y6(o),"$and"in o&&(o=o2(o.$and))),["$or","$nor"].forEach(function(s){s in o&&o[s].forEach(function(c){for(var d=Object.keys(c),x=0;x<d.length;x++){var k=d[x],l=c[k];(typeof l!="object"||l===null)&&(c[k]={$eq:l})}})}),"$not"in o&&(o.$not=o2([o.$not]));for(var t=Object.keys(o),n=0;n<t.length;n++){var i=t[n],a=o[i];(typeof a!="object"||a===null)&&(a={$eq:a}),o[i]=a}return k4(o),o}function k4(e){Object.keys(e).forEach(function(o){var t=e[o];Array.isArray(t)?t.forEach(function(n){n&&typeof n=="object"&&k4(n)}):o==="$ne"?e.$ne=[t]:o==="$regex"?e.$regex=[t]:t&&typeof t=="object"&&k4(t)})}function n5(e,o,t){for(var n="",i=t-e.length;n.length<i;)n+=o;return n}function o5(e,o,t){var n=n5(e,o,t);return n+e}var _6=-324,z4=3,y4="";function L1(e,o){if(e===o)return 0;e=I0(e),o=I0(o);var t=C4(e),n=C4(o);if(t-n!==0)return t-n;switch(typeof e){case"number":return e-o;case"boolean":return e<o?-1:1;case"string":return l5(e,o)}return Array.isArray(e)?c5(e,o):d5(e,o)}function I0(e){switch(typeof e){case"undefined":return null;case"number":return e===1/0||e===-1/0||isNaN(e)?null:e;case"object":var o=e;if(Array.isArray(e)){var t=e.length;e=new Array(t);for(var n=0;n<t;n++)e[n]=I0(o[n])}else{if(e instanceof Date)return e.toJSON();if(e!==null){e={};for(var i in o)if(Object.prototype.hasOwnProperty.call(o,i)){var a=o[i];typeof a<"u"&&(e[i]=I0(a))}}}}return e}function i5(e){if(e!==null)switch(typeof e){case"boolean":return e?1:0;case"number":return v5(e);case"string":return e.replace(/\u0002/g,"").replace(/\u0001/g,"").replace(/\u0000/g,"");case"object":var o=Array.isArray(e),t=o?e:Object.keys(e),n=-1,i=t.length,a="";if(o)for(;++n<i;)a+=X1(t[n]);else for(;++n<i;){var s=t[n];a+=X1(s)+X1(e[s])}return a}return""}function X1(e){var o="\0";return e=I0(e),C4(e)+y4+i5(e)+o}function a5(e,o){var t=o,n,i=e[o]==="1";if(i)n=0,o++;else{var a=e[o]==="0";o++;var s="",c=e.substring(o,o+z4),d=parseInt(c,10)+_6;for(a&&(d=-d),o+=z4;;){var x=e[o];if(x==="\0")break;s+=x,o++}s=s.split("."),s.length===1?n=parseInt(s,10):n=parseFloat(s[0]+"."+s[1]),a&&(n=n-10),d!==0&&(n=parseFloat(n+"e"+d))}return{num:n,length:o-t}}function s5(e,o){var t=e.pop();if(o.length){var n=o[o.length-1];t===n.element&&(o.pop(),n=o[o.length-1]);var i=n.element,a=n.index;if(Array.isArray(i))i.push(t);else if(a===e.length-2){var s=e.pop();i[s]=t}else e.push(t)}}function r5(e){for(var o=[],t=[],n=0;;){var i=e[n++];if(i==="\0"){if(o.length===1)return o.pop();s5(o,t);continue}switch(i){case"1":o.push(null);break;case"2":o.push(e[n]==="1"),n++;break;case"3":var a=a5(e,n);o.push(a.num),n+=a.length;break;case"4":for(var s="";;){var c=e[n];if(c==="\0")break;s+=c,n++}s=s.replace(/\u0001\u0001/g,"\0").replace(/\u0001\u0002/g,"").replace(/\u0002\u0002/g,""),o.push(s);break;case"5":var d={element:[],index:o.length};o.push(d.element),t.push(d);break;case"6":var x={element:{},index:o.length};o.push(x.element),t.push(x);break;default:throw new Error("bad collationIndex or unexpectedly reached end of input: "+i)}}}function c5(e,o){for(var t=Math.min(e.length,o.length),n=0;n<t;n++){var i=L1(e[n],o[n]);if(i!==0)return i}return e.length===o.length?0:e.length>o.length?1:-1}function l5(e,o){return e===o?0:e>o?1:-1}function d5(e,o){for(var t=Object.keys(e),n=Object.keys(o),i=Math.min(t.length,n.length),a=0;a<i;a++){var s=L1(t[a],n[a]);if(s!==0||(s=L1(e[t[a]],o[n[a]]),s!==0))return s}return t.length===n.length?0:t.length>n.length?1:-1}function C4(e){var o=["boolean","number","string","object"],t=o.indexOf(typeof e);if(~t)return e===null?1:Array.isArray(e)?5:t<3?t+2:t+3;if(Array.isArray(e))return 5}function v5(e){if(e===0)return"1";var o=e.toExponential().split(/e\+?/),t=parseInt(o[1],10),n=e<0,i=n?"0":"2",a=(n?-t:t)-_6,s=o5(a.toString(),"0",z4);i+=y4+s;var c=Math.abs(parseFloat(o[0]));n&&(c=10-c);var d=c.toFixed(20);return d=d.replace(/\.?0+$/,""),i+=y4+d,i}function g5(e){function o(t){return e.map(function(n){var i=z6(n),a=Q4(i),s=Z4(t,a);return s})}return function(t,n){var i=o(t.doc),a=o(n.doc),s=L1(i,a);return s!==0?s:Ka(t.doc._id,n.doc._id)}}function h5(e,o,t){if(e=e.filter(function(s){return q0(s.doc,o.selector,t)}),o.sort){var n=g5(o.sort);e=e.sort(n),typeof o.sort[0]!="string"&&Za(o.sort[0])==="desc"&&(e=e.reverse())}if("limit"in o||"skip"in o){var i=o.skip||0,a=("limit"in o?o.limit:e.length)+i;e=e.slice(i,a)}return e}function q0(e,o,t){return t.every(function(n){var i=o[n],a=Q4(n),s=Z4(e,a);return k6(n)?u5(n,i,e):V2(i,e,a,s)})}function V2(e,o,t,n){return e?typeof e=="object"?Object.keys(e).every(function(i){var a=e[i];if(i.indexOf("$")===0)return Tn(i,o,a,t,n);var s=Q4(i);if(n===void 0&&typeof a!="object"&&s.length>0)return!1;var c=Z4(n,s);return typeof a=="object"?V2(a,o,t,c):Tn("$eq",o,a,s,c)}):e===n:!0}function u5(e,o,t){return e==="$or"?o.some(function(n){return q0(t,n,Object.keys(n))}):e==="$not"?!q0(t,o,Object.keys(o)):!o.find(function(n){return q0(t,n,Object.keys(n))})}function Tn(e,o,t,n,i){if(!Rn[e])throw new Error('unknown operator "'+e+'" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, $nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');return Rn[e](o,t,n,i)}function t2(e){return typeof e<"u"&&e!==null}function y0(e){return typeof e<"u"}function p5(e,o){if(typeof e!="number"||parseInt(e,10)!==e)return!1;var t=o[0],n=o[1];return e%t===n}function Pn(e,o){return o.some(function(t){return e instanceof Array?e.some(function(n){return L1(t,n)===0}):L1(t,e)===0})}function m5(e,o){return o.every(function(t){return e.some(function(n){return L1(t,n)===0})})}function w5(e,o){return e.length===o}function f5(e,o){var t=new RegExp(o);return t.test(e)}function x5(e,o){switch(o){case"null":return e===null;case"boolean":return typeof e=="boolean";case"number":return typeof e=="number";case"string":return typeof e=="string";case"array":return e instanceof Array;case"object":return{}.toString.call(e)==="[object Object]"}}var Rn={$elemMatch:function(e,o,t,n){return!Array.isArray(n)||n.length===0?!1:typeof n[0]=="object"&&n[0]!==null?n.some(function(i){return q0(i,o,Object.keys(o))}):n.some(function(i){return V2(o,e,t,i)})},$allMatch:function(e,o,t,n){return!Array.isArray(n)||n.length===0?!1:typeof n[0]=="object"&&n[0]!==null?n.every(function(i){return q0(i,o,Object.keys(o))}):n.every(function(i){return V2(o,e,t,i)})},$eq:function(e,o,t,n){return y0(n)&&L1(n,o)===0},$gte:function(e,o,t,n){return y0(n)&&L1(n,o)>=0},$gt:function(e,o,t,n){return y0(n)&&L1(n,o)>0},$lte:function(e,o,t,n){return y0(n)&&L1(n,o)<=0},$lt:function(e,o,t,n){return y0(n)&&L1(n,o)<0},$exists:function(e,o,t,n){return o?y0(n):!y0(n)},$mod:function(e,o,t,n){return t2(n)&&p5(n,o)},$ne:function(e,o,t,n){return o.every(function(i){return L1(n,i)!==0})},$in:function(e,o,t,n){return t2(n)&&Pn(n,o)},$nin:function(e,o,t,n){return t2(n)&&!Pn(n,o)},$size:function(e,o,t,n){return t2(n)&&Array.isArray(n)&&w5(n,o)},$all:function(e,o,t,n){return Array.isArray(n)&&m5(n,o)},$regex:function(e,o,t,n){return t2(n)&&typeof n=="string"&&o.every(function(i){return f5(n,i)})},$type:function(e,o,t,n){return x5(n,o)}};function M5(e,o){if(typeof o!="object")throw new Error("Selector error: expected a JSON object");o=t5(o);var t={doc:e},n=h5([t],{selector:o},Object.keys(o));return n&&n.length===1}function k5(e){return R4(`"use strict";
return `+e+";",{})}function z5(e){var o=["return function(doc) {",'  "use strict";',"  var emitted = false;","  var emit = function (a, b) {","    emitted = true;","  };","  var view = "+e+";","  view(doc);","  if (emitted) {","    return true;","  }","};"].join(`
`);return R4(o,{})}function y5(e,o){if(e.selector&&e.filter&&e.filter!=="_selector"){var t=typeof e.filter=="string"?e.filter:"function";return o(new Error('selector invalid for filter "'+t+'"'))}o()}function C5(e){e.view&&!e.filter&&(e.filter="_view"),e.selector&&!e.filter&&(e.filter="_selector"),e.filter&&typeof e.filter=="string"&&(e.filter==="_view"?e.view=jn(e.view):e.filter=jn(e.filter))}function _5(e,o){return o.filter&&typeof o.filter=="string"&&!o.doc_ids&&!o0(e.db)}function B5(e,o){var t=o.complete;if(o.filter==="_view"){if(!o.view||typeof o.view!="string"){var n=c1(R2,"`view` filter parameter not found or invalid.");return t(n)}var i=w4(o.view);e.db.get("_design/"+i[0],function(s,c){if(e.isCancelled)return t(null,{status:"cancelled"});if(s)return t(N0(s));var d=c&&c.views&&c.views[i[1]]&&c.views[i[1]].map;if(!d)return t(c1(U1,c.views?"missing json key: "+i[1]:"missing json key: views"));o.filter=z5(d),e.doChanges(o)})}else if(o.selector)o.filter=function(s){return M5(s,o.selector)},e.doChanges(o);else{var a=w4(o.filter);e.db.get("_design/"+a[0],function(s,c){if(e.isCancelled)return t(null,{status:"cancelled"});if(s)return t(N0(s));var d=c&&c.filters&&c.filters[a[1]];if(!d)return t(c1(U1,c&&c.filters?"missing json key: "+a[1]:"missing json key: filters"));o.filter=k5(d),e.doChanges(o)})}}function b5(e){e._changesFilterPlugin={validate:y5,normalize:C5,shouldFilter:_5,filter:B5}}d1.plugin(b5);d1.version=Ga;function S5(e,o,t){return new Promise(function(n){var i=F4([""]);let a;if(typeof t=="function"){let c=t(i);a=e.objectStore(o).put(c)}else{let s=t;a=e.objectStore(o).put(i,s)}a.onsuccess=function(){var s=navigator.userAgent.match(/Chrome\/(\d+)/),c=navigator.userAgent.match(/Edge\//);n(c||!s||parseInt(s[1],10)>=43)},a.onerror=e.onabort=function(s){s.preventDefault(),s.stopPropagation(),n(!1)}}).catch(function(){return!1})}function B6(e){return e.reduce(function(o,t){return o[t]=!0,o},{})}var A5=B6(["_id","_rev","_access","_attachments","_deleted","_revisions","_revs_info","_conflicts","_deleted_conflicts","_local_seq","_rev_tree","_replication_id","_replication_state","_replication_state_time","_replication_state_reason","_replication_stats","_removed"]),L5=B6(["_access","_attachments","_replication_id","_replication_state","_replication_state_time","_replication_state_reason","_replication_stats"]);function qn(e){if(!/^\d+-/.test(e))return c1(b2);var o=e.indexOf("-"),t=e.substring(0,o),n=e.substring(o+1);return{prefix:parseInt(t,10),id:n}}function I5(e,o){for(var t=e.start-e.ids.length+1,n=e.ids,i=[n[0],o,[]],a=1,s=n.length;a<s;a++)i=[n[a],{status:"missing"},[i]];return[{pos:t,ids:i}]}function b6(e,o,t){t||(t={deterministic_revs:!0});var n,i,a,s={status:"available"};if(e._deleted&&(s.deleted=!0),o)if(e._id||(e._id=q2()),i=u6(e,t.deterministic_revs),e._rev){if(a=qn(e._rev),a.error)return a;e._rev_tree=[{pos:a.prefix,ids:[a.id,{status:"missing"},[[i,s,[]]]]}],n=a.prefix+1}else e._rev_tree=[{pos:1,ids:[i,s,[]]}],n=1;else if(e._revisions&&(e._rev_tree=I5(e._revisions,s),n=e._revisions.start,i=e._revisions.ids[0]),!e._rev_tree){if(a=qn(e._rev),a.error)return a;n=a.prefix,i=a.id,e._rev_tree=[{pos:n,ids:[i,s,[]]}]}l6(e._id),e._rev=n+"-"+i;var c={metadata:{},data:{}};for(var d in e)if(Object.prototype.hasOwnProperty.call(e,d)){var x=d[0]==="_";if(x&&!A5[d]){var k=c1(In,d);throw k.message=In.message+": "+d,k}else x&&!L5[d]?c.metadata[d.slice(1)]=e[d]:c.data[d]=e[d]}return c}function j5(e){try{return q4(e)}catch{var o=c1(c6,"Attachment is not a valid base64 string");return{error:o}}}function H5(e,o,t){var n=j5(e.data);if(n.error)return t(n.error);e.length=n.length,o==="blob"?e.data=N4(n,e.content_type):o==="base64"?e.data=s2(n):e.data=n,G4(n,function(i){e.digest="md5-"+i,t()})}function V5(e,o,t){G4(e.data,function(n){e.digest="md5-"+n,e.length=e.data.size||e.data.length||0,o==="binary"?g6(e.data,function(i){e.data=i,t()}):o==="base64"?U4(e.data,function(i){e.data=i,t()}):t()})}function O5(e,o,t){if(e.stub)return t();typeof e.data=="string"?H5(e,o,t):V5(e,o,t)}function D5(e,o,t){if(!e.length)return t();var n=0,i;e.forEach(function(s){var c=s.data&&s.data._attachments?Object.keys(s.data._attachments):[],d=0;if(!c.length)return a();function x(l){i=l,d++,d===c.length&&a()}for(var k in s.data._attachments)Object.prototype.hasOwnProperty.call(s.data._attachments,k)&&O5(s.data._attachments[k],o,x)});function a(){n++,e.length===n&&(i?t(i):t())}}function E5(e,o,t,n,i,a,s,c){if(La(o.rev_tree,t.metadata.rev)&&!c)return n[i]=t,a();var d=o.winningRev||A0(o),x="deleted"in o?o.deleted:c0(o,d),k="deleted"in t.metadata?t.metadata.deleted:c0(t.metadata),l=/^1-/.test(t.metadata.rev);if(x&&!k&&c&&l){var M=t.data;M._rev=d,M._id=t.metadata.id,t=b6(M,c)}var _=w6(o.rev_tree,t.metadata.rev_tree[0],e),b=c&&(x&&k&&_.conflicts!=="new_leaf"||!x&&_.conflicts!=="new_leaf"||x&&!k&&_.conflicts==="new_branch");if(b){var A=c1(F0);return n[i]=A,a()}var y=t.metadata.rev;t.metadata.rev_tree=_.tree,t.stemmedRevs=_.stemmedRevs||[],o.rev_map&&(t.metadata.rev_map=o.rev_map);var u=A0(t.metadata),h=c0(t.metadata,u),r=x===h?0:x<h?-1:1,v;y===u?v=h:v=c0(t.metadata,y),s(t,u,h,v,!0,r,i,a)}function T5(e){return e.metadata.rev_tree[0].ids[1].status==="missing"}function P5(e,o,t,n,i,a,s,c,d){e=e||1e3;function x(A,y,u){var h=A0(A.metadata),r=c0(A.metadata,h);if("was_delete"in c&&r)return a[y]=c1(U1,"deleted"),u();var v=k&&T5(A);if(v){var w=c1(F0);return a[y]=w,u()}var f=r?0:1;s(A,h,r,r,!1,f,y,u)}var k=c.new_edits,l=new Map,M=0,_=o.length;function b(){++M===_&&d&&d()}o.forEach(function(A,y){if(A._id&&S0(A._id)){var u=A._deleted?"_removeLocal":"_putLocal";t[u](A,{ctx:i},function(r,v){a[y]=r||v,b()});return}var h=A.metadata.id;l.has(h)?(_--,l.get(h).push([A,y])):l.set(h,[[A,y]])}),l.forEach(function(A,y){var u=0;function h(){++u<A.length?r():b()}function r(){var v=A[u],w=v[0],f=v[1];if(n.has(y))E5(e,n.get(y),w,a,f,h,s,k);else{var O=w6([],w.metadata.rev_tree[0],e);w.metadata.rev_tree=O.tree,w.stemmedRevs=O.stemmedRevs||[],x(w,f,h)}}r()})}var R5=5,I1="document-store",T1="by-sequence",G1="attach-store",B0="attach-seq-store",q1="meta-store",t0="local-store",a4="detect-blob-support";function q5(e){try{return JSON.parse(e)}catch{return D4.default.parse(e)}}function F5(e){try{return JSON.stringify(e)}catch{return D4.default.stringify(e)}}function n0(e){return function(o){var t="unknown_error";o.target&&o.target.error&&(t=o.target.error.name||o.target.error.message),e(c1(T4,t,o.type))}}function _4(e,o,t){return{data:F5(e),winningRev:o,deletedOrLocal:t?"1":"0",seq:e.seq,id:e.id}}function b0(e){if(!e)return null;var o=q5(e.data);return o.winningRev=e.winningRev,o.deleted=e.deletedOrLocal==="1",o.seq=e.seq,o}function O2(e){if(!e)return e;var o=e._doc_id_rev.lastIndexOf(":");return e._id=e._doc_id_rev.substring(0,o-1),e._rev=e._doc_id_rev.substring(o+1),delete e._doc_id_rev,e}function S6(e,o,t,n){t?n(e?typeof e!="string"?e:$4(e,o):F4([""],{type:o})):e?typeof e!="string"?v6(e,function(i){n(s2(i))}):n(e):n("")}function A6(e,o,t,n){var i=Object.keys(e._attachments||{});if(!i.length)return n&&n();var a=0;function s(){++a===i.length&&n&&n()}function c(d,x){var k=d._attachments[x],l=k.digest,M=t.objectStore(G1).get(l);M.onsuccess=function(_){k.body=_.target.result.body,s()}}i.forEach(function(d){o.attachments&&o.include_docs?c(e,d):(e._attachments[d].stub=!0,s())})}function B4(e,o){return Promise.all(e.map(function(t){if(t.doc&&t.doc._attachments){var n=Object.keys(t.doc._attachments);return Promise.all(n.map(function(i){var a=t.doc._attachments[i];if("body"in a){var s=a.body,c=a.content_type;return new Promise(function(d){S6(s,c,o,function(x){t.doc._attachments[i]=Object.assign(a2(a,["digest","content_type"]),{data:x}),d()})})}}))}}))}function L6(e,o,t){var n=[],i=t.objectStore(T1),a=t.objectStore(G1),s=t.objectStore(B0),c=e.length;function d(){c--,c||x()}function x(){n.length&&n.forEach(function(k){var l=s.index("digestSeq").count(IDBKeyRange.bound(k+"::",k+"::\uFFFF",!1,!1));l.onsuccess=function(M){var _=M.target.result;_||a.delete(k)}})}e.forEach(function(k){var l=i.index("_doc_id_rev"),M=o+"::"+k;l.getKey(M).onsuccess=function(_){var b=_.target.result;if(typeof b!="number")return d();i.delete(b);var A=s.index("seq").openCursor(IDBKeyRange.only(b));A.onsuccess=function(y){var u=y.target.result;if(u){var h=u.value.digestSeq.split("::")[0];n.push(h),s.delete(u.primaryKey),u.continue()}else d()}}})}function J1(e,o,t){try{return{txn:e.transaction(o,t)}}catch(n){return{error:n}}}var n2=new p4;function N5(e,o,t,n,i,a){for(var s=o.docs,c,d,x,k,l,M,_,b,A=0,y=s.length;A<y;A++){var u=s[A];u._id&&S0(u._id)||(u=s[A]=b6(u,t.new_edits,e),u.error&&!_&&(_=u))}if(_)return a(_);var h=!1,r=0,v=new Array(s.length),w=new Map,f=!1,O=n._meta.blobSupport?"blob":"base64";D5(s,O,function(C){if(C)return a(C);E()});function E(){var C=[I1,T1,G1,t0,B0,q1],L=J1(i,C,"readwrite");if(L.error)return a(L.error);c=L.txn,c.onabort=n0(a),c.ontimeout=n0(a),c.oncomplete=e1,d=c.objectStore(I1),x=c.objectStore(T1),k=c.objectStore(G1),l=c.objectStore(B0),M=c.objectStore(q1),M.get(q1).onsuccess=function(T){b=T.target.result,Y()},v1(function(T){if(T)return f=!0,a(T);r1()})}function V(){h=!0,Y()}function n1(){P5(e.revs_limit,s,n,w,c,v,g1,t,V)}function Y(){!b||!h||(b.docCount+=r,M.put(b))}function r1(){if(!s.length)return;var C=0;function L(){++C===s.length&&n1()}function T(D){var $=b0(D.target.result);$&&w.set($.id,$),L()}for(var J=0,I=s.length;J<I;J++){var B=s[J];if(B._id&&S0(B._id)){L();continue}var S=d.get(B.metadata.id);S.onsuccess=T}}function e1(){f||(n2.notify(n._meta.name),a(null,v))}function i1(C,L){var T=k.get(C);T.onsuccess=function(J){if(J.target.result)L();else{var I=c1(ca,"unknown stub attachment with digest "+C);I.status=412,L(I)}}}function v1(C){var L=[];if(s.forEach(function(B){B.data&&B.data._attachments&&Object.keys(B.data._attachments).forEach(function(S){var D=B.data._attachments[S];D.stub&&L.push(D.digest)})}),!L.length)return C();var T=0,J;function I(){++T===L.length&&C(J)}L.forEach(function(B){i1(B,function(S){S&&!J&&(J=S),I()})})}function g1(C,L,T,J,I,B,S,D){C.metadata.winningRev=L,C.metadata.deleted=T;var $=C.data;$._id=C.metadata.id,$._rev=C.metadata.rev,J&&($._deleted=!0);var Q=$._attachments&&Object.keys($._attachments).length;if(Q)return g(C,L,T,I,S,D);r+=B,Y(),u1(C,L,T,I,S,D)}function u1(C,L,T,J,I,B){var S=C.data,D=C.metadata;S._doc_id_rev=D.id+"::"+D.rev,delete S._id,delete S._rev;function $(M1){var j1=C.stemmedRevs||[];J&&n.auto_compaction&&(j1=j1.concat(ya(C.metadata))),j1&&j1.length&&L6(j1,C.metadata.id,c),D.seq=M1.target.result;var P1=_4(D,L,T),o1=d.put(P1);o1.onsuccess=l1}function Q(M1){M1.preventDefault(),M1.stopPropagation();var j1=x.index("_doc_id_rev"),P1=j1.getKey(S._doc_id_rev);P1.onsuccess=function(o1){var t1=x.put(S,o1.target.result);t1.onsuccess=$}}function l1(){v[I]={ok:!0,id:D.id,rev:D.rev},w.set(C.metadata.id,C.metadata),p(C,D.seq,B)}var w1=x.put(S);w1.onsuccess=$,w1.onerror=Q}function g(C,L,T,J,I,B){var S=C.data,D=0,$=Object.keys(S._attachments);function Q(){D===$.length&&u1(C,L,T,J,I,B)}function l1(){D++,Q()}$.forEach(function(w1){var M1=C.data._attachments[w1];if(M1.stub)D++,Q();else{var j1=M1.data;delete M1.data,M1.revpos=parseInt(L,10);var P1=M1.digest;z(P1,j1,l1)}})}function p(C,L,T){var J=0,I=Object.keys(C.data._attachments||{});if(!I.length)return T();function B(){++J===I.length&&T()}function S($){var Q=C.data._attachments[$].digest,l1=l.put({seq:L,digestSeq:Q+"::"+L});l1.onsuccess=B,l1.onerror=function(w1){w1.preventDefault(),w1.stopPropagation(),B()}}for(var D=0;D<I.length;D++)S(I[D])}function z(C,L,T){var J=k.count(C);J.onsuccess=function(I){var B=I.target.result;if(B)return T();var S={digest:C,body:L},D=k.put(S);D.onsuccess=T}}}function I6(e,o,t,n,i){n===-1&&(n=1e3);var a=typeof e.getAll=="function"&&typeof e.getAllKeys=="function"&&n>1&&!t,s,c,d;function x(_){c=_.target.result,s&&i(s,c,d)}function k(_){s=_.target.result,c&&i(s,c,d)}function l(){if(!s.length)return i();var _=s[s.length-1],b;if(o&&o.upper)try{b=IDBKeyRange.bound(_,o.upper,!0,o.upperOpen)}catch(A){if(A.name==="DataError"&&A.code===0)return i()}else b=IDBKeyRange.lowerBound(_,!0);o=b,s=null,c=null,e.getAll(o,n).onsuccess=x,e.getAllKeys(o,n).onsuccess=k}function M(_){var b=_.target.result;if(!b)return i();i([b.key],[b.value],b)}a?(d={continue:l},e.getAll(o,n).onsuccess=x,e.getAllKeys(o,n).onsuccess=k):t?e.openCursor(o,"prev").onsuccess=M:e.openCursor(o).onsuccess=M}function $5(e,o,t){if(typeof e.getAll=="function"){e.getAll(o).onsuccess=t;return}var n=[];function i(a){var s=a.target.result;s?(n.push(s.value),s.continue()):t({target:{result:n}})}e.openCursor(o).onsuccess=i}function U5(e,o,t){var n=new Array(e.length),i=0;e.forEach(function(a,s){o.get(a).onsuccess=function(c){c.target.result?n[s]=c.target.result:n[s]={key:a,error:"not_found"},i++,i===e.length&&t(e,n,{})}})}function G5(e,o,t,n,i){try{if(e&&o)return i?IDBKeyRange.bound(o,e,!t,!1):IDBKeyRange.bound(e,o,!1,!t);if(e)return i?IDBKeyRange.upperBound(e):IDBKeyRange.lowerBound(e);if(o)return i?IDBKeyRange.lowerBound(o,!t):IDBKeyRange.upperBound(o,!t);if(n)return IDBKeyRange.only(n)}catch(a){return{error:a}}return null}function K5(e,o,t){var n="startkey"in e?e.startkey:!1,i="endkey"in e?e.endkey:!1,a="key"in e?e.key:!1,s="keys"in e?e.keys:!1,c=e.skip||0,d=typeof e.limit=="number"?e.limit:-1,x=e.inclusive_end!==!1,k,l;if(!s&&(k=G5(n,i,x,a,e.descending),l=k&&k.error,l&&!(l.name==="DataError"&&l.code===0)))return t(c1(T4,l.name,l.message));var M=[I1,T1,q1];e.attachments&&M.push(G1);var _=J1(o,M,"readonly");if(_.error)return t(_.error);var b=_.txn;b.oncomplete=r1,b.onabort=n0(t);var A=b.objectStore(I1),y=b.objectStore(T1),u=b.objectStore(q1),h=y.index("_doc_id_rev"),r=[],v,w;u.get(q1).onsuccess=function(e1){v=e1.target.result.docCount},e.update_seq&&(y.openKeyCursor(null,"prev").onsuccess=e1=>{var i1=e1.target.result;i1&&i1.key&&(w=i1.key)});function f(e1,i1,v1){var g1=e1.id+"::"+v1;h.get(g1).onsuccess=function(g){if(i1.doc=O2(g.target.result)||{},e.conflicts){var p=W4(e1);p.length&&(i1.doc._conflicts=p)}A6(i1.doc,e,b)}}function O(e1,i1){var v1={id:i1.id,key:i1.id,value:{rev:e1}},g1=i1.deleted;g1?s&&(r.push(v1),v1.value.deleted=!0,v1.doc=null):c--<=0&&(r.push(v1),e.include_docs&&f(i1,v1,e1))}function E(e1){for(var i1=0,v1=e1.length;i1<v1&&r.length!==d;i1++){var g1=e1[i1];if(g1.error&&s){r.push(g1);continue}var u1=b0(g1),g=u1.winningRev;O(g,u1)}}function V(e1,i1,v1){v1&&(E(i1),r.length<d&&v1.continue())}function n1(e1){var i1=e1.target.result;e.descending&&(i1=i1.reverse()),E(i1)}function Y(){var e1={total_rows:v,offset:e.skip,rows:r};e.update_seq&&w!==void 0&&(e1.update_seq=w),t(null,e1)}function r1(){e.attachments?B4(r,e.binary).then(Y):Y()}if(!(l||d===0)){if(s)return U5(s,A,V);if(d===-1)return $5(A,k,n1);I6(A,k,e.descending,d+c,V)}}function W5(e,o){var t=e.objectStore(I1).index("deletedOrLocal");t.count(IDBKeyRange.only("0")).onsuccess=function(n){o(n.target.result)}}var b4=!1,S4=[];function Z5(e,o,t,n){try{e(o,t)}catch(i){n.emit("error",i)}}function Fn(){b4||!S4.length||(b4=!0,S4.shift()())}function Q5(e,o,t){S4.push(function(){e(function(a,s){Z5(o,a,s,t),b4=!1,a0(function(){Fn(t)})})}),Fn()}function X5(e,o,t,n){if(e=V1(e),e.continuous){var i=t+":"+q2();return n2.addListener(t,i,o,e),n2.notify(t),{cancel:function(){n2.removeListener(t,i)}}}var a=e.doc_ids&&new Set(e.doc_ids);e.since=e.since||0;var s=e.since,c="limit"in e?e.limit:-1;c===0&&(c=1);var d=[],x=0,k=P4(e),l=new Map,M,_,b,A;function y(E,V,n1){if(!n1||!E.length)return;var Y=new Array(E.length),r1=new Array(E.length);function e1(g1,u1){var g=e.processChange(u1,g1,e);s=g.seq=g1.seq;var p=k(g);return typeof p=="object"?Promise.reject(p):p?(x++,e.return_docs&&d.push(g),e.attachments&&e.include_docs?new Promise(function(z){A6(u1,e,M,function(){B4([g],e.binary).then(function(){z(g)})})}):Promise.resolve(g)):Promise.resolve()}function i1(){for(var g1=[],u1=0,g=Y.length;u1<g&&x!==c;u1++){var p=Y[u1];if(p){var z=r1[u1];g1.push(e1(z,p))}}Promise.all(g1).then(function(C){for(var L=0,T=C.length;L<T;L++)C[L]&&e.onChange(C[L])}).catch(e.complete),x!==c&&n1.continue()}var v1=0;V.forEach(function(g1,u1){var g=O2(g1),p=E[u1];h(g,p,function(z,C){r1[u1]=z,Y[u1]=C,++v1===E.length&&i1()})})}function u(E,V,n1,Y){if(n1.seq!==V)return Y();if(n1.winningRev===E._rev)return Y(n1,E);var r1=E._id+"::"+n1.winningRev,e1=A.get(r1);e1.onsuccess=function(i1){Y(n1,O2(i1.target.result))}}function h(E,V,n1){if(a&&!a.has(E._id))return n1();var Y=l.get(E._id);if(Y)return u(E,V,Y,n1);b.get(E._id).onsuccess=function(r1){Y=b0(r1.target.result),l.set(E._id,Y),u(E,V,Y,n1)}}function r(){e.complete(null,{results:d,last_seq:s})}function v(){!e.continuous&&e.attachments?B4(d).then(r):r()}var w=[I1,T1];e.attachments&&w.push(G1);var f=J1(n,w,"readonly");if(f.error)return e.complete(f.error);M=f.txn,M.onabort=n0(e.complete),M.oncomplete=v,_=M.objectStore(T1),b=M.objectStore(I1),A=_.index("_doc_id_rev");var O=e.since&&!e.descending?IDBKeyRange.lowerBound(e.since,!0):null;I6(_,O,e.descending,c,y)}var P0=new Map,s4,r4=new Map;function j6(e,o){var t=this;Q5(function(n){J5(t,e,n)},o,t.constructor)}function J5(e,o,t){var n=o.name,i=null,a=null;e._meta=null;function s(y){return function(u,h){u&&u instanceof Error&&!u.reason&&a&&(u.reason=a),y(u,h)}}function c(y){var u=y.createObjectStore(I1,{keyPath:"id"});y.createObjectStore(T1,{autoIncrement:!0}).createIndex("_doc_id_rev","_doc_id_rev",{unique:!0}),y.createObjectStore(G1,{keyPath:"digest"}),y.createObjectStore(q1,{keyPath:"id",autoIncrement:!1}),y.createObjectStore(a4),u.createIndex("deletedOrLocal","deletedOrLocal",{unique:!1}),y.createObjectStore(t0,{keyPath:"_id"});var h=y.createObjectStore(B0,{autoIncrement:!0});h.createIndex("seq","seq"),h.createIndex("digestSeq","digestSeq",{unique:!0})}function d(y,u){var h=y.objectStore(I1);h.createIndex("deletedOrLocal","deletedOrLocal",{unique:!1}),h.openCursor().onsuccess=function(r){var v=r.target.result;if(v){var w=v.value,f=c0(w);w.deletedOrLocal=f?"1":"0",h.put(w),v.continue()}else u()}}function x(y){y.createObjectStore(t0,{keyPath:"_id"}).createIndex("_doc_id_rev","_doc_id_rev",{unique:!0})}function k(y,u){var h=y.objectStore(t0),r=y.objectStore(I1),v=y.objectStore(T1),w=r.openCursor();w.onsuccess=function(f){var O=f.target.result;if(O){var E=O.value,V=E.id,n1=S0(V),Y=A0(E);if(n1){var r1=V+"::"+Y,e1=V+"::",i1=V+"::~",v1=v.index("_doc_id_rev"),g1=IDBKeyRange.bound(e1,i1,!1,!1),u1=v1.openCursor(g1);u1.onsuccess=function(g){if(u1=g.target.result,!u1)r.delete(O.primaryKey),O.continue();else{var p=u1.value;p._doc_id_rev===r1&&h.put(p),v.delete(u1.primaryKey),u1.continue()}}}else O.continue()}else u&&u()}}function l(y){var u=y.createObjectStore(B0,{autoIncrement:!0});u.createIndex("seq","seq"),u.createIndex("digestSeq","digestSeq",{unique:!0})}function M(y,u){var h=y.objectStore(T1),r=y.objectStore(G1),v=y.objectStore(B0),w=r.count();w.onsuccess=function(f){var O=f.target.result;if(!O)return u();h.openCursor().onsuccess=function(E){var V=E.target.result;if(!V)return u();for(var n1=V.value,Y=V.primaryKey,r1=Object.keys(n1._attachments||{}),e1={},i1=0;i1<r1.length;i1++){var v1=n1._attachments[r1[i1]];e1[v1.digest]=!0}var g1=Object.keys(e1);for(i1=0;i1<g1.length;i1++){var u1=g1[i1];v.put({seq:Y,digestSeq:u1+"::"+Y})}V.continue()}}}function _(y){function u(w){return w.data?b0(w):(w.deleted=w.deletedOrLocal==="1",w)}var h=y.objectStore(T1),r=y.objectStore(I1),v=r.openCursor();v.onsuccess=function(w){var f=w.target.result;if(!f)return;var O=u(f.value);O.winningRev=O.winningRev||A0(O);function E(){var n1=O.id+"::",Y=O.id+"::\uFFFF",r1=h.index("_doc_id_rev").openCursor(IDBKeyRange.bound(n1,Y)),e1=0;r1.onsuccess=function(i1){var v1=i1.target.result;if(!v1)return O.seq=e1,V();var g1=v1.primaryKey;g1>e1&&(e1=g1),v1.continue()}}function V(){var n1=_4(O,O.winningRev,O.deleted),Y=r.put(n1);Y.onsuccess=function(){f.continue()}}if(O.seq)return V();E()}}e._remote=!1,e.type=function(){return"idb"},e._id=a6(function(y){y(null,e._meta.instanceId)}),e._bulkDocs=function(u,h,r){N5(o,u,h,e,i,s(r))},e._get=function(u,h,r){var v,w,f,O=h.ctx;if(!O){var E=J1(i,[I1,T1,G1],"readonly");if(E.error)return r(E.error);O=E.txn}function V(){r(f,{doc:v,metadata:w,ctx:O})}O.objectStore(I1).get(u).onsuccess=function(n1){if(w=b0(n1.target.result),!w)return f=c1(U1,"missing"),V();var Y;if(h.rev)Y=h.latest?ja(h.rev,w):h.rev;else{Y=w.winningRev;var r1=c0(w);if(r1)return f=c1(U1,"deleted"),V()}var e1=O.objectStore(T1),i1=w.id+"::"+Y;e1.index("_doc_id_rev").get(i1).onsuccess=function(v1){if(v=v1.target.result,v&&(v=O2(v)),!v)return f=c1(U1,"missing"),V();V()}}},e._getAttachment=function(y,u,h,r,v){var w;if(r.ctx)w=r.ctx;else{var f=J1(i,[I1,T1,G1],"readonly");if(f.error)return v(f.error);w=f.txn}var O=h.digest,E=h.content_type;w.objectStore(G1).get(O).onsuccess=function(V){var n1=V.target.result.body;S6(n1,E,r.binary,function(Y){v(null,Y)})}},e._info=function(u){var h,r,v=J1(i,[q1,T1],"readonly");if(v.error)return u(v.error);var w=v.txn;w.objectStore(q1).get(q1).onsuccess=function(f){r=f.target.result.docCount},w.objectStore(T1).openKeyCursor(null,"prev").onsuccess=function(f){var O=f.target.result;h=O?O.key:0},w.oncomplete=function(){u(null,{doc_count:r,update_seq:h,idb_attachment_format:e._meta.blobSupport?"binary":"base64"})}},e._allDocs=function(u,h){K5(u,i,s(h))},e._changes=function(u){return X5(u,e,n,i)},e._close=function(y){i.close(),P0.delete(n),y()},e._getRevisionTree=function(y,u){var h=J1(i,[I1],"readonly");if(h.error)return u(h.error);var r=h.txn,v=r.objectStore(I1).get(y);v.onsuccess=function(w){var f=b0(w.target.result);f?u(null,f.rev_tree):u(c1(U1))}},e._doCompaction=function(y,u,h){var r=[I1,T1,G1,B0],v=J1(i,r,"readwrite");if(v.error)return h(v.error);var w=v.txn,f=w.objectStore(I1);f.get(y).onsuccess=function(O){var E=b0(O.target.result);L0(E.rev_tree,function(Y,r1,e1,i1,v1){var g1=r1+"-"+e1;u.indexOf(g1)!==-1&&(v1.status="missing")}),L6(u,y,w);var V=E.winningRev,n1=E.deleted;w.objectStore(I1).put(_4(E,V,n1))},w.onabort=n0(h),w.oncomplete=function(){h()}},e._getLocal=function(y,u){var h=J1(i,[t0],"readonly");if(h.error)return u(h.error);var r=h.txn,v=r.objectStore(t0).get(y);v.onerror=n0(u),v.onsuccess=function(w){var f=w.target.result;f?(delete f._doc_id_rev,u(null,f)):u(c1(U1))}},e._putLocal=function(y,u,h){typeof u=="function"&&(h=u,u={}),delete y._revisions;var r=y._rev,v=y._id;r?y._rev="0-"+(parseInt(r.split("-")[1],10)+1):y._rev="0-1";var w=u.ctx,f;if(!w){var O=J1(i,[t0],"readwrite");if(O.error)return h(O.error);w=O.txn,w.onerror=n0(h),w.oncomplete=function(){f&&h(null,f)}}var E=w.objectStore(t0),V;r?(V=E.get(v),V.onsuccess=function(n1){var Y=n1.target.result;if(!Y||Y._rev!==r)h(c1(F0));else{var r1=E.put(y);r1.onsuccess=function(){f={ok:!0,id:y._id,rev:y._rev},u.ctx&&h(null,f)}}}):(V=E.add(y),V.onerror=function(n1){h(c1(F0)),n1.preventDefault(),n1.stopPropagation()},V.onsuccess=function(){f={ok:!0,id:y._id,rev:y._rev},u.ctx&&h(null,f)})},e._removeLocal=function(y,u,h){typeof u=="function"&&(h=u,u={});var r=u.ctx;if(!r){var v=J1(i,[t0],"readwrite");if(v.error)return h(v.error);r=v.txn,r.oncomplete=function(){w&&h(null,w)}}var w,f=y._id,O=r.objectStore(t0),E=O.get(f);E.onerror=n0(h),E.onsuccess=function(V){var n1=V.target.result;!n1||n1._rev!==y._rev?h(c1(U1)):(O.delete(f),w={ok:!0,id:f,rev:"0-0"},u.ctx&&h(null,w))}},e._destroy=function(y,u){n2.removeAllListeners(n);var h=r4.get(n);h&&h.result&&(h.result.close(),P0.delete(n));var r=indexedDB.deleteDatabase(n);r.onsuccess=function(){r4.delete(n),L2()&&n in localStorage&&delete localStorage[n],u(null,{ok:!0})},r.onerror=n0(u)};var b=P0.get(n);if(b)return i=b.idb,e._meta=b.global,a0(function(){t(null,e)});var A=indexedDB.open(n,R5);r4.set(n,A),A.onupgradeneeded=function(y){var u=y.target.result;if(y.oldVersion<1)return c(u);var h=y.currentTarget.transaction;y.oldVersion<3&&x(u),y.oldVersion<4&&l(u);var r=[d,k,M,_],v=y.oldVersion;function w(){var f=r[v-1];v++,f&&f(h,w)}w()},A.onsuccess=function(y){i=y.target.result,i.onversionchange=function(){i.close(),P0.delete(n)},i.onabort=function(V){K1("error","Database has a global failure",V.target.error),a=V.target.error,i.close(),P0.delete(n)};var u=i.transaction([q1,a4,I1],"readwrite"),h=!1,r,v,w,f;function O(){typeof w>"u"||!h||(e._meta={name:n,instanceId:f,blobSupport:w},P0.set(n,{idb:i,global:e._meta}),t(null,e))}function E(){if(!(typeof v>"u"||typeof r>"u")){var V=n+"_id";V in r?f=r[V]:r[V]=f=q2(),r.docCount=v,u.objectStore(q1).put(r)}}u.objectStore(q1).get(q1).onsuccess=function(V){r=V.target.result||{id:q1},E()},W5(u,function(V){v=V,E()}),s4||(s4=S5(u,a4,"key")),s4.then(function(V){w=V,O()}),u.oncomplete=function(){h=!0,O()},u.onabort=n0(t)},A.onerror=function(y){var u=y.target.error&&y.target.error.message;u?u.indexOf("stored database is a higher version")!==-1&&(u=new Error('This DB was created with the newer "indexeddb" adapter, but you are trying to open it with the older "idb" adapter')):u="Failed to open indexedDB, are you in private browsing mode?",K1("error",u),t(c1(T4,u))}}j6.valid=function(){try{return typeof indexedDB<"u"&&typeof IDBKeyRange<"u"}catch{return!1}};function Y5(e){e.adapter("idb",j6,!0)}function es(e,o){return new Promise(function(t,n){var i=0,a=0,s=0,c=e.length,d;function x(){i++,e[a++]().then(l,M)}function k(){++s===c?d?n(d):t():_()}function l(){i--,k()}function M(b){i--,d=d||b,k()}function _(){for(;i<o&&a<c;)x()}_()})}var ts=25,ns=50,_2=5e3,os=1e4,c4={};function l4(e){let o=e.doc||e.ok,t=o&&o._attachments;t&&Object.keys(t).forEach(function(n){let i=t[n];i.data=$4(i.data,i.content_type)})}function C0(e){return/^_design/.test(e)?"_design/"+encodeURIComponent(e.slice(8)):e.startsWith("_local/")?"_local/"+encodeURIComponent(e.slice(7)):encodeURIComponent(e)}function Nn(e){return!e._attachments||!Object.keys(e._attachments)?Promise.resolve():Promise.all(Object.keys(e._attachments).map(function(o){let t=e._attachments[o];if(t.data&&typeof t.data!="string")return new Promise(function(n){U4(t.data,n)}).then(function(n){t.data=n})}))}function is(e){if(!e.prefix)return!1;let o=d6(e.prefix).protocol;return o==="http"||o==="https"}function as(e,o){if(is(o)){let i=o.name.substr(o.prefix.length);e=o.prefix.replace(/\/?$/,"/")+encodeURIComponent(i)}let t=d6(e);(t.user||t.password)&&(t.auth={username:t.user,password:t.password});let n=t.path.replace(/(^\/|\/$)/g,"").split("/");return t.db=n.pop(),t.db.indexOf("%")===-1&&(t.db=encodeURIComponent(t.db)),t.path=n.join("/"),t}function H1(e,o){return S2(e,e.db+"/"+o)}function S2(e,o){let t=e.path?"/":"";return e.protocol+"://"+e.host+(e.port?":"+e.port:"")+"/"+e.path+t+o}function B2(e){let o=Object.keys(e);return o.length===0?"":"?"+o.map(t=>t+"="+encodeURIComponent(e[t])).join("&")}function ss(e){let o=typeof navigator<"u"&&navigator.userAgent?navigator.userAgent.toLowerCase():"",t=o.indexOf("msie")!==-1,n=o.indexOf("trident")!==-1,i=o.indexOf("edge")!==-1,a=!("method"in e)||e.method==="GET";return(t||n||i)&&a}function A4(e,o){let t=this,n=as(e.name,e),i=H1(n,"");e=V1(e);let a=function(l,M){return X(this,null,function*(){if(M=M||{},M.headers=M.headers||new R0,M.credentials="include",e.auth||n.auth){let A=e.auth||n.auth,y=A.username+":"+A.password,u=s2(unescape(encodeURIComponent(y)));M.headers.set("Authorization","Basic "+u)}let _=e.headers||{};return Object.keys(_).forEach(function(A){M.headers.append(A,_[A])}),ss(M)&&(l+=(l.indexOf("?")===-1?"?":"&")+"_nonce="+Date.now()),yield(e.fetch||M6)(l,M)})};function s(l,M){return A1(l,function(..._){x().then(function(){return M.apply(this,_)}).catch(function(b){_.pop()(b)})}).bind(t)}function c(l,M){return X(this,null,function*(){let _={};M=M||{},M.headers=M.headers||new R0,M.headers.get("Content-Type")||M.headers.set("Content-Type","application/json"),M.headers.get("Accept")||M.headers.set("Accept","application/json");let b=yield a(l,M);_.ok=b.ok,_.status=b.status;let A=yield b.json();if(_.data=A,!_.ok)throw _.data.status=_.status,N0(_.data);return Array.isArray(_.data)&&(_.data=_.data.map(function(y){return y.error||y.missing?N0(y):y})),_})}let d;function x(){return X(this,null,function*(){return e.skip_setup?Promise.resolve():d||(d=c(i).catch(function(l){return l&&l.status&&l.status===404?(m4(404,"PouchDB is just detecting if the remote exists."),c(i,{method:"PUT"})):Promise.reject(l)}).catch(function(l){return l&&l.status&&l.status===412?!0:Promise.reject(l)}),d.catch(function(){d=null}),d)})}a0(function(){o(null,t)}),t._remote=!0,t.type=function(){return"http"},t.id=s("id",function(l){return X(this,null,function*(){let M;try{M=yield(yield a(S2(n,""))).json()}catch{M={}}let _=M&&M.uuid?M.uuid+n.db:H1(n,"");l(null,_)})}),t.compact=s("compact",function(l,M){return X(this,null,function*(){typeof l=="function"&&(M=l,l={}),l=V1(l),yield c(H1(n,"_compact"),{method:"POST"});function _(){t.info(function(b,A){A&&!A.compact_running?M(null,{ok:!0}):setTimeout(_,l.interval||200)})}_()})}),t.bulkGet=A1("bulkGet",function(l,M){let _=this;function b(h){return X(this,null,function*(){let r={};l.revs&&(r.revs=!0),l.attachments&&(r.attachments=!0),l.latest&&(r.latest=!0);try{let v=yield c(H1(n,"_bulk_get"+B2(r)),{method:"POST",body:JSON.stringify({docs:l.docs})});l.attachments&&l.binary&&v.data.results.forEach(function(w){w.docs.forEach(l4)}),h(null,v.data)}catch(v){h(v)}})}function A(){let h=ns,r=Math.ceil(l.docs.length/h),v=0,w=new Array(r);function f(O){return function(E,V){w[O]=V.results,++v===r&&M(null,{results:w.flat()})}}for(let O=0;O<r;O++){let E=a2(l,["revs","attachments","binary","latest"]);E.docs=l.docs.slice(O*h,Math.min(l.docs.length,(O+1)*h)),s6(_,E,f(O))}}let y=S2(n,""),u=c4[y];typeof u!="boolean"?b(function(h,r){h?(c4[y]=!1,m4(h.status,"PouchDB is just detecting if the remote supports the _bulk_get API."),A()):(c4[y]=!0,M(null,r))}):u?b(M):A()}),t._info=function(l){return X(this,null,function*(){try{yield x();let _=yield(yield a(H1(n,""))).json();_.host=H1(n,""),l(null,_)}catch(M){l(M)}})},t.fetch=function(l,M){return X(this,null,function*(){yield x();let _=l.substring(0,1)==="/"?S2(n,l.substring(1)):H1(n,l);return a(_,M)})},t.get=s("get",function(l,M,_){return X(this,null,function*(){typeof M=="function"&&(_=M,M={}),M=V1(M);let b={};M.revs&&(b.revs=!0),M.revs_info&&(b.revs_info=!0),M.latest&&(b.latest=!0),M.open_revs&&(M.open_revs!=="all"&&(M.open_revs=JSON.stringify(M.open_revs)),b.open_revs=M.open_revs),M.rev&&(b.rev=M.rev),M.conflicts&&(b.conflicts=M.conflicts),M.update_seq&&(b.update_seq=M.update_seq),l=C0(l);function A(h){let r=h._attachments,v=r&&Object.keys(r);if(!r||!v.length)return;function w(O){return X(this,null,function*(){let E=r[O],V=C0(h._id)+"/"+k(O)+"?rev="+h._rev,n1=yield a(H1(n,V)),Y;"buffer"in n1?Y=yield n1.buffer():Y=yield n1.blob();let r1;if(M.binary){let e1=Object.getOwnPropertyDescriptor(Y.__proto__,"type");(!e1||e1.set)&&(Y.type=E.content_type),r1=Y}else r1=yield new Promise(function(e1){U4(Y,e1)});delete E.stub,delete E.length,E.data=r1})}let f=v.map(function(O){return function(){return w(O)}});return es(f,5)}function y(h){return Array.isArray(h)?Promise.all(h.map(function(r){if(r.ok)return A(r.ok)})):A(h)}let u=H1(n,l+B2(b));try{let h=yield c(u);M.attachments&&(yield y(h.data)),_(null,h.data)}catch(h){h.docId=l,_(h)}})}),t.remove=s("remove",function(l,M,_,b){return X(this,null,function*(){let A;typeof M=="string"?(A={_id:l,_rev:M},typeof _=="function"&&(b=_,_={})):(A=l,typeof M=="function"?(b=M,_={}):(b=_,_=M));let y=A._rev||_.rev,u=H1(n,C0(A._id))+"?rev="+y;try{let h=yield c(u,{method:"DELETE"});b(null,h.data)}catch(h){b(h)}})});function k(l){return l.split("/").map(encodeURIComponent).join("/")}t.getAttachment=s("getAttachment",function(l,M,_,b){return X(this,null,function*(){typeof _=="function"&&(b=_,_={});let A=_.rev?"?rev="+_.rev:"",y=H1(n,C0(l))+"/"+k(M)+A,u;try{let h=yield a(y,{method:"GET"});if(!h.ok)throw h;u=h.headers.get("content-type");let r;if(typeof process<"u"&&!process.browser&&typeof h.buffer=="function"?r=yield h.buffer():r=yield h.blob(),typeof process<"u"&&!process.browser){let v=Object.getOwnPropertyDescriptor(r.__proto__,"type");(!v||v.set)&&(r.type=u)}b(null,r)}catch(h){b(h)}})}),t.removeAttachment=s("removeAttachment",function(l,M,_,b){return X(this,null,function*(){let A=H1(n,C0(l)+"/"+k(M))+"?rev="+_;try{let y=yield c(A,{method:"DELETE"});b(null,y.data)}catch(y){b(y)}})}),t.putAttachment=s("putAttachment",function(l,M,_,b,A,y){return X(this,null,function*(){typeof A=="function"&&(y=A,A=b,b=_,_=null);let u=C0(l)+"/"+k(M),h=H1(n,u);if(_&&(h+="?rev="+_),typeof b=="string"){let r;try{r=q4(b)}catch{return y(c1(c6,"Attachment is not a valid base64 string"))}b=r?N4(r,A):""}try{let r=yield c(h,{headers:new R0({"Content-Type":A}),method:"PUT",body:b});y(null,r.data)}catch(r){y(r)}})}),t._bulkDocs=function(l,M,_){return X(this,null,function*(){l.new_edits=M.new_edits;try{yield x(),yield Promise.all(l.docs.map(Nn));let b=yield c(H1(n,"_bulk_docs"),{method:"POST",body:JSON.stringify(l)});_(null,b.data)}catch(b){_(b)}})},t._put=function(l,M,_){return X(this,null,function*(){try{yield x(),yield Nn(l);let b=yield c(H1(n,C0(l._id)),{method:"PUT",body:JSON.stringify(l)});_(null,b.data)}catch(b){b.docId=l&&l._id,_(b)}})},t.allDocs=s("allDocs",function(l,M){return X(this,null,function*(){typeof l=="function"&&(M=l,l={}),l=V1(l);let _={},b,A="GET";l.conflicts&&(_.conflicts=!0),l.update_seq&&(_.update_seq=!0),l.descending&&(_.descending=!0),l.include_docs&&(_.include_docs=!0),l.attachments&&(_.attachments=!0),l.key&&(_.key=JSON.stringify(l.key)),l.start_key&&(l.startkey=l.start_key),l.startkey&&(_.startkey=JSON.stringify(l.startkey)),l.end_key&&(l.endkey=l.end_key),l.endkey&&(_.endkey=JSON.stringify(l.endkey)),typeof l.inclusive_end<"u"&&(_.inclusive_end=!!l.inclusive_end),typeof l.limit<"u"&&(_.limit=l.limit),typeof l.skip<"u"&&(_.skip=l.skip);let y=B2(_);typeof l.keys<"u"&&(A="POST",b={keys:l.keys});try{let u=yield c(H1(n,"_all_docs"+y),{method:A,body:JSON.stringify(b)});l.include_docs&&l.attachments&&l.binary&&u.data.rows.forEach(l4),M(null,u.data)}catch(u){M(u)}})}),t._changes=function(l){let M="batch_size"in l?l.batch_size:ts;l=V1(l),l.continuous&&!("heartbeat"in l)&&(l.heartbeat=os);let _="timeout"in l?l.timeout:30*1e3;"timeout"in l&&l.timeout&&_-l.timeout<_2&&(_=l.timeout+_2),"heartbeat"in l&&l.heartbeat&&_-l.heartbeat<_2&&(_=l.heartbeat+_2);let b={};"timeout"in l&&l.timeout&&(b.timeout=l.timeout);let A=typeof l.limit<"u"?l.limit:!1,y=A;if(l.style&&(b.style=l.style),(l.include_docs||l.filter&&typeof l.filter=="function")&&(b.include_docs=!0),l.attachments&&(b.attachments=!0),l.continuous&&(b.feed="longpoll"),l.seq_interval&&(b.seq_interval=l.seq_interval),l.conflicts&&(b.conflicts=!0),l.descending&&(b.descending=!0),l.update_seq&&(b.update_seq=!0),"heartbeat"in l&&l.heartbeat&&(b.heartbeat=l.heartbeat),l.filter&&typeof l.filter=="string"&&(b.filter=l.filter),l.view&&typeof l.view=="string"&&(b.filter="_view",b.view=l.view),l.query_params&&typeof l.query_params=="object")for(let E in l.query_params)Object.prototype.hasOwnProperty.call(l.query_params,E)&&(b[E]=l.query_params[E]);let u="GET",h;l.doc_ids?(b.filter="_doc_ids",u="POST",h={doc_ids:l.doc_ids}):l.selector&&(b.filter="_selector",u="POST",h={selector:l.selector});let r=new AbortController,v,w=function(E,V){return X(this,null,function*(){if(l.aborted)return;b.since=E,typeof b.since=="object"&&(b.since=JSON.stringify(b.since)),l.descending?A&&(b.limit=y):b.limit=!A||y>M?M:y;let n1=H1(n,"_changes"+B2(b)),Y={signal:r.signal,method:u,body:JSON.stringify(h)};if(v=E,!l.aborted)try{yield x();let r1=yield c(n1,Y);V(null,r1.data)}catch(r1){V(r1)}})},f={results:[]},O=function(E,V){if(l.aborted)return;let n1=0;if(V&&V.results){n1=V.results.length,f.last_seq=V.last_seq;let r1=null,e1=null;typeof V.pending=="number"&&(r1=V.pending),(typeof f.last_seq=="string"||typeof f.last_seq=="number")&&(e1=f.last_seq);let i1={};i1.query=l.query_params,V.results=V.results.filter(function(v1){y--;let g1=P4(l)(v1);return g1&&(l.include_docs&&l.attachments&&l.binary&&l4(v1),l.return_docs&&f.results.push(v1),l.onChange(v1,r1,e1)),g1})}else if(E){l.aborted=!0,l.complete(E);return}V&&V.last_seq&&(v=V.last_seq);let Y=A&&y<=0||V&&n1<M||l.descending;l.continuous&&!(A&&y<=0)||!Y?a0(function(){w(v,O)}):l.complete(null,f)};return w(l.since||0,O),{cancel:function(){l.aborted=!0,r.abort()}}},t.revsDiff=s("revsDiff",function(l,M,_){return X(this,null,function*(){typeof M=="function"&&(_=M,M={});try{let b=yield c(H1(n,"_revs_diff"),{method:"POST",body:JSON.stringify(l)});_(null,b.data)}catch(b){_(b)}})}),t._close=function(l){l()},t._destroy=function(l,M){return X(this,null,function*(){try{let _=yield c(H1(n,""),{method:"DELETE"});M(null,_)}catch(_){_.status===404?M(null,{ok:!0}):M(_)}})}}A4.valid=function(){return!0};function rs(e){e.adapter("http",A4,!1),e.adapter("https",A4,!1)}var _0=class e extends Error{constructor(o){super(),this.status=400,this.name="query_parse_error",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},D2=class e extends Error{constructor(o){super(),this.status=404,this.name="not_found",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},E2=class e extends Error{constructor(o){super(),this.status=500,this.name="invalid_value",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}};function H6(e,o){return o&&e.then(function(t){a0(function(){o(null,t)})},function(t){a0(function(){o(t)})}),e}function cs(e){return function(...o){var t=o.pop(),n=e.apply(this,o);return typeof t=="function"&&H6(n,t),n}}function ls(e,o){return e.then(function(t){return o().then(function(){return t})},function(t){return o().then(function(){throw t})})}function d4(e,o){return function(){var t=arguments,n=this;return e.add(function(){return o.apply(n,t)})}}function $n(e){var o=new Set(e),t=new Array(o.size),n=-1;return o.forEach(function(i){t[++n]=i}),t}function v4(e){var o=new Array(e.size),t=-1;return e.forEach(function(n,i){o[++t]=i}),o}function Un(e){var o="builtin "+e+" function requires map values to be numbers or number arrays";return new E2(o)}function L4(e){for(var o=0,t=0,n=e.length;t<n;t++){var i=e[t];if(typeof i!="number")if(Array.isArray(i)){o=typeof o=="number"?[o]:o;for(var a=0,s=i.length;a<s;a++){var c=i[a];if(typeof c!="number")throw Un("_sum");typeof o[a]>"u"?o.push(c):o[a]+=c}}else throw Un("_sum");else typeof o=="number"?o+=i:o[0]+=i}return o}var ds=K1.bind(null,"log"),vs=Array.isArray,gs=JSON.parse;function V6(e,o){return R4("return ("+e.replace(/;\s*$/,"")+");",{emit:o,sum:L4,log:ds,isArray:vs,toJSON:gs})}var i2=class{constructor(){this.promise=Promise.resolve()}add(o){return this.promise=this.promise.catch(()=>{}).then(()=>o()),this.promise}finish(){return this.promise}};function Gn(e){if(!e)return"undefined";switch(typeof e){case"function":return e.toString();case"string":return e.toString();default:return JSON.stringify(e)}}function hs(e,o){return Gn(e)+Gn(o)+"undefined"}function Kn(e,o,t,n,i,a){return X(this,null,function*(){let s=hs(t,n),c;if(!i&&(c=e._cachedViews=e._cachedViews||{},c[s]))return c[s];let d=e.info().then(function(x){return X(this,null,function*(){let k=x.db_name+"-mrview-"+(i?"temp":h6(s));function l(y){y.views=y.views||{};let u=o;u.indexOf("/")===-1&&(u=o+"/"+o);let h=y.views[u]=y.views[u]||{};if(!h[k])return h[k]=!0,y}yield I2(e,"_local/"+a,l);let _=(yield e.registerDependentDatabase(k)).db;_.auto_compaction=!0;let b={name:k,db:_,sourceDB:e,adapter:e.adapter,mapFun:t,reduceFun:n},A;try{A=yield b.db.get("_local/lastSeq")}catch(y){if(y.status!==404)throw y}return b.seq=A?A.seq:0,c&&b.db.once("destroyed",function(){delete c[s]}),b})});return c&&(c[s]=d),d})}var Wn={},Zn=new i2,us=50;function g4(e){return e.indexOf("/")===-1?[e,e]:e.split("/")}function ps(e){return e.length===1&&/^1-/.test(e[0].rev)}function Qn(e,o,t){try{e.emit("error",o)}catch{K1("error",`The user's map/reduce function threw an uncaught error.
You can debug this error by doing:
myDatabase.on('error', function (err) { debugger; });
Please double-check your map/reduce function.`),K1("error",o,t)}}function ms(e,o,t,n){function i(g,p,z){try{p(z)}catch(C){Qn(g,C,{fun:p,doc:z})}}function a(g,p,z,C,L){try{return{output:p(z,C,L)}}catch(T){return Qn(g,T,{fun:p,keys:z,values:C,rereduce:L}),{error:T}}}function s(g,p){let z=L1(g.key,p.key);return z!==0?z:L1(g.value,p.value)}function c(g,p,z){return z=z||0,typeof p=="number"?g.slice(z,p+z):z>0?g.slice(z):g}function d(g){let p=g.value;return p&&typeof p=="object"&&p._id||g.id}function x(g){for(let p of g.rows){let z=p.doc&&p.doc._attachments;if(z)for(let C of Object.keys(z)){let L=z[C];z[C].data=$4(L.data,L.content_type)}}}function k(g){return function(p){return g.include_docs&&g.attachments&&g.binary&&x(p),p}}function l(g,p,z,C){let L=p[g];typeof L<"u"&&(C&&(L=encodeURIComponent(JSON.stringify(L))),z.push(g+"="+L))}function M(g){if(typeof g<"u"){let p=Number(g);return!isNaN(p)&&p===parseInt(g,10)?p:g}}function _(g){return g.group_level=M(g.group_level),g.limit=M(g.limit),g.skip=M(g.skip),g}function b(g){if(g){if(typeof g!="number")return new _0(`Invalid value for integer: "${g}"`);if(g<0)return new _0(`Invalid value for positive integer: "${g}"`)}}function A(g,p){let z=g.descending?"endkey":"startkey",C=g.descending?"startkey":"endkey";if(typeof g[z]<"u"&&typeof g[C]<"u"&&L1(g[z],g[C])>0)throw new _0("No rows can match your key range, reverse your start_key and end_key or set {descending : true}");if(p.reduce&&g.reduce!==!1){if(g.include_docs)throw new _0("{include_docs:true} is invalid for reduce");if(g.keys&&g.keys.length>1&&!g.group&&!g.group_level)throw new _0("Multi-key fetches for reduce views must use {group: true}")}for(let L of["group_level","limit","skip"]){let T=b(g[L]);if(T)throw T}}function y(g,p,z){return X(this,null,function*(){let C=[],L,T="GET",J;if(l("reduce",z,C),l("include_docs",z,C),l("attachments",z,C),l("limit",z,C),l("descending",z,C),l("group",z,C),l("group_level",z,C),l("skip",z,C),l("stale",z,C),l("conflicts",z,C),l("startkey",z,C,!0),l("start_key",z,C,!0),l("endkey",z,C,!0),l("end_key",z,C,!0),l("inclusive_end",z,C),l("key",z,C,!0),l("update_seq",z,C),C=C.join("&"),C=C===""?"":"?"+C,typeof z.keys<"u"){let D=`keys=${encodeURIComponent(JSON.stringify(z.keys))}`;D.length+C.length+1<=2e3?C+=(C[0]==="?"?"&":"?")+D:(T="POST",typeof p=="string"?L={keys:z.keys}:p.keys=z.keys)}if(typeof p=="string"){let S=g4(p),D=yield g.fetch("_design/"+S[0]+"/_view/"+S[1]+C,{headers:new R0({"Content-Type":"application/json"}),method:T,body:JSON.stringify(L)});J=D.ok;let $=yield D.json();if(!J)throw $.status=D.status,N0($);for(let Q of $.rows)if(Q.value&&Q.value.error&&Q.value.error==="builtin_reduce_error")throw new Error(Q.reason);return new Promise(function(Q){Q($)}).then(k(z))}L=L||{};for(let S of Object.keys(p))Array.isArray(p[S])?L[S]=p[S]:L[S]=p[S].toString();let I=yield g.fetch("_temp_view"+C,{headers:new R0({"Content-Type":"application/json"}),method:"POST",body:JSON.stringify(L)});J=I.ok;let B=yield I.json();if(!J)throw B.status=I.status,N0(B);return new Promise(function(S){S(B)}).then(k(z))})}function u(g,p,z){return new Promise(function(C,L){g._query(p,z,function(T,J){if(T)return L(T);C(J)})})}function h(g){return new Promise(function(p,z){g._viewCleanup(function(C,L){if(C)return z(C);p(L)})})}function r(g){return function(p){if(p.status===404)return g;throw p}}function v(g,p,z){return X(this,null,function*(){let C="_local/doc_"+g,L={_id:C,keys:[]},T=z.get(g),J=T[0],I=T[1];function B(){return ps(I)?Promise.resolve(L):p.db.get(C).catch(r(L))}function S(l1){return l1.keys.length?p.db.allDocs({keys:l1.keys,include_docs:!0}):Promise.resolve({rows:[]})}function D(l1,w1){let M1=[],j1=new Set;for(let o1 of w1.rows){let t1=o1.doc;if(t1&&(M1.push(t1),j1.add(t1._id),t1._deleted=!J.has(t1._id),!t1._deleted)){let a1=J.get(t1._id);"value"in a1&&(t1.value=a1.value)}}let P1=v4(J);for(let o1 of P1)if(!j1.has(o1)){let t1={_id:o1},a1=J.get(o1);"value"in a1&&(t1.value=a1.value),M1.push(t1)}return l1.keys=$n(P1.concat(l1.keys)),M1.push(l1),M1}let $=yield B(),Q=yield S($);return D($,Q)})}function w(g){return g.sourceDB.get("_local/purges").then(function(p){let z=p.purgeSeq;return g.db.get("_local/purgeSeq").then(function(C){return C._rev}).catch(r(void 0)).then(function(C){return g.db.put({_id:"_local/purgeSeq",_rev:C,purgeSeq:z})})}).catch(function(p){if(p.status!==404)throw p})}function f(g,p,z){var C="_local/lastSeq";return g.db.get(C).catch(r({_id:C,seq:0})).then(function(L){var T=v4(p);return Promise.all(T.map(function(J){return v(J,g,p)})).then(function(J){var I=J.flat();return L.seq=z,I.push(L),g.db.bulkDocs({docs:I})}).then(()=>w(g))})}function O(g){let p=typeof g=="string"?g:g.name,z=Wn[p];return z||(z=Wn[p]=new i2),z}function E(g,p){return X(this,null,function*(){return d4(O(g),function(){return V(g,p)})()})}function V(g,p){return X(this,null,function*(){let z,C,L;function T(o1,t1){let a1={id:C._id,key:I0(o1)};typeof t1<"u"&&t1!==null&&(a1.value=I0(t1)),z.push(a1)}let J=o(g.mapFun,T),I=g.seq||0;function B(){return g.sourceDB.info().then(function(o1){L=g.sourceDB.activeTasks.add({name:"view_indexing",total_items:o1.update_seq-I})})}function S(o1,t1){return function(){return f(g,o1,t1)}}let D=0,$={view:g.name,indexed_docs:D};g.sourceDB.emit("indexing",$);let Q=new i2;function l1(){return X(this,null,function*(){let o1=yield g.sourceDB.changes({return_docs:!0,conflicts:!0,include_docs:!0,style:"all_docs",since:I,limit:p.changes_batch_size}),t1=yield w1();return M1(o1,t1)})}function w1(){return g.db.get("_local/purgeSeq").then(function(o1){return o1.purgeSeq}).catch(r(-1)).then(function(o1){return g.sourceDB.get("_local/purges").then(function(t1){let a1=t1.purges.filter(function(C1,h1){return h1>o1}).map(C1=>C1.docId),B1=a1.filter(function(C1,h1){return a1.indexOf(C1)===h1});return Promise.all(B1.map(function(C1){return g.sourceDB.get(C1).then(function(h1){return{docId:C1,doc:h1}}).catch(r({docId:C1}))}))}).catch(r([]))})}function M1(o1,t1){let a1=o1.results;if(!a1.length&&!t1.length)return;for(let h1 of t1)if(a1.findIndex(function(Z1){return Z1.id===h1.docId})<0){let Z1={_id:h1.docId,doc:{_id:h1.docId,_deleted:1},changes:[]};h1.doc&&(Z1.doc=h1.doc,Z1.changes.push({rev:h1.doc._rev})),a1.push(Z1)}let B1=j1(a1);Q.add(S(B1,I)),D=D+a1.length;let C1={view:g.name,last_seq:o1.last_seq,results_count:a1.length,indexed_docs:D};if(g.sourceDB.emit("indexing",C1),g.sourceDB.activeTasks.update(L,{completed_items:D}),!(a1.length<p.changes_batch_size))return l1()}function j1(o1){let t1=new Map;for(let a1 of o1){if(a1.doc._id[0]!=="_"){z=[],C=a1.doc,C._deleted||i(g.sourceDB,J,C),z.sort(s);let B1=P1(z);t1.set(a1.doc._id,[B1,a1.changes])}I=a1.seq}return t1}function P1(o1){let t1=new Map,a1;for(let B1=0,C1=o1.length;B1<C1;B1++){let h1=o1[B1],m0=[h1.key,h1.id];B1>0&&L1(h1.key,a1)===0&&m0.push(B1),t1.set(X1(m0),h1),a1=h1.key}return t1}try{yield B(),yield l1(),yield Q.finish(),g.seq=I,g.sourceDB.activeTasks.remove(L)}catch(o1){g.sourceDB.activeTasks.remove(L,o1)}})}function n1(g,p,z){z.group_level===0&&delete z.group_level;let C=z.group||z.group_level,L=t(g.reduceFun),T=[],J=isNaN(z.group_level)?Number.POSITIVE_INFINITY:z.group_level;for(let I of p){let B=T[T.length-1],S=C?I.key:null;if(C&&Array.isArray(S)&&(S=S.slice(0,J)),B&&L1(B.groupKey,S)===0){B.keys.push([I.key,I.id]),B.values.push(I.value);continue}T.push({keys:[[I.key,I.id]],values:[I.value],groupKey:S})}p=[];for(let I of T){let B=a(g.sourceDB,L,I.keys,I.values,!1);if(B.error&&B.error instanceof E2)throw B.error;p.push({value:B.error?null:B.output,key:I.groupKey})}return{rows:c(p,z.limit,z.skip)}}function Y(g,p){return d4(O(g),function(){return r1(g,p)})()}function r1(g,p){return X(this,null,function*(){let z,C=g.reduceFun&&p.reduce!==!1,L=p.skip||0;typeof p.keys<"u"&&!p.keys.length&&(p.limit=0,delete p.keys);function T(I){return X(this,null,function*(){I.include_docs=!0;let B=yield g.db.allDocs(I);return z=B.total_rows,B.rows.map(function(S){if("value"in S.doc&&typeof S.doc.value=="object"&&S.doc.value!==null){let $=Object.keys(S.doc.value).sort(),Q=["id","key","value"];if(!($<Q||$>Q))return S.doc.value}let D=r5(S.doc._id);return{key:D[0],id:D[1],value:"value"in S.doc?S.doc.value:null}})})}function J(I){return X(this,null,function*(){let B;if(C?B=n1(g,I,p):typeof p.keys>"u"?B={total_rows:z,offset:L,rows:I}:B={total_rows:z,offset:L,rows:c(I,p.limit,p.skip)},p.update_seq&&(B.update_seq=g.seq),p.include_docs){let S=$n(I.map(d)),D=yield g.sourceDB.allDocs({keys:S,include_docs:!0,conflicts:p.conflicts,attachments:p.attachments,binary:p.binary}),$=new Map;for(let Q of D.rows)$.set(Q.id,Q.doc);for(let Q of I){let l1=d(Q),w1=$.get(l1);w1&&(Q.doc=w1)}}return B})}if(typeof p.keys<"u"){let B=p.keys.map(function($){let Q={startkey:X1([$]),endkey:X1([$,{}])};return p.update_seq&&(Q.update_seq=!0),T(Q)}),D=(yield Promise.all(B)).flat();return J(D)}else{let I={descending:p.descending};p.update_seq&&(I.update_seq=!0);let B,S;if("start_key"in p&&(B=p.start_key),"startkey"in p&&(B=p.startkey),"end_key"in p&&(S=p.end_key),"endkey"in p&&(S=p.endkey),typeof B<"u"&&(I.startkey=p.descending?X1([B,{}]):X1([B])),typeof S<"u"){let $=p.inclusive_end!==!1;p.descending&&($=!$),I.endkey=X1($?[S,{}]:[S])}if(typeof p.key<"u"){let $=X1([p.key]),Q=X1([p.key,{}]);I.descending?(I.endkey=$,I.startkey=Q):(I.startkey=$,I.endkey=Q)}C||(typeof p.limit=="number"&&(I.limit=p.limit),I.skip=L);let D=yield T(I);return J(D)}})}function e1(g){return X(this,null,function*(){return(yield g.fetch("_view_cleanup",{headers:new R0({"Content-Type":"application/json"}),method:"POST"})).json()})}function i1(g){return X(this,null,function*(){try{let p=yield g.get("_local/"+e),z=new Map;for(let B of Object.keys(p.views)){let S=g4(B),D="_design/"+S[0],$=S[1],Q=z.get(D);Q||(Q=new Set,z.set(D,Q)),Q.add($)}let C={keys:v4(z),include_docs:!0},L=yield g.allDocs(C),T={};for(let B of L.rows){let S=B.key.substring(8);for(let D of z.get(B.key)){let $=S+"/"+D;p.views[$]||($=D);let Q=Object.keys(p.views[$]),l1=B.doc&&B.doc.views&&B.doc.views[D];for(let w1 of Q)T[w1]=T[w1]||l1}}let I=Object.keys(T).filter(function(B){return!T[B]}).map(function(B){return d4(O(B),function(){return new g.constructor(B,g.__opts).destroy()})()});return Promise.all(I).then(function(){return{ok:!0}})}catch(p){if(p.status===404)return{ok:!0};throw p}})}function v1(g,p,z){return X(this,null,function*(){if(typeof g._query=="function")return u(g,p,z);if(o0(g))return y(g,p,z);let C={changes_batch_size:g.__opts.view_update_changes_batch_size||us};if(typeof p!="string")return A(z,p),Zn.add(function(){return X(this,null,function*(){let L=yield Kn(g,"temp_view/temp_view",p.map,p.reduce,!0,e);return ls(E(L,C).then(function(){return Y(L,z)}),function(){return L.db.destroy()})})}),Zn.finish();{let L=p,T=g4(L),J=T[0],I=T[1],B=yield g.get("_design/"+J);if(p=B.views&&B.views[I],!p)throw new D2(`ddoc ${B._id} has no view named ${I}`);n(B,I),A(z,p);let S=yield Kn(g,L,p.map,p.reduce,!1,e);return z.stale==="ok"||z.stale==="update_after"?(z.stale==="update_after"&&a0(function(){E(S,C)}),Y(S,z)):(yield E(S,C),Y(S,z))}})}function g1(g,p,z){let C=this;typeof p=="function"&&(z=p,p={}),p=p?_(p):{},typeof g=="function"&&(g={map:g});let L=Promise.resolve().then(function(){return v1(C,g,p)});return H6(L,z),L}let u1=cs(function(){let g=this;return typeof g._viewCleanup=="function"?h(g):o0(g)?e1(g):i1(g)});return{query:g1,viewCleanup:u1}}var h4={_sum:function(e,o){return L4(o)},_count:function(e,o){return o.length},_stats:function(e,o){function t(n){for(var i=0,a=0,s=n.length;a<s;a++){var c=n[a];i+=c*c}return i}return{sum:L4(o),min:Math.min.apply(null,o),max:Math.max.apply(null,o),count:o.length,sumsqr:t(o)}}};function ws(e){if(/^_sum/.test(e))return h4._sum;if(/^_count/.test(e))return h4._count;if(/^_stats/.test(e))return h4._stats;if(/^_/.test(e))throw new Error(e+" is not a supported reduce function.")}function fs(e,o){if(typeof e=="function"&&e.length===2){var t=e;return function(n){return t(n,o)}}else return V6(e.toString(),o)}function xs(e){var o=e.toString(),t=ws(o);return t||V6(o)}function Ms(e,o){var t=e.views&&e.views[o];if(typeof t.map!="string")throw new D2("ddoc "+e._id+" has no string view named "+o+", instead found object of type: "+typeof t.map)}var ks="mrviews",O6=ms(ks,fs,xs,Ms);function zs(e,o,t){return O6.query.call(this,e,o,t)}function ys(e){return O6.viewCleanup.call(this,e)}var Cs={query:zs,viewCleanup:ys};function _s(e,o,t){return!e._attachments||!e._attachments[t]||e._attachments[t].digest!==o._attachments[t].digest}function Xn(e,o){var t=Object.keys(o._attachments);return Promise.all(t.map(function(n){return e.getAttachment(o._id,n,{rev:o._rev})}))}function Bs(e,o,t){var n=o0(o)&&!o0(e),i=Object.keys(t._attachments);return n?e.get(t._id).then(function(a){return Promise.all(i.map(function(s){return _s(a,t,s)?o.getAttachment(t._id,s):e.getAttachment(a._id,s)}))}).catch(function(a){if(a.status!==404)throw a;return Xn(o,t)}):Xn(o,t)}function bs(e){var o=[];return Object.keys(e).forEach(function(t){var n=e[t].missing;n.forEach(function(i){o.push({id:t,rev:i})})}),{docs:o,revs:!0,latest:!0}}function Ss(e,o,t,n){t=V1(t);var i=[],a=!0;function s(){var d=bs(t);if(d.docs.length)return e.bulkGet(d).then(function(x){if(n.cancelled)throw new Error("cancelled");return Promise.all(x.results.map(function(k){return Promise.all(k.docs.map(function(l){var M=l.ok;return l.error&&(a=!1),!M||!M._attachments?M:Bs(o,e,M).then(_=>{var b=Object.keys(M._attachments);return _.forEach(function(A,y){var u=M._attachments[b[y]];delete u.stub,delete u.length,u.data=A}),M})}))})).then(function(k){i=i.concat(k.flat().filter(Boolean))})})}function c(){return{ok:a,docs:i}}return Promise.resolve().then(s).then(c)}var Jn=1,Yn="pouchdb",As=5,Q1=0;function I4(e,o,t,n,i){return e.get(o).catch(function(a){if(a.status===404)return(e.adapter==="http"||e.adapter==="https")&&m4(404,"PouchDB is just checking if a remote checkpoint exists."),{session_id:n,_id:o,history:[],replicator:Yn,version:Jn};throw a}).then(function(a){if(!i.cancelled&&a.last_seq!==t)return a.history=(a.history||[]).filter(function(s){return s.session_id!==n}),a.history.unshift({last_seq:t,session_id:n}),a.history=a.history.slice(0,As),a.version=Jn,a.replicator=Yn,a.session_id=n,a.last_seq=t,e.put(a).catch(function(s){if(s.status===409)return I4(e,o,t,n,i);throw s})})}var T2=class{constructor(o,t,n,i,a={writeSourceCheckpoint:!0,writeTargetCheckpoint:!0}){this.src=o,this.target=t,this.id=n,this.returnValue=i,this.opts=a,typeof a.writeSourceCheckpoint>"u"&&(a.writeSourceCheckpoint=!0),typeof a.writeTargetCheckpoint>"u"&&(a.writeTargetCheckpoint=!0)}writeCheckpoint(o,t){var n=this;return this.updateTarget(o,t).then(function(){return n.updateSource(o,t)})}updateTarget(o,t){return this.opts.writeTargetCheckpoint?I4(this.target,this.id,o,t,this.returnValue):Promise.resolve(!0)}updateSource(o,t){if(this.opts.writeSourceCheckpoint){var n=this;return I4(this.src,this.id,o,t,this.returnValue).catch(function(i){if(t6(i))return n.opts.writeSourceCheckpoint=!1,!0;throw i})}else return Promise.resolve(!0)}getCheckpoint(){var o=this;return!o.opts.writeSourceCheckpoint&&!o.opts.writeTargetCheckpoint?Promise.resolve(Q1):o.opts&&o.opts.writeSourceCheckpoint&&!o.opts.writeTargetCheckpoint?o.src.get(o.id).then(function(t){return t.last_seq||Q1}).catch(function(t){if(t.status!==404)throw t;return Q1}):o.target.get(o.id).then(function(t){return o.opts&&o.opts.writeTargetCheckpoint&&!o.opts.writeSourceCheckpoint?t.last_seq||Q1:o.src.get(o.id).then(function(n){if(t.version!==n.version)return Q1;var i;return t.version?i=t.version.toString():i="undefined",i in e6?e6[i](t,n):Q1},function(n){if(n.status===404&&t.last_seq)return o.src.put({_id:o.id,last_seq:Q1}).then(function(){return Q1},function(i){return t6(i)?(o.opts.writeSourceCheckpoint=!1,t.last_seq):Q1});throw n})}).catch(function(t){if(t.status!==404)throw t;return Q1})}},e6={undefined:function(e,o){return L1(e.last_seq,o.last_seq)===0?o.last_seq:0},1:function(e,o){return Ls(o,e).last_seq}};function Ls(e,o){return e.session_id===o.session_id?{last_seq:e.last_seq,history:e.history}:D6(e.history,o.history)}function D6(e,o){var t=e[0],n=e.slice(1),i=o[0],a=o.slice(1);if(!t||o.length===0)return{last_seq:Q1,history:[]};var s=t.session_id;if(j4(s,o))return{last_seq:t.last_seq,history:e};var c=i.session_id;return j4(c,n)?{last_seq:i.last_seq,history:a}:D6(n,a)}function j4(e,o){var t=o[0],n=o.slice(1);return!e||o.length===0?!1:e===t.session_id?!0:j4(e,n)}function t6(e){return typeof e.status=="number"&&Math.floor(e.status/100)===4}function E6(e,o,t,n,i){return this instanceof T2?E6:new T2(e,o,t,n,i)}var n6=0;function Is(e,o,t,n){if(e.retry===!1){o.emit("error",t),o.removeAllListeners();return}if(typeof e.back_off_function!="function"&&(e.back_off_function=oa),o.emit("requestError",t),o.state==="active"||o.state==="pending"){o.emit("paused",t),o.state="stopped";var i=function(){e.current_back_off=n6},a=function(){o.removeListener("active",i)};o.once("paused",a),o.once("active",i)}e.current_back_off=e.current_back_off||n6,e.current_back_off=e.back_off_function(e.current_back_off),setTimeout(n,e.current_back_off)}function js(e){return Object.keys(e).sort(L1).reduce(function(o,t){return o[t]=e[t],o},{})}function Hs(e,o,t){var n=t.doc_ids?t.doc_ids.sort(L1):"",i=t.filter?t.filter.toString():"",a="",s="",c="";return t.selector&&(c=JSON.stringify(t.selector)),t.filter&&t.query_params&&(a=JSON.stringify(js(t.query_params))),t.filter&&t.filter==="_view"&&(s=t.view.toString()),Promise.all([e.id(),o.id()]).then(function(d){var x=d[0]+d[1]+i+s+a+n+c;return new Promise(function(k){G4(x,k)})}).then(function(d){return d=d.replace(/\//g,".").replace(/\+/g,"_"),"_local/"+d})}function T6(e,o,t,n,i){var a=[],s,c={seq:0,changes:[],docs:[]},d=!1,x=!1,k=!1,l=0,M=0,_=t.continuous||t.live||!1,b=t.batch_size||100,A=t.batches_limit||10,y=t.style||"all_docs",u=!1,h=t.doc_ids,r=t.selector,v,w,f=[],O=q2(),E;i=i||{ok:!0,start_time:new Date().toISOString(),docs_read:0,docs_written:0,doc_write_failures:0,errors:[]};var V={};n.ready(e,o);function n1(){return w?Promise.resolve():Hs(e,o,t).then(function(B){v=B;var S={};t.checkpoint===!1?S={writeSourceCheckpoint:!1,writeTargetCheckpoint:!1}:t.checkpoint==="source"?S={writeSourceCheckpoint:!0,writeTargetCheckpoint:!1}:t.checkpoint==="target"?S={writeSourceCheckpoint:!1,writeTargetCheckpoint:!0}:S={writeSourceCheckpoint:!0,writeTargetCheckpoint:!0},w=new E6(e,o,v,n,S)})}function Y(){if(f=[],s.docs.length!==0){var B=s.docs,S={timeout:t.timeout};return o.bulkDocs({docs:B,new_edits:!1},S).then(function(D){if(n.cancelled)throw g(),new Error("cancelled");var $=Object.create(null);D.forEach(function(l1){l1.error&&($[l1.id]=l1)});var Q=Object.keys($).length;i.doc_write_failures+=Q,i.docs_written+=B.length-Q,B.forEach(function(l1){var w1=$[l1._id];if(w1){i.errors.push(w1);var M1=(w1.name||"").toLowerCase();if(M1==="unauthorized"||M1==="forbidden")n.emit("denied",V1(w1));else throw w1}else f.push(l1)})},function(D){throw i.doc_write_failures+=B.length,D})}}function r1(){if(s.error)throw new Error("There was a problem getting docs.");i.last_seq=M=s.seq;var B=V1(i);return f.length&&(B.docs=f,typeof s.pending=="number"&&(B.pending=s.pending,delete s.pending),n.emit("change",B)),d=!0,e.info().then(function(S){var D=e.activeTasks.get(E);if(!(!s||!D)){var $=D.completed_items||0,Q=parseInt(S.update_seq,10)-parseInt(l,10);e.activeTasks.update(E,{completed_items:$+s.changes.length,total_items:Q})}}),w.writeCheckpoint(s.seq,O).then(function(){if(n.emit("checkpoint",{checkpoint:s.seq}),d=!1,n.cancelled)throw g(),new Error("cancelled");s=void 0,L()}).catch(function(S){throw I(S),S})}function e1(){var B={};return s.changes.forEach(function(S){n.emit("checkpoint",{revs_diff:S}),S.id!=="_user/"&&(B[S.id]=S.changes.map(function(D){return D.rev}))}),o.revsDiff(B).then(function(S){if(n.cancelled)throw g(),new Error("cancelled");s.diffs=S})}function i1(){return Ss(e,o,s.diffs,n).then(function(B){s.error=!B.ok,B.docs.forEach(function(S){delete s.diffs[S._id],i.docs_read++,s.docs.push(S)})})}function v1(){if(!(n.cancelled||s)){if(a.length===0){g1(!0);return}s=a.shift(),n.emit("checkpoint",{start_next_batch:s.seq}),e1().then(i1).then(Y).then(r1).then(v1).catch(function(B){u1("batch processing terminated with error",B)})}}function g1(B){if(c.changes.length===0){a.length===0&&!s&&((_&&V.live||x)&&(n.state="pending",n.emit("paused")),x&&g());return}(B||x||c.changes.length>=b)&&(a.push(c),c={seq:0,changes:[],docs:[]},(n.state==="pending"||n.state==="stopped")&&(n.state="active",n.emit("active")),v1())}function u1(B,S){k||(S.message||(S.message=B),i.ok=!1,i.status="aborting",a=[],c={seq:0,changes:[],docs:[]},g(S))}function g(B){if(!k&&!(n.cancelled&&(i.status="cancelled",d)))if(i.status=i.status||"complete",i.end_time=new Date().toISOString(),i.last_seq=M,k=!0,e.activeTasks.remove(E,B),B){B=c1(B),B.result=i;var S=(B.name||"").toLowerCase();S==="unauthorized"||S==="forbidden"?(n.emit("error",B),n.removeAllListeners()):Is(t,n,B,function(){T6(e,o,t,n)})}else n.emit("complete",i),n.removeAllListeners()}function p(B,S,D){if(n.cancelled)return g();typeof S=="number"&&(c.pending=S);var $=P4(t)(B);if(!$){var Q=e.activeTasks.get(E);if(Q){var l1=Q.completed_items||0;e.activeTasks.update(E,{completed_items:++l1})}return}c.seq=B.seq||D,c.changes.push(B),n.emit("checkpoint",{pending_batch:c.seq}),a0(function(){g1(a.length===0&&V.live)})}function z(B){if(u=!1,n.cancelled)return g();if(B.results.length>0)V.since=B.results[B.results.length-1].seq,L(),g1(!0);else{var S=function(){_?(V.live=!0,L()):x=!0,g1(!0)};!s&&B.results.length===0?(d=!0,w.writeCheckpoint(B.last_seq,O).then(function(){if(d=!1,i.last_seq=M=B.last_seq,n.cancelled)throw g(),new Error("cancelled");S()}).catch(I)):S()}}function C(B){if(u=!1,n.cancelled)return g();u1("changes rejected",B)}function L(){if(!(!u&&!x&&a.length<A))return;u=!0;function B(){D.cancel()}function S(){n.removeListener("cancel",B)}n._changes&&(n.removeListener("cancel",n._abortChanges),n._changes.cancel()),n.once("cancel",B);var D=e.changes(V).on("change",p);D.then(S,S),D.then(z).catch(C),t.retry&&(n._changes=D,n._abortChanges=B)}function T(B){return e.info().then(function(S){var D=typeof t.since>"u"?parseInt(S.update_seq,10)-parseInt(B,10):parseInt(S.update_seq,10);return E=e.activeTasks.add({name:`${_?"continuous ":""}replication from ${S.db_name}`,total_items:D}),B})}function J(){n1().then(function(){if(n.cancelled){g();return}return w.getCheckpoint().then(T).then(function(B){M=B,l=B,V={since:M,limit:b,batch_size:b,style:y,doc_ids:h,selector:r,return_docs:!0},t.filter&&(typeof t.filter!="string"?V.include_docs=!0:V.filter=t.filter),"heartbeat"in t&&(V.heartbeat=t.heartbeat),"timeout"in t&&(V.timeout=t.timeout),t.query_params&&(V.query_params=t.query_params),t.view&&(V.view=t.view),L()})}).catch(function(B){u1("getCheckpoint rejected with ",B)})}function I(B){d=!1,u1("writeCheckpoint completed with error",B)}if(n.cancelled){g();return}n._addedListeners||(n.once("cancel",g),typeof t.complete=="function"&&(n.once("error",t.complete),n.once("complete",function(B){t.complete(null,B)})),n._addedListeners=!0),typeof t.since>"u"?J():n1().then(function(){return d=!0,w.writeCheckpoint(t.since,O)}).then(function(){if(d=!1,n.cancelled){g();return}M=t.since,J()}).catch(I)}var H4=class extends i0.default{constructor(){super(),this.cancelled=!1,this.state="pending";let o=new Promise((t,n)=>{this.once("complete",t),this.once("error",n)});this.then=function(t,n){return o.then(t,n)},this.catch=function(t){return o.catch(t)},this.catch(function(){})}cancel(){this.cancelled=!0,this.state="cancelled",this.emit("cancel")}ready(o,t){if(this._readyCalled)return;this._readyCalled=!0;let n=()=>{this.cancel()};o.once("destroyed",n),t.once("destroyed",n);function i(){o.removeListener("destroyed",n),t.removeListener("destroyed",n)}this.once("complete",i),this.once("error",i)}};function P2(e,o){var t=o.PouchConstructor;return typeof e=="string"?new t(e,o):e}function V4(e,o,t,n){if(typeof t=="function"&&(n=t,t={}),typeof t>"u"&&(t={}),t.doc_ids&&!Array.isArray(t.doc_ids))throw c1(R2,"`doc_ids` filter parameter is not a list.");t.complete=n,t=V1(t),t.continuous=t.continuous||t.live,t.retry="retry"in t?t.retry:!1,t.PouchConstructor=t.PouchConstructor||this;var i=new H4(t),a=P2(e,t),s=P2(o,t);return T6(a,s,t,i),i}function Vs(e,o,t,n){return typeof t=="function"&&(n=t,t={}),typeof t>"u"&&(t={}),t=V1(t),t.PouchConstructor=t.PouchConstructor||this,e=P2(e,t),o=P2(o,t),new O4(e,o,t,n)}var O4=class extends i0.default{constructor(o,t,n,i){super(),this.canceled=!1;let a=n.push?Object.assign({},n,n.push):n,s=n.pull?Object.assign({},n,n.pull):n;this.push=V4(o,t,a),this.pull=V4(t,o,s),this.pushPaused=!0,this.pullPaused=!0;let c=r=>{this.emit("change",{direction:"pull",change:r})},d=r=>{this.emit("change",{direction:"push",change:r})},x=r=>{this.emit("denied",{direction:"push",doc:r})},k=r=>{this.emit("denied",{direction:"pull",doc:r})},l=()=>{this.pushPaused=!0,this.pullPaused&&this.emit("paused")},M=()=>{this.pullPaused=!0,this.pushPaused&&this.emit("paused")},_=()=>{this.pushPaused=!1,this.pullPaused&&this.emit("active",{direction:"push"})},b=()=>{this.pullPaused=!1,this.pushPaused&&this.emit("active",{direction:"pull"})},A={},y=r=>(v,w)=>{(v==="change"&&(w===c||w===d)||v==="denied"&&(w===k||w===x)||v==="paused"&&(w===M||w===l)||v==="active"&&(w===b||w===_))&&(v in A||(A[v]={}),A[v][r]=!0,Object.keys(A[v]).length===2&&this.removeAllListeners(v))};n.live&&(this.push.on("complete",this.pull.cancel.bind(this.pull)),this.pull.on("complete",this.push.cancel.bind(this.push)));function u(r,v,w){r.listeners(v).indexOf(w)==-1&&r.on(v,w)}this.on("newListener",function(r){r==="change"?(u(this.pull,"change",c),u(this.push,"change",d)):r==="denied"?(u(this.pull,"denied",k),u(this.push,"denied",x)):r==="active"?(u(this.pull,"active",b),u(this.push,"active",_)):r==="paused"&&(u(this.pull,"paused",M),u(this.push,"paused",l))}),this.on("removeListener",function(r){r==="change"?(this.pull.removeListener("change",c),this.push.removeListener("change",d)):r==="denied"?(this.pull.removeListener("denied",k),this.push.removeListener("denied",x)):r==="active"?(this.pull.removeListener("active",b),this.push.removeListener("active",_)):r==="paused"&&(this.pull.removeListener("paused",M),this.push.removeListener("paused",l))}),this.pull.on("removeListener",y("pull")),this.push.on("removeListener",y("push"));let h=Promise.all([this.push,this.pull]).then(r=>{let v={push:r[0],pull:r[1]};return this.emit("complete",v),i&&i(null,v),this.removeAllListeners(),v},r=>{if(this.cancel(),i?i(r):this.emit("error",r),this.removeAllListeners(),i)throw r});this.then=function(r,v){return h.then(r,v)},this.catch=function(r){return h.catch(r)}}cancel(){this.canceled||(this.canceled=!0,this.push.cancel(),this.pull.cancel())}};function Os(e){e.replicate=V4,e.sync=Vs,Object.defineProperty(e.prototype,"replicate",{get:function(){var o=this;return typeof this.replicateMethods>"u"&&(this.replicateMethods={from:function(t,n,i){return o.constructor.replicate(t,o,n,i)},to:function(t,n,i){return o.constructor.replicate(o,t,n,i)}}),this.replicateMethods}}),e.prototype.sync=function(o,t,n){return this.constructor.sync(this,o,t,n)}}d1.plugin(Y5).plugin(rs).plugin(Cs).plugin(Os);var X4=d1;var y1=class extends Error{constructor(o,t,n){super(),this.status=o,this.name=t,this.message=n,this.error=!0}toString(){return JSON.stringify({status:this.status,name:this.name,message:this.message,reason:this.reason})}},E7=new y1(401,"unauthorized","Name or password is incorrect."),T7=new y1(400,"bad_request","Missing JSON list of 'docs'"),P7=new y1(404,"not_found","missing"),R7=new y1(409,"conflict","Document update conflict"),Ds=new y1(400,"bad_request","_id field must contain a string"),Es=new y1(412,"missing_id","_id is required for puts"),Ts=new y1(400,"bad_request","Only reserved document ids may start with underscore."),q7=new y1(412,"precondition_failed","Database not open"),Ps=new y1(500,"unknown_error","Database encountered an unknown error"),F7=new y1(500,"badarg","Some query argument is invalid"),N7=new y1(400,"invalid_request","Request was invalid"),$7=new y1(400,"query_parse_error","Some query parameter is invalid"),U7=new y1(500,"doc_validation","Bad special document member"),Rs=new y1(400,"bad_request","Something wrong with the request"),G7=new y1(400,"bad_request","Document must be a JSON object"),K7=new y1(404,"not_found","Database not found"),W7=new y1(500,"indexed_db_went_bad","unknown"),Z7=new y1(500,"web_sql_went_bad","unknown"),Q7=new y1(500,"levelDB_went_went_bad","unknown"),X7=new y1(403,"forbidden","Forbidden by design doc validate_doc_update function"),J7=new y1(400,"bad_request","Invalid rev format"),Y7=new y1(412,"file_exists","The database could not be created, the file already exists."),e9=new y1(412,"missing_stub","A pre-existing attachment stub wasn't found"),t9=new y1(413,"invalid_url","Provided URL is invalid");function J4(e,o){function t(n){for(var i=Object.getOwnPropertyNames(e),a=0,s=i.length;a<s;a++)typeof e[i[a]]!="function"&&(this[i[a]]=e[i[a]]);this.stack===void 0&&(this.stack=new Error().stack),n!==void 0&&(this.reason=n)}return t.prototype=y1.prototype,new t(o)}function r2(e){if(typeof e!="object"){var o=e;e=Ps,e.data=o}return"error"in e&&e.error==="conflict"&&(e.name="conflict",e.status=409),"name"in e||(e.name=e.error||"unknown"),"status"in e||(e.status=500),"message"in e||(e.message=e.message||e.reason),"stack"in e||(e.stack=new Error().stack),e}var $0=Headers;var qs=function(e){return atob(e)};function Fs(e,o){e=e||[],o=o||{};try{return new Blob(e,o)}catch(a){if(a.name!=="TypeError")throw a;for(var t=typeof BlobBuilder<"u"?BlobBuilder:typeof MSBlobBuilder<"u"?MSBlobBuilder:typeof MozBlobBuilder<"u"?MozBlobBuilder:WebKitBlobBuilder,n=new t,i=0;i<e.length;i+=1)n.append(e[i]);return n.getBlob(o.type)}}function Ns(e){for(var o=e.length,t=new ArrayBuffer(o),n=new Uint8Array(t),i=0;i<o;i++)n[i]=e.charCodeAt(i);return t}function $s(e,o){return Fs([Ns(e)],{type:o})}function P6(e,o){return $s(qs(e),o)}function Us(e,o,t){for(var n="",i=t-e.length;n.length<i;)n+=o;return n}function Gs(e,o,t){var n=Us(e,o,t);return n+e}var R6=-324,Y4=3,e3="";function b1(e,o){if(e===o)return 0;e=h0(e),o=h0(o);var t=t3(e),n=t3(o);if(t-n!==0)return t-n;switch(typeof e){case"number":return e-o;case"boolean":return e<o?-1:1;case"string":return Xs(e,o)}return Array.isArray(e)?Qs(e,o):Js(e,o)}function h0(e){switch(typeof e){case"undefined":return null;case"number":return e===1/0||e===-1/0||isNaN(e)?null:e;case"object":var o=e;if(Array.isArray(e)){var t=e.length;e=new Array(t);for(var n=0;n<t;n++)e[n]=h0(o[n])}else{if(e instanceof Date)return e.toJSON();if(e!==null){e={};for(var i in o)if(Object.prototype.hasOwnProperty.call(o,i)){var a=o[i];typeof a<"u"&&(e[i]=h0(a))}}}}return e}function Ks(e){if(e!==null)switch(typeof e){case"boolean":return e?1:0;case"number":return Ys(e);case"string":return e.replace(/\u0002/g,"").replace(/\u0001/g,"").replace(/\u0000/g,"");case"object":var o=Array.isArray(e),t=o?e:Object.keys(e),n=-1,i=t.length,a="";if(o)for(;++n<i;)a+=W1(t[n]);else for(;++n<i;){var s=t[n];a+=W1(s)+W1(e[s])}return a}return""}function W1(e){var o="\0";return e=h0(e),t3(e)+e3+Ks(e)+o}function Ws(e,o){var t=o,n,i=e[o]==="1";if(i)n=0,o++;else{var a=e[o]==="0";o++;var s="",c=e.substring(o,o+Y4),d=parseInt(c,10)+R6;for(a&&(d=-d),o+=Y4;;){var x=e[o];if(x==="\0")break;s+=x,o++}s=s.split("."),s.length===1?n=parseInt(s,10):n=parseFloat(s[0]+"."+s[1]),a&&(n=n-10),d!==0&&(n=parseFloat(n+"e"+d))}return{num:n,length:o-t}}function Zs(e,o){var t=e.pop();if(o.length){var n=o[o.length-1];t===n.element&&(o.pop(),n=o[o.length-1]);var i=n.element,a=n.index;if(Array.isArray(i))i.push(t);else if(a===e.length-2){var s=e.pop();i[s]=t}else e.push(t)}}function q6(e){for(var o=[],t=[],n=0;;){var i=e[n++];if(i==="\0"){if(o.length===1)return o.pop();Zs(o,t);continue}switch(i){case"1":o.push(null);break;case"2":o.push(e[n]==="1"),n++;break;case"3":var a=Ws(e,n);o.push(a.num),n+=a.length;break;case"4":for(var s="";;){var c=e[n];if(c==="\0")break;s+=c,n++}s=s.replace(/\u0001\u0001/g,"\0").replace(/\u0001\u0002/g,"").replace(/\u0002\u0002/g,""),o.push(s);break;case"5":var d={element:[],index:o.length};o.push(d.element),t.push(d);break;case"6":var x={element:{},index:o.length};o.push(x.element),t.push(x);break;default:throw new Error("bad collationIndex or unexpectedly reached end of input: "+i)}}}function Qs(e,o){for(var t=Math.min(e.length,o.length),n=0;n<t;n++){var i=b1(e[n],o[n]);if(i!==0)return i}return e.length===o.length?0:e.length>o.length?1:-1}function Xs(e,o){return e===o?0:e>o?1:-1}function Js(e,o){for(var t=Object.keys(e),n=Object.keys(o),i=Math.min(t.length,n.length),a=0;a<i;a++){var s=b1(t[a],n[a]);if(s!==0||(s=b1(e[t[a]],o[n[a]]),s!==0))return s}return t.length===n.length?0:t.length>n.length?1:-1}function t3(e){var o=["boolean","number","string","object"],t=o.indexOf(typeof e);if(~t)return e===null?1:Array.isArray(e)?5:t<3?t+2:t+3;if(Array.isArray(e))return 5}function Ys(e){if(e===0)return"1";var o=e.toExponential().split(/e\+?/),t=parseInt(o[1],10),n=e<0,i=n?"0":"2",a=(n?-t:t)-R6,s=Gs(a.toString(),"0",Y4);i+=e3+s;var c=Math.abs(parseFloat(o[0]));n&&(c=10-c);var d=c.toFixed(20);return d=d.replace(/\.?0+$/,""),i+=e3+d,i}var er=Z0(e4());var F6=Z0(Qt()),l9=self.setImmediate||self.setTimeout;function c2(e){return F6.default.hash(e)}function tr(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer||typeof Blob<"u"&&e instanceof Blob}function nr(e){return e instanceof ArrayBuffer?e.slice(0):e.slice(0,e.size,e.type)}var U6=Function.prototype.toString,or=U6.call(Object);function ir(e){var o=Object.getPrototypeOf(e);if(o===null)return!0;var t=o.constructor;return typeof t=="function"&&t instanceof t&&U6.call(t)==or}function l0(e){var o,t,n;if(!e||typeof e!="object")return e;if(Array.isArray(e)){for(o=[],t=0,n=e.length;t<n;t++)o[t]=l0(e[t]);return o}if(e instanceof Date&&isFinite(e))return e.toISOString();if(tr(e))return nr(e);if(!ir(e))return e;o={};for(t in e)if(Object.prototype.hasOwnProperty.call(e,t)){var i=l0(e[t]);typeof i<"u"&&(o[t]=i)}return o}var N6;try{localStorage.setItem("_pouch_check_localstorage",1),N6=!!localStorage.getItem("_pouch_check_localstorage")}catch{N6=!1}var l2=typeof queueMicrotask=="function"?queueMicrotask:function(o){Promise.resolve().then(o)};function F2(e){if(typeof console<"u"&&typeof console[e]=="function"){var o=Array.prototype.slice.call(arguments,1);console[e].apply(console,o)}}function ar(){}var sr=ar.name,$6;sr?$6=function(e){return e.name}:$6=function(e){var o=e.toString().match(/^\s*function\s*(?:(\S+)\s*)?\(/);return o&&o[1]?o[1]:""};function d0(e){return typeof e._remote=="boolean"?e._remote:typeof e.type=="function"?(F2("warn","db.type() is deprecated and will be removed in a future version of PouchDB"),e.type()==="http"):!1}function U0(e,o,t){return e.get(o).catch(function(n){if(n.status!==404)throw n;return{}}).then(function(n){var i=n._rev,a=t(n);return a?(a._id=o,a._rev=i,rr(e,a,t)):{updated:!1,rev:i}})}function rr(e,o,t){return e.put(o).then(function(n){return{updated:!0,rev:n.rev}},function(n){if(n.status!==409)throw n;return U0(e,o._id,t)})}var u0=class e extends Error{constructor(o){super(),this.status=400,this.name="query_parse_error",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},N2=class e extends Error{constructor(o){super(),this.status=404,this.name="not_found",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},$2=class e extends Error{constructor(o){super(),this.status=500,this.name="invalid_value",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}};function n3(e,o){return o&&e.then(function(t){l2(function(){o(null,t)})},function(t){l2(function(){o(t)})}),e}function G6(e){return function(...o){var t=o.pop(),n=e.apply(this,o);return typeof t=="function"&&n3(n,t),n}}function K6(e,o){return e.then(function(t){return o().then(function(){return t})},function(t){return o().then(function(){throw t})})}function U2(e,o){return function(){var t=arguments,n=this;return e.add(function(){return o.apply(n,t)})}}function o3(e){var o=new Set(e),t=new Array(o.size),n=-1;return o.forEach(function(i){t[++n]=i}),t}function G2(e){var o=new Array(e.size),t=-1;return e.forEach(function(n,i){o[++t]=i}),o}var d2=class{constructor(){this.promise=Promise.resolve()}add(o){return this.promise=this.promise.catch(()=>{}).then(()=>o()),this.promise}finish(){return this.promise}};function W6(e){if(!e)return"undefined";switch(typeof e){case"function":return e.toString();case"string":return e.toString();default:return JSON.stringify(e)}}function cr(e,o){return W6(e)+W6(o)+"undefined"}function Z6(e,o,t,n,i,a){return X(this,null,function*(){let s=cr(t,n),c;if(!i&&(c=e._cachedViews=e._cachedViews||{},c[s]))return c[s];let d=e.info().then(function(x){return X(this,null,function*(){let k=x.db_name+"-mrview-"+(i?"temp":c2(s));function l(y){y.views=y.views||{};let u=o;u.indexOf("/")===-1&&(u=o+"/"+o);let h=y.views[u]=y.views[u]||{};if(!h[k])return h[k]=!0,y}yield U0(e,"_local/"+a,l);let _=(yield e.registerDependentDatabase(k)).db;_.auto_compaction=!0;let b={name:k,db:_,sourceDB:e,adapter:e.adapter,mapFun:t,reduceFun:n},A;try{A=yield b.db.get("_local/lastSeq")}catch(y){if(y.status!==404)throw y}return b.seq=A?A.seq:0,c&&b.db.once("destroyed",function(){delete c[s]}),b})});return c&&(c[s]=d),d})}var Q6={},X6=new d2,lr=50;function i3(e){return e.indexOf("/")===-1?[e,e]:e.split("/")}function dr(e){return e.length===1&&/^1-/.test(e[0].rev)}function J6(e,o,t){try{e.emit("error",o)}catch{F2("error",`The user's map/reduce function threw an uncaught error.
You can debug this error by doing:
myDatabase.on('error', function (err) { debugger; });
Please double-check your map/reduce function.`),F2("error",o,t)}}function vr(e,o,t,n){function i(g,p,z){try{p(z)}catch(C){J6(g,C,{fun:p,doc:z})}}function a(g,p,z,C,L){try{return{output:p(z,C,L)}}catch(T){return J6(g,T,{fun:p,keys:z,values:C,rereduce:L}),{error:T}}}function s(g,p){let z=b1(g.key,p.key);return z!==0?z:b1(g.value,p.value)}function c(g,p,z){return z=z||0,typeof p=="number"?g.slice(z,p+z):z>0?g.slice(z):g}function d(g){let p=g.value;return p&&typeof p=="object"&&p._id||g.id}function x(g){for(let p of g.rows){let z=p.doc&&p.doc._attachments;if(z)for(let C of Object.keys(z)){let L=z[C];z[C].data=P6(L.data,L.content_type)}}}function k(g){return function(p){return g.include_docs&&g.attachments&&g.binary&&x(p),p}}function l(g,p,z,C){let L=p[g];typeof L<"u"&&(C&&(L=encodeURIComponent(JSON.stringify(L))),z.push(g+"="+L))}function M(g){if(typeof g<"u"){let p=Number(g);return!isNaN(p)&&p===parseInt(g,10)?p:g}}function _(g){return g.group_level=M(g.group_level),g.limit=M(g.limit),g.skip=M(g.skip),g}function b(g){if(g){if(typeof g!="number")return new u0(`Invalid value for integer: "${g}"`);if(g<0)return new u0(`Invalid value for positive integer: "${g}"`)}}function A(g,p){let z=g.descending?"endkey":"startkey",C=g.descending?"startkey":"endkey";if(typeof g[z]<"u"&&typeof g[C]<"u"&&b1(g[z],g[C])>0)throw new u0("No rows can match your key range, reverse your start_key and end_key or set {descending : true}");if(p.reduce&&g.reduce!==!1){if(g.include_docs)throw new u0("{include_docs:true} is invalid for reduce");if(g.keys&&g.keys.length>1&&!g.group&&!g.group_level)throw new u0("Multi-key fetches for reduce views must use {group: true}")}for(let L of["group_level","limit","skip"]){let T=b(g[L]);if(T)throw T}}function y(g,p,z){return X(this,null,function*(){let C=[],L,T="GET",J;if(l("reduce",z,C),l("include_docs",z,C),l("attachments",z,C),l("limit",z,C),l("descending",z,C),l("group",z,C),l("group_level",z,C),l("skip",z,C),l("stale",z,C),l("conflicts",z,C),l("startkey",z,C,!0),l("start_key",z,C,!0),l("endkey",z,C,!0),l("end_key",z,C,!0),l("inclusive_end",z,C),l("key",z,C,!0),l("update_seq",z,C),C=C.join("&"),C=C===""?"":"?"+C,typeof z.keys<"u"){let D=`keys=${encodeURIComponent(JSON.stringify(z.keys))}`;D.length+C.length+1<=2e3?C+=(C[0]==="?"?"&":"?")+D:(T="POST",typeof p=="string"?L={keys:z.keys}:p.keys=z.keys)}if(typeof p=="string"){let S=i3(p),D=yield g.fetch("_design/"+S[0]+"/_view/"+S[1]+C,{headers:new $0({"Content-Type":"application/json"}),method:T,body:JSON.stringify(L)});J=D.ok;let $=yield D.json();if(!J)throw $.status=D.status,r2($);for(let Q of $.rows)if(Q.value&&Q.value.error&&Q.value.error==="builtin_reduce_error")throw new Error(Q.reason);return new Promise(function(Q){Q($)}).then(k(z))}L=L||{};for(let S of Object.keys(p))Array.isArray(p[S])?L[S]=p[S]:L[S]=p[S].toString();let I=yield g.fetch("_temp_view"+C,{headers:new $0({"Content-Type":"application/json"}),method:"POST",body:JSON.stringify(L)});J=I.ok;let B=yield I.json();if(!J)throw B.status=I.status,r2(B);return new Promise(function(S){S(B)}).then(k(z))})}function u(g,p,z){return new Promise(function(C,L){g._query(p,z,function(T,J){if(T)return L(T);C(J)})})}function h(g){return new Promise(function(p,z){g._viewCleanup(function(C,L){if(C)return z(C);p(L)})})}function r(g){return function(p){if(p.status===404)return g;throw p}}function v(g,p,z){return X(this,null,function*(){let C="_local/doc_"+g,L={_id:C,keys:[]},T=z.get(g),J=T[0],I=T[1];function B(){return dr(I)?Promise.resolve(L):p.db.get(C).catch(r(L))}function S(l1){return l1.keys.length?p.db.allDocs({keys:l1.keys,include_docs:!0}):Promise.resolve({rows:[]})}function D(l1,w1){let M1=[],j1=new Set;for(let o1 of w1.rows){let t1=o1.doc;if(t1&&(M1.push(t1),j1.add(t1._id),t1._deleted=!J.has(t1._id),!t1._deleted)){let a1=J.get(t1._id);"value"in a1&&(t1.value=a1.value)}}let P1=G2(J);for(let o1 of P1)if(!j1.has(o1)){let t1={_id:o1},a1=J.get(o1);"value"in a1&&(t1.value=a1.value),M1.push(t1)}return l1.keys=o3(P1.concat(l1.keys)),M1.push(l1),M1}let $=yield B(),Q=yield S($);return D($,Q)})}function w(g){return g.sourceDB.get("_local/purges").then(function(p){let z=p.purgeSeq;return g.db.get("_local/purgeSeq").then(function(C){return C._rev}).catch(r(void 0)).then(function(C){return g.db.put({_id:"_local/purgeSeq",_rev:C,purgeSeq:z})})}).catch(function(p){if(p.status!==404)throw p})}function f(g,p,z){var C="_local/lastSeq";return g.db.get(C).catch(r({_id:C,seq:0})).then(function(L){var T=G2(p);return Promise.all(T.map(function(J){return v(J,g,p)})).then(function(J){var I=J.flat();return L.seq=z,I.push(L),g.db.bulkDocs({docs:I})}).then(()=>w(g))})}function O(g){let p=typeof g=="string"?g:g.name,z=Q6[p];return z||(z=Q6[p]=new d2),z}function E(g,p){return X(this,null,function*(){return U2(O(g),function(){return V(g,p)})()})}function V(g,p){return X(this,null,function*(){let z,C,L;function T(o1,t1){let a1={id:C._id,key:h0(o1)};typeof t1<"u"&&t1!==null&&(a1.value=h0(t1)),z.push(a1)}let J=o(g.mapFun,T),I=g.seq||0;function B(){return g.sourceDB.info().then(function(o1){L=g.sourceDB.activeTasks.add({name:"view_indexing",total_items:o1.update_seq-I})})}function S(o1,t1){return function(){return f(g,o1,t1)}}let D=0,$={view:g.name,indexed_docs:D};g.sourceDB.emit("indexing",$);let Q=new d2;function l1(){return X(this,null,function*(){let o1=yield g.sourceDB.changes({return_docs:!0,conflicts:!0,include_docs:!0,style:"all_docs",since:I,limit:p.changes_batch_size}),t1=yield w1();return M1(o1,t1)})}function w1(){return g.db.get("_local/purgeSeq").then(function(o1){return o1.purgeSeq}).catch(r(-1)).then(function(o1){return g.sourceDB.get("_local/purges").then(function(t1){let a1=t1.purges.filter(function(C1,h1){return h1>o1}).map(C1=>C1.docId),B1=a1.filter(function(C1,h1){return a1.indexOf(C1)===h1});return Promise.all(B1.map(function(C1){return g.sourceDB.get(C1).then(function(h1){return{docId:C1,doc:h1}}).catch(r({docId:C1}))}))}).catch(r([]))})}function M1(o1,t1){let a1=o1.results;if(!a1.length&&!t1.length)return;for(let h1 of t1)if(a1.findIndex(function(Z1){return Z1.id===h1.docId})<0){let Z1={_id:h1.docId,doc:{_id:h1.docId,_deleted:1},changes:[]};h1.doc&&(Z1.doc=h1.doc,Z1.changes.push({rev:h1.doc._rev})),a1.push(Z1)}let B1=j1(a1);Q.add(S(B1,I)),D=D+a1.length;let C1={view:g.name,last_seq:o1.last_seq,results_count:a1.length,indexed_docs:D};if(g.sourceDB.emit("indexing",C1),g.sourceDB.activeTasks.update(L,{completed_items:D}),!(a1.length<p.changes_batch_size))return l1()}function j1(o1){let t1=new Map;for(let a1 of o1){if(a1.doc._id[0]!=="_"){z=[],C=a1.doc,C._deleted||i(g.sourceDB,J,C),z.sort(s);let B1=P1(z);t1.set(a1.doc._id,[B1,a1.changes])}I=a1.seq}return t1}function P1(o1){let t1=new Map,a1;for(let B1=0,C1=o1.length;B1<C1;B1++){let h1=o1[B1],m0=[h1.key,h1.id];B1>0&&b1(h1.key,a1)===0&&m0.push(B1),t1.set(W1(m0),h1),a1=h1.key}return t1}try{yield B(),yield l1(),yield Q.finish(),g.seq=I,g.sourceDB.activeTasks.remove(L)}catch(o1){g.sourceDB.activeTasks.remove(L,o1)}})}function n1(g,p,z){z.group_level===0&&delete z.group_level;let C=z.group||z.group_level,L=t(g.reduceFun),T=[],J=isNaN(z.group_level)?Number.POSITIVE_INFINITY:z.group_level;for(let I of p){let B=T[T.length-1],S=C?I.key:null;if(C&&Array.isArray(S)&&(S=S.slice(0,J)),B&&b1(B.groupKey,S)===0){B.keys.push([I.key,I.id]),B.values.push(I.value);continue}T.push({keys:[[I.key,I.id]],values:[I.value],groupKey:S})}p=[];for(let I of T){let B=a(g.sourceDB,L,I.keys,I.values,!1);if(B.error&&B.error instanceof $2)throw B.error;p.push({value:B.error?null:B.output,key:I.groupKey})}return{rows:c(p,z.limit,z.skip)}}function Y(g,p){return U2(O(g),function(){return r1(g,p)})()}function r1(g,p){return X(this,null,function*(){let z,C=g.reduceFun&&p.reduce!==!1,L=p.skip||0;typeof p.keys<"u"&&!p.keys.length&&(p.limit=0,delete p.keys);function T(I){return X(this,null,function*(){I.include_docs=!0;let B=yield g.db.allDocs(I);return z=B.total_rows,B.rows.map(function(S){if("value"in S.doc&&typeof S.doc.value=="object"&&S.doc.value!==null){let $=Object.keys(S.doc.value).sort(),Q=["id","key","value"];if(!($<Q||$>Q))return S.doc.value}let D=q6(S.doc._id);return{key:D[0],id:D[1],value:"value"in S.doc?S.doc.value:null}})})}function J(I){return X(this,null,function*(){let B;if(C?B=n1(g,I,p):typeof p.keys>"u"?B={total_rows:z,offset:L,rows:I}:B={total_rows:z,offset:L,rows:c(I,p.limit,p.skip)},p.update_seq&&(B.update_seq=g.seq),p.include_docs){let S=o3(I.map(d)),D=yield g.sourceDB.allDocs({keys:S,include_docs:!0,conflicts:p.conflicts,attachments:p.attachments,binary:p.binary}),$=new Map;for(let Q of D.rows)$.set(Q.id,Q.doc);for(let Q of I){let l1=d(Q),w1=$.get(l1);w1&&(Q.doc=w1)}}return B})}if(typeof p.keys<"u"){let B=p.keys.map(function($){let Q={startkey:W1([$]),endkey:W1([$,{}])};return p.update_seq&&(Q.update_seq=!0),T(Q)}),D=(yield Promise.all(B)).flat();return J(D)}else{let I={descending:p.descending};p.update_seq&&(I.update_seq=!0);let B,S;if("start_key"in p&&(B=p.start_key),"startkey"in p&&(B=p.startkey),"end_key"in p&&(S=p.end_key),"endkey"in p&&(S=p.endkey),typeof B<"u"&&(I.startkey=p.descending?W1([B,{}]):W1([B])),typeof S<"u"){let $=p.inclusive_end!==!1;p.descending&&($=!$),I.endkey=W1($?[S,{}]:[S])}if(typeof p.key<"u"){let $=W1([p.key]),Q=W1([p.key,{}]);I.descending?(I.endkey=$,I.startkey=Q):(I.startkey=$,I.endkey=Q)}C||(typeof p.limit=="number"&&(I.limit=p.limit),I.skip=L);let D=yield T(I);return J(D)}})}function e1(g){return X(this,null,function*(){return(yield g.fetch("_view_cleanup",{headers:new $0({"Content-Type":"application/json"}),method:"POST"})).json()})}function i1(g){return X(this,null,function*(){try{let p=yield g.get("_local/"+e),z=new Map;for(let B of Object.keys(p.views)){let S=i3(B),D="_design/"+S[0],$=S[1],Q=z.get(D);Q||(Q=new Set,z.set(D,Q)),Q.add($)}let C={keys:G2(z),include_docs:!0},L=yield g.allDocs(C),T={};for(let B of L.rows){let S=B.key.substring(8);for(let D of z.get(B.key)){let $=S+"/"+D;p.views[$]||($=D);let Q=Object.keys(p.views[$]),l1=B.doc&&B.doc.views&&B.doc.views[D];for(let w1 of Q)T[w1]=T[w1]||l1}}let I=Object.keys(T).filter(function(B){return!T[B]}).map(function(B){return U2(O(B),function(){return new g.constructor(B,g.__opts).destroy()})()});return Promise.all(I).then(function(){return{ok:!0}})}catch(p){if(p.status===404)return{ok:!0};throw p}})}function v1(g,p,z){return X(this,null,function*(){if(typeof g._query=="function")return u(g,p,z);if(d0(g))return y(g,p,z);let C={changes_batch_size:g.__opts.view_update_changes_batch_size||lr};if(typeof p!="string")return A(z,p),X6.add(function(){return X(this,null,function*(){let L=yield Z6(g,"temp_view/temp_view",p.map,p.reduce,!0,e);return K6(E(L,C).then(function(){return Y(L,z)}),function(){return L.db.destroy()})})}),X6.finish();{let L=p,T=i3(L),J=T[0],I=T[1],B=yield g.get("_design/"+J);if(p=B.views&&B.views[I],!p)throw new N2(`ddoc ${B._id} has no view named ${I}`);n(B,I),A(z,p);let S=yield Z6(g,L,p.map,p.reduce,!1,e);return z.stale==="ok"||z.stale==="update_after"?(z.stale==="update_after"&&l2(function(){E(S,C)}),Y(S,z)):(yield E(S,C),Y(S,z))}})}function g1(g,p,z){let C=this;typeof p=="function"&&(z=p,p={}),p=p?_(p):{},typeof g=="function"&&(g={map:g});let L=Promise.resolve().then(function(){return v1(C,g,p)});return n3(L,z),L}let u1=G6(function(){let g=this;return typeof g._viewCleanup=="function"?h(g):d0(g)?e1(g):i1(g)});return{query:g1,viewCleanup:u1}}var Y6=vr;function K0(e,o){for(var t=e,n=0,i=o.length;n<i;n++){var a=o[n];if(t=t[a],!t)break}return t}function oo(e,o,t){for(var n=0,i=o.length;n<i-1;n++){var a=o[n];e=e[a]=e[a]||{}}e[o[i-1]]=t}function W2(e,o){return e<o?-1:e>o?1:0}function p0(e){for(var o=[],t="",n=0,i=e.length;n<i;n++){var a=e[n];n>0&&e[n-1]==="\\"&&(a==="$"||a===".")?t=t.substring(0,t.length-1)+a:a==="."?(o.push(t),t=""):t+=a}return o.push(t),o}var gr=["$or","$nor","$not"];function io(e){return gr.indexOf(e)>-1}function F1(e){return Object.keys(e)[0]}function Z2(e){return e[F1(e)]}function g2(e){var o={},t={$or:!0,$nor:!0};return e.forEach(function(n){Object.keys(n).forEach(function(i){var a=n[i];if(typeof a!="object"&&(a={$eq:a}),io(i))if(a instanceof Array){if(t[i]){t[i]=!1,o[i]=a;return}var s=[];o[i].forEach(function(d){Object.keys(a).forEach(function(x){var k=a[x],l=Math.max(Object.keys(d).length,Object.keys(k).length),M=g2([d,k]);Object.keys(M).length<=l||s.push(M)})}),o[i]=s}else o[i]=g2([a]);else{var c=o[i]=o[i]||{};Object.keys(a).forEach(function(d){var x=a[d];if(d==="$gt"||d==="$gte")return hr(d,x,c);if(d==="$lt"||d==="$lte")return ur(d,x,c);if(d==="$ne")return pr(x,c);if(d==="$eq")return mr(x,c);if(d==="$regex")return wr(x,c);c[d]=x})}})}),o}function hr(e,o,t){typeof t.$eq<"u"||(typeof t.$gte<"u"?e==="$gte"?o>t.$gte&&(t.$gte=o):o>=t.$gte&&(delete t.$gte,t.$gt=o):typeof t.$gt<"u"?e==="$gte"?o>t.$gt&&(delete t.$gt,t.$gte=o):o>t.$gt&&(t.$gt=o):t[e]=o)}function ur(e,o,t){typeof t.$eq<"u"||(typeof t.$lte<"u"?e==="$lte"?o<t.$lte&&(t.$lte=o):o<=t.$lte&&(delete t.$lte,t.$lt=o):typeof t.$lt<"u"?e==="$lte"?o<t.$lt&&(delete t.$lt,t.$lte=o):o<t.$lt&&(t.$lt=o):t[e]=o)}function pr(e,o){"$ne"in o?o.$ne.push(e):o.$ne=[e]}function mr(e,o){delete o.$gt,delete o.$gte,delete o.$lt,delete o.$lte,delete o.$ne,o.$eq=e}function wr(e,o){"$regex"in o?o.$regex.push(e):o.$regex=[e]}function ao(e){for(var o in e){if(Array.isArray(e))for(var t in e)e[t].$and&&(e[t]=g2(e[t].$and));var n=e[o];typeof n=="object"&&ao(n)}return e}function so(e,o){for(var t in e){t==="$and"&&(o=!0);var n=e[t];typeof n=="object"&&(o=so(n,o))}return o}function Q2(e){var o=l0(e);so(o,!1)&&(o=ao(o),"$and"in o&&(o=g2(o.$and))),["$or","$nor"].forEach(function(s){s in o&&o[s].forEach(function(c){for(var d=Object.keys(c),x=0;x<d.length;x++){var k=d[x],l=c[k];(typeof l!="object"||l===null)&&(c[k]={$eq:l})}})}),"$not"in o&&(o.$not=g2([o.$not]));for(var t=Object.keys(o),n=0;n<t.length;n++){var i=t[n],a=o[i];(typeof a!="object"||a===null)&&(a={$eq:a}),o[i]=a}return a3(o),o}function a3(e){Object.keys(e).forEach(function(o){var t=e[o];Array.isArray(t)?t.forEach(function(n){n&&typeof n=="object"&&a3(n)}):o==="$ne"?e.$ne=[t]:o==="$regex"?e.$regex=[t]:t&&typeof t=="object"&&a3(t)})}function fr(e){function o(t){return e.map(function(n){var i=F1(n),a=p0(i),s=K0(t,a);return s})}return function(t,n){var i=o(t.doc),a=o(n.doc),s=b1(i,a);return s!==0?s:W2(t.doc._id,n.doc._id)}}function s3(e,o,t){if(e=e.filter(function(s){return G0(s.doc,o.selector,t)}),o.sort){var n=fr(o.sort);e=e.sort(n),typeof o.sort[0]!="string"&&Z2(o.sort[0])==="desc"&&(e=e.reverse())}if("limit"in o||"skip"in o){var i=o.skip||0,a=("limit"in o?o.limit:e.length)+i;e=e.slice(i,a)}return e}function G0(e,o,t){return t.every(function(n){var i=o[n],a=p0(n),s=K0(e,a);return io(n)?xr(n,i,e):K2(i,e,a,s)})}function K2(e,o,t,n){return e?typeof e=="object"?Object.keys(e).every(function(i){var a=e[i];if(i.indexOf("$")===0)return eo(i,o,a,t,n);var s=p0(i);if(n===void 0&&typeof a!="object"&&s.length>0)return!1;var c=K0(n,s);return typeof a=="object"?K2(a,o,t,c):eo("$eq",o,a,s,c)}):e===n:!0}function xr(e,o,t){return e==="$or"?o.some(function(n){return G0(t,n,Object.keys(n))}):e==="$not"?!G0(t,o,Object.keys(o)):!o.find(function(n){return G0(t,n,Object.keys(n))})}function eo(e,o,t,n,i){if(!no[e])throw new Error('unknown operator "'+e+'" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, $nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');return no[e](o,t,n,i)}function v2(e){return typeof e<"u"&&e!==null}function j0(e){return typeof e<"u"}function Mr(e,o){if(typeof e!="number"||parseInt(e,10)!==e)return!1;var t=o[0],n=o[1];return e%t===n}function to(e,o){return o.some(function(t){return e instanceof Array?e.some(function(n){return b1(t,n)===0}):b1(t,e)===0})}function kr(e,o){return o.every(function(t){return e.some(function(n){return b1(t,n)===0})})}function zr(e,o){return e.length===o}function yr(e,o){var t=new RegExp(o);return t.test(e)}function Cr(e,o){switch(o){case"null":return e===null;case"boolean":return typeof e=="boolean";case"number":return typeof e=="number";case"string":return typeof e=="string";case"array":return e instanceof Array;case"object":return{}.toString.call(e)==="[object Object]"}}var no={$elemMatch:function(e,o,t,n){return!Array.isArray(n)||n.length===0?!1:typeof n[0]=="object"&&n[0]!==null?n.some(function(i){return G0(i,o,Object.keys(o))}):n.some(function(i){return K2(o,e,t,i)})},$allMatch:function(e,o,t,n){return!Array.isArray(n)||n.length===0?!1:typeof n[0]=="object"&&n[0]!==null?n.every(function(i){return G0(i,o,Object.keys(o))}):n.every(function(i){return K2(o,e,t,i)})},$eq:function(e,o,t,n){return j0(n)&&b1(n,o)===0},$gte:function(e,o,t,n){return j0(n)&&b1(n,o)>=0},$gt:function(e,o,t,n){return j0(n)&&b1(n,o)>0},$lte:function(e,o,t,n){return j0(n)&&b1(n,o)<=0},$lt:function(e,o,t,n){return j0(n)&&b1(n,o)<0},$exists:function(e,o,t,n){return o?j0(n):!j0(n)},$mod:function(e,o,t,n){return v2(n)&&Mr(n,o)},$ne:function(e,o,t,n){return o.every(function(i){return b1(n,i)!==0})},$in:function(e,o,t,n){return v2(n)&&to(n,o)},$nin:function(e,o,t,n){return v2(n)&&!to(n,o)},$size:function(e,o,t,n){return v2(n)&&Array.isArray(n)&&zr(n,o)},$all:function(e,o,t,n){return Array.isArray(n)&&kr(n,o)},$regex:function(e,o,t,n){return v2(n)&&typeof n=="string"&&o.every(function(i){return yr(n,i)})},$type:function(e,o,t,n){return Cr(n,o)}};function h2(e,o){if(typeof o!="object")throw new Error("Selector error: expected a JSON object");o=Q2(o);var t={doc:e},n=s3([t],{selector:o},Object.keys(o));return n&&n.length===1}var _r=(...e)=>e.flat(1/0),co=(...e)=>{let o=[];for(let t of e)Array.isArray(t)?o=o.concat(co(...t)):o.push(t);return o},lo=typeof Array.prototype.flat=="function"?_r:co;function l3(e){let o={};for(let t of e)Object.assign(o,t);return o}function Br(e,o){let t={};for(let n of o){let i=p0(n),a=K0(e,i);typeof a<"u"&&oo(t,i,a)}return t}function vo(e,o){for(let t=0,n=Math.min(e.length,o.length);t<n;t++)if(e[t]!==o[t])return!1;return!0}function br(e,o){return e.length>o.length?!1:vo(e,o)}function Sr(e,o){e=e.slice();for(let t of o){if(!e.length)break;let n=e.indexOf(t);if(n===-1)return!1;e.splice(n,1)}return!0}function Ar(e){let o={};for(let t of e)o[t]=!0;return o}function Lr(e,o){let t=null,n=-1;for(let i of e){let a=o(i);a>n&&(n=a,t=i)}return t}function ro(e,o){if(e.length!==o.length)return!1;for(let t=0,n=e.length;t<n;t++)if(e[t]!==o[t])return!1;return!0}function Ir(e){return Array.from(new Set(e))}function u2(e){return function(...o){let t=o[o.length-1];if(typeof t=="function"){let n=t.bind(null,null),i=t.bind(null);e.apply(this,o.slice(0,-1)).then(n,i)}else return e.apply(this,o)}}function go(e){e=l0(e),e.index||(e.index={});for(let o of["type","name","ddoc"])e.index[o]&&(e[o]=e.index[o],delete e.index[o]);return e.fields&&(e.index.fields=e.fields,delete e.fields),e.type||(e.type="json"),e}function X2(e){return typeof e=="object"&&e!==null}function jr(e,o,t){let n="",i=o,a=!0;if(["$in","$nin","$or","$and","$mod","$nor","$all"].indexOf(e)!==-1&&(Array.isArray(o)||(n="Query operator "+e+" must be an array.")),["$not","$elemMatch","$allMatch"].indexOf(e)!==-1&&(!Array.isArray(o)&&X2(o)||(n="Query operator "+e+" must be an object.")),e==="$mod"&&Array.isArray(o))if(o.length!==2)n="Query operator $mod must be in the format [divisor, remainder], where divisor and remainder are both integers.";else{let s=o[0],c=o[1];s===0&&(n="Query operator $mod's divisor cannot be 0, cannot divide by zero.",a=!1),(typeof s!="number"||parseInt(s,10)!==s)&&(n="Query operator $mod's divisor is not an integer.",i=s),parseInt(c,10)!==c&&(n="Query operator $mod's remainder is not an integer.",i=c)}if(e==="$exists"&&typeof o!="boolean"&&(n="Query operator $exists must be a boolean."),e==="$type"){let s=["null","boolean","number","string","array","object"],c='"'+s.slice(0,s.length-1).join('", "')+'", or "'+s[s.length-1]+'"';(typeof o!="string"||s.indexOf(o)==-1)&&(n="Query operator $type must be a string. Supported values: "+c+".")}if(e==="$size"&&parseInt(o,10)!==o&&(n="Query operator $size must be a integer."),e==="$regex"&&typeof o!="string"&&(t?n="Query operator $regex must be a string.":o instanceof RegExp||(n="Query operator $regex must be a string or an instance of a javascript regular expression.")),n){if(a){let s=i===null?" ":Array.isArray(i)?" array":" "+typeof i,c=X2(i)?JSON.stringify(i,null,"	"):i;n+=" Received"+s+": "+c}throw new Error(n)}}var Hr=["$all","$allMatch","$and","$elemMatch","$exists","$in","$mod","$nin","$nor","$not","$or","$regex","$size","$type"],Vr=["$in","$nin","$mod","$all"],Or=["$eq","$gt","$gte","$lt","$lte"];function J2(e,o){if(Array.isArray(e))for(let t of e)X2(t)&&J2(t,o);else for(let[t,n]of Object.entries(e))Hr.indexOf(t)!==-1&&jr(t,n,o),Or.indexOf(t)===-1&&Vr.indexOf(t)===-1&&X2(n)&&J2(n,o)}function p2(e,o,t){return X(this,null,function*(){t.body&&(t.body=JSON.stringify(t.body),t.headers=new $0({"Content-type":"application/json"}));let n=yield e.fetch(o,t),i=yield n.json();if(!n.ok){i.status=n.status;let a=J4(i);throw r2(a)}return i})}function Dr(e,o){return X(this,null,function*(){return yield p2(e,"_index",{method:"POST",body:go(o)})})}function Er(e,o){return X(this,null,function*(){return J2(o.selector,!0),yield p2(e,"_find",{method:"POST",body:o})})}function Tr(e,o){return X(this,null,function*(){return yield p2(e,"_explain",{method:"POST",body:o})})}function Pr(e){return X(this,null,function*(){return yield p2(e,"_index",{method:"GET"})})}function Rr(e,o){return X(this,null,function*(){let t=o.ddoc,n=o.type||"json",i=o.name;if(!t)throw new Error("you must provide an index's ddoc");if(!i)throw new Error("you must provide an index's name");let a="_index/"+[t,n,i].map(encodeURIComponent).join("/");return yield p2(e,a,{method:"DELETE"})})}function ho(e,o){for(let t of o)if(e=e[t],e===void 0)return;return e}function qr(e,o,t){return function(n){if(t&&!h2(n,t))return;let i=[];for(let a of e){let s=ho(n,p0(a));if(s===void 0)return;i.push(s)}o(i)}}function Fr(e,o,t){let n=p0(e);return function(i){if(t&&!h2(i,t))return;let a=ho(i,n);a!==void 0&&o(a)}}function Nr(e,o,t){return function(n){t&&!h2(n,t)||o(n[e])}}function $r(e,o,t){return function(n){if(t&&!h2(n,t))return;let i=e.map(a=>n[a]);o(i)}}function Ur(e){return e.every(o=>o.indexOf(".")===-1)}function Gr(e,o,t){let n=Ur(e),i=e.length===1;return n?i?Nr(e[0],o,t):$r(e,o,t):i?Fr(e[0],o,t):qr(e,o,t)}function Kr(e,o){let t=Object.keys(e.fields),n=e.partial_filter_selector;return Gr(t,o,n)}function Wr(){throw new Error("reduce not supported")}function Zr(e,o){let t=e.views[o];if(!t.map||!t.map.fields)throw new Error("ddoc "+e._id+" with view "+o+" doesn't have map.fields defined. maybe it wasn't created by this plugin?")}var r3=Y6("indexes",Kr,Wr,Zr);function d3(e){return e._customFindAbstractMapper?{query:function(t,n){let i=r3.query.bind(this);return e._customFindAbstractMapper.query.call(this,t,n,i)},viewCleanup:function(){let t=r3.viewCleanup.bind(this);return e._customFindAbstractMapper.viewCleanup.call(this,t)}}:r3}function Qr(e){if(!Array.isArray(e))throw new Error("invalid sort json - should be an array");return e.map(function(o){if(typeof o=="string"){let t={};return t[o]="asc",t}else return o})}var Xr=/^_design\//;function Jr(e){let o=[];return typeof e=="string"?o.push(e):o=e,o.map(function(t){return t.replace(Xr,"")})}function uo(e){return e.fields=e.fields.map(function(o){if(typeof o=="string"){let t={};return t[o]="asc",t}return o}),e.partial_filter_selector&&(e.partial_filter_selector=Q2(e.partial_filter_selector)),e}function Yr(e,o){return o.def.fields.map(t=>{let n=F1(t);return K0(e,p0(n))})}function ec(e,o,t){let n=t.def.fields,i=0;for(let a of e){let s=Yr(a.doc,t);if(n.length===1)s=s[0];else for(;s.length>o.length;)s.pop();if(Math.abs(b1(s,o))>0)break;++i}return i>0?e.slice(i):e}function tc(e){let o=l0(e);return delete o.startkey,delete o.endkey,delete o.inclusive_start,delete o.inclusive_end,"endkey"in e&&(o.startkey=e.endkey),"startkey"in e&&(o.endkey=e.startkey),"inclusive_start"in e&&(o.inclusive_end=e.inclusive_start),"inclusive_end"in e&&(o.inclusive_start=e.inclusive_end),o}function nc(e){let o=e.fields.filter(function(t){return Z2(t)==="asc"});if(o.length!==0&&o.length!==e.fields.length)throw new Error("unsupported mixed sorting")}function oc(e,o){if(o.defaultUsed&&e.sort){let t=e.sort.filter(function(n){return Object.keys(n)[0]!=="_id"}).map(function(n){return Object.keys(n)[0]});if(t.length>0)throw new Error('Cannot sort on field(s) "'+t.join(",")+'" when using the default index')}o.defaultUsed}function ic(e){if(typeof e.selector!="object")throw new Error("you must provide a selector when you find()")}function ac(e,o){let t=Object.keys(e),n=o?o.map(F1):[],i;return t.length>=n.length?i=t:i=n,n.length===0?{fields:i}:(i=i.sort(function(a,s){let c=n.indexOf(a);c===-1&&(c=Number.MAX_VALUE);let d=n.indexOf(s);return d===-1&&(d=Number.MAX_VALUE),c<d?-1:c>d?1:0}),{fields:i,sortOrder:o.map(F1)})}function sc(e,o){return X(this,null,function*(){o=go(o);let t=l0(o.index);o.index=uo(o.index),nc(o.index);let n;function i(){return n||(n=c2(JSON.stringify(o)))}let a=o.name||"idx-"+i(),s=o.ddoc||"idx-"+i(),c="_design/"+s,d=!1,x=!1;function k(M){return M._rev&&M.language!=="query"&&(d=!0),M.language="query",M.views=M.views||{},x=!!M.views[a],x?!1:(M.views[a]={map:{fields:l3(o.index.fields),partial_filter_selector:o.index.partial_filter_selector},reduce:"_count",options:{def:t}},M)}if(e.constructor.emit("debug",["find","creating index",c]),yield U0(e,c,k),d)throw new Error('invalid language for ddoc with id "'+c+'" (should be "query")');let l=s+"/"+a;return yield d3(e).query.call(e,l,{limit:0,reduce:!1}),{id:c,name:a,result:x?"exists":"created"}})}function po(e){return X(this,null,function*(){let o=yield e.allDocs({startkey:"_design/",endkey:"_design/\uFFFF",include_docs:!0}),t={indexes:[{ddoc:null,name:"_all_docs",type:"special",def:{fields:[{_id:"asc"}]}}]};return t.indexes=lo(t.indexes,o.rows.filter(function(n){return n.doc.language==="query"}).map(function(n){return(n.doc.views!==void 0?Object.keys(n.doc.views):[]).map(function(a){let s=n.doc.views[a];return{ddoc:n.id,name:a,type:"json",def:uo(s.options.def)}})})),t.indexes.sort(function(n,i){return W2(n.name,i.name)}),t.total_rows=t.indexes.length,t})}var Y2=null,c3={"\uFFFF":{}},rc={queryOpts:{limit:0,startkey:c3,endkey:Y2},inMemoryFields:[]};function cc(e,o){return e.def.fields.some(t=>F1(t)===o)}function lc(e,o){let t=e[o];return F1(t)!=="$eq"}function mo(e,o){let t=o.def.fields.map(F1);return e.slice().sort(function(n,i){let a=t.indexOf(n),s=t.indexOf(i);return a===-1&&(a=Number.MAX_VALUE),s===-1&&(s=Number.MAX_VALUE),W2(a,s)})}function dc(e,o,t){t=mo(t,e);let n=!1;for(let i=0,a=t.length;i<a;i++){let s=t[i];if(n||!cc(e,s))return t.slice(i);i<a-1&&lc(o,s)&&(n=!0)}return[]}function vc(e){let o=[];for(let[t,n]of Object.entries(e))for(let i of Object.keys(n))i==="$ne"&&o.push(t);return o}function gc(e,o,t,n){let i=lo(e,dc(o,t,n),vc(t));return mo(Ir(i),o)}function hc(e,o,t){if(o){let n=br(o,e),i=vo(t,e);return n&&i}return Sr(t,e)}var uc=["$eq","$gt","$gte","$lt","$lte"];function wo(e){return uc.indexOf(e)===-1}function pc(e,o){let t=e[0],n=o[t];return typeof n>"u"?!0:!(Object.keys(n).length===1&&F1(n)==="$ne")}function mc(e,o,t,n){let i=e.def.fields.map(F1);return hc(i,o,t)?pc(i,n):!1}function wc(e,o,t,n){return n.filter(function(i){return mc(i,t,o,e)})}function fc(e,o,t,n,i){let a=wc(e,o,t,n);if(a.length===0){if(i)throw{error:"no_usable_index",message:"There is no index available for this selector."};let d=n[0];return d.defaultUsed=!0,d}if(a.length===1&&!i)return a[0];let s=Ar(o);function c(d){let x=d.def.fields.map(F1),k=0;for(let l of x)s[l]&&k++;return k}if(i){let d="_design/"+i[0],x=i.length===2?i[1]:!1,k=a.find(function(l){return!!(x&&l.ddoc===d&&x===l.name||l.ddoc===d)});if(!k)throw{error:"unknown_error",message:"Could not find that index or could not use that index for the query"};return k}return Lr(a,c)}function xc(e,o){switch(e){case"$eq":return{key:o};case"$lte":return{endkey:o};case"$gte":return{startkey:o};case"$lt":return{endkey:o,inclusive_end:!1};case"$gt":return{startkey:o,inclusive_start:!1}}return{startkey:Y2}}function Mc(e,o){let t=F1(o.def.fields[0]),n=e[t]||{},i=[],a=Object.keys(n),s;for(let c of a){wo(c)&&i.push(t);let d=n[c],x=xc(c,d);s?s=l3([s,x]):s=x}return{queryOpts:s,inMemoryFields:i}}function kc(e,o){switch(e){case"$eq":return{startkey:o,endkey:o};case"$lte":return{endkey:o};case"$gte":return{startkey:o};case"$lt":return{endkey:o,inclusive_end:!1};case"$gt":return{startkey:o,inclusive_start:!1}}}function zc(e,o){let t=o.def.fields.map(F1),n=[],i=[],a=[],s,c;function d(k){s!==!1&&i.push(Y2),c!==!1&&a.push(c3),n=t.slice(k)}for(let k=0,l=t.length;k<l;k++){let M=t[k],_=e[M];if(!_||!Object.keys(_).length){d(k);break}else if(Object.keys(_).some(wo)){d(k);break}else if(k>0){let y="$gt"in _||"$gte"in _||"$lt"in _||"$lte"in _,u=Object.keys(e[t[k-1]]),h=ro(u,["$eq"]),r=ro(u,Object.keys(_));if(y&&!h&&!r){d(k);break}}let b=Object.keys(_),A=null;for(let y of b){let u=_[y],h=kc(y,u);A?A=l3([A,h]):A=h}i.push("startkey"in A?A.startkey:Y2),a.push("endkey"in A?A.endkey:c3),"inclusive_start"in A&&(s=A.inclusive_start),"inclusive_end"in A&&(c=A.inclusive_end)}let x={startkey:i,endkey:a};return typeof s<"u"&&(x.inclusive_start=s),typeof c<"u"&&(x.inclusive_end=c),{queryOpts:x,inMemoryFields:n}}function yc(e){return Object.keys(e).map(function(t){return e[t]}).some(function(t){return typeof t=="object"&&Object.keys(t).length===0})}function Cc(e){return{queryOpts:{startkey:null},inMemoryFields:[Object.keys(e)]}}function _c(e,o){return o.defaultUsed?Cc(e,o):o.def.fields.length===1?Mc(e,o):zc(e,o)}function Bc(e,o){let t=e.selector,n=e.sort;if(yc(t))return Object.assign({},rc,{index:o[0]});let i=ac(t,n),a=i.fields,s=i.sortOrder,c=fc(t,a,s,o,e.use_index),d=_c(t,c),x=d.queryOpts,k=d.inMemoryFields,l=gc(k,c,t,a);return{queryOpts:x,index:c,inMemoryFields:l}}function bc(e){return e.ddoc.substring(8)+"/"+e.name}function Sc(e,o){return X(this,null,function*(){let t=l0(o);t.descending?("endkey"in t&&typeof t.endkey!="string"&&(t.endkey=""),"startkey"in t&&typeof t.startkey!="string"&&(t.limit=0)):("startkey"in t&&typeof t.startkey!="string"&&(t.startkey=""),"endkey"in t&&typeof t.endkey!="string"&&(t.limit=0)),"key"in t&&typeof t.key!="string"&&(t.limit=0),t.limit>0&&t.indexes_count&&(t.original_limit=t.limit,t.limit+=t.indexes_count);let n=yield e.allDocs(t);return n.rows=n.rows.filter(function(i){return!/^_design\//.test(i.id)}),t.original_limit&&(t.limit=t.original_limit),n.rows=n.rows.slice(0,t.limit),n})}function Ac(e,o,t){return X(this,null,function*(){return t.name==="_all_docs"?Sc(e,o):d3(e).query.call(e,bc(t),o)})}function fo(e,o,t){return X(this,null,function*(){o.selector&&(J2(o.selector,!1),o.selector=Q2(o.selector)),o.sort&&(o.sort=Qr(o.sort)),o.use_index&&(o.use_index=Jr(o.use_index)),"limit"in o||(o.limit=25),ic(o);let n=yield po(e);e.constructor.emit("debug",["find","planning query",o]);let i=Bc(o,n.indexes);e.constructor.emit("debug",["find","query plan",i]);let a=i.index;oc(o,a);let s=Object.assign({include_docs:!0,reduce:!1,indexes_count:n.total_rows},i.queryOpts);if("startkey"in s&&"endkey"in s&&b1(s.startkey,s.endkey)>0)return{docs:[]};if(o.sort&&typeof o.sort[0]!="string"&&Z2(o.sort[0])==="desc"&&(s.descending=!0,s=tc(s)),i.inMemoryFields.length||(s.limit=o.limit,"skip"in o&&(s.skip=o.skip)),t)return Promise.resolve(i,s);let d=yield Ac(e,s,a);s.inclusive_start===!1&&(d.rows=ec(d.rows,s.startkey,a)),i.inMemoryFields.length&&(d.rows=s3(d.rows,o,i.inMemoryFields));let x={docs:d.rows.map(function(k){let l=k.doc;return o.fields?Br(l,o.fields):l})};return a.defaultUsed&&(x.warning="No matching index found, create an index to optimize query time."),x})}function Lc(e,o){return X(this,null,function*(){let t=yield fo(e,o,!0);return{dbname:e.name,index:t.index,selector:o.selector,range:{start_key:t.queryOpts.startkey,end_key:t.queryOpts.endkey},opts:{use_index:o.use_index||[],bookmark:"nil",limit:o.limit,skip:o.skip,sort:o.sort||{},fields:o.fields,conflicts:!1,r:[49]},limit:o.limit,skip:o.skip||0,fields:o.fields}})}function Ic(e,o){return X(this,null,function*(){if(!o.ddoc)throw new Error("you must supply an index.ddoc when deleting");if(!o.name)throw new Error("you must supply an index.name when deleting");let t=o.ddoc,n=o.name;function i(a){return Object.keys(a.views).length===1&&a.views[n]?{_id:t,_deleted:!0}:(delete a.views[n],a)}return yield U0(e,t,i),yield d3(e).viewCleanup.apply(e),{ok:!0}})}var W0={};W0.createIndex=u2(function(e){return X(this,null,function*(){if(typeof e!="object")throw new Error("you must provide an index to create");return(d0(this)?Dr:sc)(this,e)})});W0.find=u2(function(e){return X(this,null,function*(){if(typeof e!="object")throw new Error("you must provide search parameters to find()");return(d0(this)?Er:fo)(this,e)})});W0.explain=u2(function(e){return X(this,null,function*(){if(typeof e!="object")throw new Error("you must provide search parameters to explain()");return(d0(this)?Tr:Lc)(this,e)})});W0.getIndexes=u2(function(){return X(this,null,function*(){return(d0(this)?Pr:po)(this)})});W0.deleteIndex=u2(function(e){return X(this,null,function*(){if(typeof e!="object")throw new Error("you must provide an index to delete");return(d0(this)?Rr:Ic)(this,e)})});var xo=W0;function Mo(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let o=Math.random()*16|0;return(e==="x"?o:o&3|8).toString(16)})}var ko=(()=>{let o=class o{constructor(){X4.plugin(xo),this.initDB("register_patient")}initDB(n){this.db=new X4(n,{adapter:"idb"}),console.log(`\u2705 PouchDB Initialized: ${n}`)}addPatient(n){return n._id=Mo(),w0(this.db.put(n)).pipe(f0(i=>i),x0(this.handleError))}getAllPatients(){return w0(this.db.allDocs({include_docs:!0})).pipe(f0(n=>n.rows.map(i=>i.doc)),x0(this.handleError))}getPatientById(n){return w0(this.db.get(n)).pipe(f0(i=>i),x0(this.handleError))}updatePatient(n){return!n._id||!n._rev?m2(()=>new Error("\u274C Patient must have _id and _rev to update")):w0(this.db.put(n)).pipe(f0(i=>i),x0(this.handleError))}deletePatient(n){return!n._id||!n._rev?m2(()=>new Error("\u274C Patient must have _id and _rev to delete")):w0(this.db.remove(n._id,n._rev)).pipe(f0(i=>i),x0(this.handleError))}syncWithServer(){return w0(fetch("https://jsonplaceholder.typicode.com/posts")).pipe(f0(n=>X(null,null,function*(){if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);let i=yield n.json();return console.log("\u2705 Synced data from server:",i),i})),x0(this.handleError))}handleError(n){return console.error("\u274C PouchDB Error:",n),m2(()=>n)}};o.\u0275fac=function(i){return new(i||o)},o.\u0275prov=Q0({token:o,factory:o.\u0275fac,providedIn:"root"});let e=o;return e})();var Hc=["video"],Vc=["canvas"],Oc=["uploadVideo"];function Dc(e,o){e&1&&(H(0,"div",11),k1(1,"input",12)(2,"input",13),H(3,"div",14)(4,"div",15)(5,"label"),U(6,"First Name"),j(),k1(7,"input",16),j(),H(8,"div",15)(9,"label"),U(10,"Last Name"),j(),k1(11,"input",17),j(),H(12,"div",15)(13,"label"),U(14,"Age"),j(),k1(15,"input",18),j(),H(16,"div",15)(17,"label"),U(18,"Age (Years)"),j(),k1(19,"input",19),j(),H(20,"div",20)(21,"label"),U(22,"Gender"),j(),H(23,"div")(24,"label"),k1(25,"input",21),U(26," Male"),j(),H(27,"label"),k1(28,"input",22),U(29," Female"),j(),H(30,"label"),k1(31,"input",23),U(32," Other"),j()()()(),H(33,"label"),U(34,"Address:"),j(),H(35,"div",15),k1(36,"input",24),j()())}function Ec(e,o){if(e&1&&(H(0,"option",42),U(1),j()),e&2){let t=o.$implicit;p1("value",t.name),s1(),N1(t.name)}}function Tc(e,o){if(e&1&&(H(0,"option",42),U(1),j()),e&2){let t=o.$implicit;p1("value",t.name),s1(),N1(t.name)}}function Pc(e,o){if(e&1){let t=$1();H(0,"div",15)(1,"label"),U(2,"State"),j(),H(3,"select",43),_1("change",function(i){O1(t);let a=f1(2);return D1(a.onStateChange(i))}),S1(4,Tc,2,2,"option",33),j()()}if(e&2){let t=f1(2);s1(4),p1("ngForOf",t.stateList)}}function Rc(e,o){if(e&1&&(H(0,"option",42),U(1),j()),e&2){let t=o.$implicit;p1("value",t),s1(),N1(t)}}function qc(e,o){if(e&1&&(H(0,"div",15)(1,"label"),U(2,"District"),j(),H(3,"select",44),S1(4,Rc,2,2,"option",33),j()()),e&2){let t=f1(2);s1(4),p1("ngForOf",t.districtList)}}function Fc(e,o){if(e&1){let t=$1();H(0,"div",11)(1,"div",14)(2,"div",15)(3,"label"),U(4,"Date of Birth"),j(),k1(5,"input",25),j(),H(6,"div",20)(7,"label"),U(8,"Marital Status"),j(),H(9,"div")(10,"label"),k1(11,"input",26),U(12," Single"),j(),H(13,"label"),k1(14,"input",27),U(15," Married"),j(),H(16,"label"),k1(17,"input",28),U(18," Separated"),j(),H(19,"label"),k1(20,"input",29),U(21," Widow"),j()()(),H(22,"div",15)(23,"label"),U(24,"Weight (kg)"),j(),k1(25,"input",30),j(),H(26,"div",15)(27,"label"),U(28,"Height (cm)"),j(),k1(29,"input",31),j(),H(30,"div",15)(31,"label"),U(32,"Country"),j(),H(33,"select",32),_1("change",function(i){O1(t);let a=f1();return D1(a.onCountryChange(i))}),S1(34,Ec,2,2,"option",33),j()(),S1(35,Pc,5,1,"div",34)(36,qc,5,1,"div",34),H(37,"div",15)(38,"label"),U(39,"Block"),j(),k1(40,"input",35),j(),H(41,"div",15)(42,"label"),U(43,"Village"),j(),k1(44,"input",36),j(),H(45,"div",15)(46,"label"),U(47,"Head of Household First Name"),j(),k1(48,"input",37),j(),H(49,"div",15)(50,"label"),U(51,"Head of Household Last Name"),j(),k1(52,"input",38),j(),H(53,"div",15)(54,"label"),U(55,"Mobile"),j(),k1(56,"input",39),j(),H(57,"div",15)(58,"label"),U(59,"Email"),j(),k1(60,"input",40),j(),H(61,"div",15)(62,"label"),U(63,"UID"),j(),k1(64,"input",41),j()()()}if(e&2){let t=f1();s1(34),p1("ngForOf",t.countryList),s1(),p1("ngIf",t.stateList.length),s1(),p1("ngIf",t.districtList.length)}}function Nc(e,o){if(e&1&&(H(0,"option",42),U(1),j()),e&2){let t=o.$implicit,n=o.index;p1("value",t.deviceId),s1(),g0(" ",t.label||"Camera "+(n+1)," ")}}function $c(e,o){if(e&1&&(H(0,"div",52),k1(1,"img",53),j()),e&2){let t=f1(2);s1(),p1("src",t.photoPreviewUrl,oe)}}function Uc(e,o){if(e&1){let t=$1();H(0,"div",11)(1,"div")(2,"label")(3,"b"),U(4,"Camera:"),j()(),H(5,"select",45),_1("change",function(i){O1(t);let a=f1();return D1(a.switchCamera(i))}),S1(6,Nc,2,2,"option",33),j()(),H(7,"div",46)(8,"button",47),_1("click",function(){O1(t);let i=f1();return D1(i.startCamera())}),U(9,"\u{1F3A5} Start Camera"),j(),H(10,"button",47),_1("click",function(){O1(t);let i=f1();return D1(i.captureImage())}),U(11,"\u{1F4F8} Take Snapshot"),j()(),H(12,"div",48),k1(13,"video",49,0),j(),k1(15,"canvas",50,1),S1(17,$c,2,1,"div",51),j()}if(e&2){let t=f1();s1(6),p1("ngForOf",t.videoDevices),s1(11),p1("ngIf",t.photoPreviewUrl)}}function Gc(e,o){e&1&&(H(0,"div")(1,"label"),U(2,"Document Type"),j(),H(3,"select",56)(4,"option",57),U(5,"X-ray"),j(),H(6,"option",58),U(7,"CT Scan"),j(),H(8,"option",59),U(9,"MRI Scan"),j(),H(10,"option",60),U(11,"Dermatoscope"),j(),H(12,"option",61),U(13,"ECG"),j(),H(14,"option",62),U(15,"CAT Scan"),j(),H(16,"option",63),U(17,"Breast Cancer Screening"),j(),H(18,"option",64),U(19,"General Image"),j(),H(20,"option",65),U(21,"Otoscope"),j(),H(22,"option",66),U(23,"Chest Image"),j(),H(24,"option",67),U(25,"General Examination"),j()()())}function Kc(e,o){if(e&1){let t=$1();H(0,"div")(1,"label"),U(2,"Choose File"),j(),H(3,"input",68),_1("change",function(i){O1(t);let a=f1(2);return D1(a.onFileSelected(i))}),j()()}}function Wc(e,o){if(e&1){let t=$1();H(0,"li"),U(1),H(2,"button",47),_1("click",function(){let i=O1(t).index,a=f1(2);return D1(a.removeDocument(i))}),U(3,"\u274C"),j()()}if(e&2){let t=o.$implicit;s1(),se(" ",t.fileName," (",t.type,") ")}}function Zc(e,o){if(e&1){let t=$1();H(0,"div",11)(1,"label")(2,"b"),U(3,"Upload Document/Image"),j()(),H(4,"div")(5,"button",47),_1("click",function(){O1(t);let i=f1();return D1(i.onBrowseClick())}),U(6,"\u{1F4C2} Browse Files"),j(),H(7,"button",47),_1("click",function(){O1(t);let i=f1();return D1(i.onCaptureClick())}),U(8,"\u{1F4F8} Capture Image"),j()(),S1(9,Gc,26,0,"div",54)(10,Kc,4,0,"div",54),H(11,"ul"),S1(12,Wc,4,2,"li",55),j()()}if(e&2){let t,n=f1();s1(9),p1("ngIf",n.showUploadControls),s1(),p1("ngIf",n.showUploadControls&&!n.isCaptureMode),s1(2),p1("ngForOf",(t=n.patientForm.get("documents"))==null?null:t.value)}}function Qc(e,o){if(e&1){let t=$1();H(0,"img",74),_1("click",function(){O1(t);let i=f1().$implicit,a=f1(2);return D1(a.openPhotoPopup(i.profile.imagepath))}),j()}if(e&2){let t=f1().$implicit;p1("src",t.profile.imagepath,oe)}}function Xc(e,o){if(e&1){let t=$1();H(0,"li",76),_1("click",function(){let i=O1(t).$implicit,a=f1(4);return D1(a.openDocPopup(i))}),U(1),j()}if(e&2){let t=o.$implicit;s1(),g0("\u{1F4C4} ",t.fileName)}}function Jc(e,o){if(e&1&&(H(0,"ul"),S1(1,Xc,2,1,"li",75),j()),e&2){let t=f1().$implicit;s1(),p1("ngForOf",t.documents)}}function Yc(e,o){if(e&1){let t=$1();H(0,"tr")(1,"td")(2,"button",47),_1("click",function(){let i=O1(t),a=i.$implicit,s=i.index,c=f1(2);return D1(c.editPatients(a,s))}),U(3,"\u270F Edit"),j(),H(4,"button",47),_1("click",function(){let i=O1(t).$implicit,a=f1(2);return D1(a.delete(i))}),U(5,"\u{1F5D1} Del"),j()(),H(6,"td"),U(7),j(),H(8,"td"),U(9),j(),H(10,"td"),U(11),j(),H(12,"td"),U(13),j(),H(14,"td"),U(15),j(),H(16,"td"),U(17),j(),H(18,"td"),U(19),j(),H(20,"td"),U(21),j(),H(22,"td"),U(23),j(),H(24,"td"),S1(25,Qc,1,1,"img",73),j(),H(26,"td"),S1(27,Jc,2,1,"ul",54),j()()}if(e&2){let t=o.$implicit;s1(7),se("",t.first_name," ",t.last_name),s1(2),N1(t.date_of_birth),s1(2),g0("",t.ageYears,"y"),s1(2),N1(t.gender),s1(2),N1(t.country),s1(2),g0("",t.height," cm"),s1(2),g0("",t.weight," kg"),s1(2),N1(t.mobile),s1(2),N1(t.email),s1(2),p1("ngIf",t.profile==null?null:t.profile.imagepath),s1(2),p1("ngIf",t.documents==null?null:t.documents.length)}}function el(e,o){if(e&1&&(H(0,"div",69)(1,"div",70)(2,"h3"),U(3,"\u{1F4CB} Submitted Patient Records"),j(),H(4,"div"),U(5),j()(),H(6,"div",11)(7,"table",71)(8,"thead")(9,"tr",72)(10,"th"),U(11,"Name"),j(),H(12,"th"),U(13,"DOB"),j(),H(14,"th"),U(15,"Age"),j(),H(16,"th"),U(17,"Gender"),j(),H(18,"th"),U(19,"Country"),j(),H(20,"th"),U(21,"Height"),j(),H(22,"th"),U(23,"Weight"),j(),H(24,"th"),U(25,"Contact"),j(),H(26,"th"),U(27,"Email"),j(),H(28,"th"),U(29,"Photo"),j(),H(30,"th"),U(31,"Docs"),j(),H(32,"th"),U(33,"Actions"),j()()(),H(34,"tbody"),S1(35,Yc,28,12,"tr",55),j()()()()),e&2){let t=f1();s1(5),g0("Total Entries: ",t.submittedPatients.length),s1(30),p1("ngForOf",t.submittedPatients)}}var X9=(()=>{let o=class o{constructor(n,i,a){this.objPouchdbService=n,this.fb=i,this.toastController=a,this.activeSection="section0",this.videoDevices=[],this.selectedCameraId="",this.submittedPatients=[],this.photoPreviewUrl=null,this.editIndex=null,this.photoPopupOpen=!1,this.selectedPhoto=null,this.docPopupOpen=!1,this.selectedDoc=null,this.hoverIndex=null,this.showUploadControls=!1,this.isCaptureMode=!1,this.countryList=[{name:"India",states:[{name:"Maharashtra",districts:["Mumbai","Pune","Nagpur"]},{name:"Karnataka",districts:["Bengaluru","Mysuru","Hubli"]}]},{name:"USA",states:[{name:"California",districts:["Los Angeles","San Francisco"]},{name:"Texas",districts:["Dallas","Austin"]}]}],this.stateList=[],this.districtList=[],Zt({"create-outline":gn,"trash-outline":hn})}openPhotoPopup(n){this.selectedPhoto=n,this.photoPopupOpen=!0}closePhotoPopup(){this.photoPopupOpen=!1,this.selectedPhoto=null}openDocPopup(n){this.selectedDoc=n,this.docPopupOpen=!0}closeDocPopup(){this.docPopupOpen=!1,this.selectedDoc=null}ngOnInit(){return X(this,null,function*(){if(this.patientForm=this.fb.group({_id:[""],_rev:[null],domainwisepid:[0],patientid:[0],first_name:[""],last_name:[""],date_of_birth:[""],age:[""],ageYears:[""],gender:[""],maritalstatus:[""],height:[""],weight:[""],mobile:[""],email:[""],head_of_household_fname:[""],head_of_household_lname:[""],country:[""],state:[""],district:[""],block:[""],village:[""],address:[""],projid:[""],head_of_household_mobile:[""],isAbhaPatient:[!1],profile:this.fb.group({patientid:[0],imagepath:[""],S3URL:[""]}),pastrecord:[null],createdat:[""],createdby:[""],domain:[0],uid:[""],prefix:[null],EhealthId:[""],MRN:[""],password:[""],consentformcheckstatus:[0],fingerPrintTemplate:[""],health_number:[""],health_address:[""],unique_id:[null],nationalId:[null],ethnicity:[null],subscriptionDetails:this.fb.group({subscribedId:[0],familycardid:[null],freeSubcriptionAllocated:[0],completedFreeSubcrition:[0],remainingSubcription:[0],isActive:[null],subcriptionName:[null],subscriptionPlanActivatedOn:[null],subscriptionExpiredOn:[null],isExpaired:[0]}),localId:[""],patient_status:[null],patient_title:[null],postCode:[null],centerName:[null],status:[null],isSync:[!1],imageType:["select"],documents:this.fb.array([]),patientImage:[""]}),yield this.loadPatients(),navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices)try{let n=yield navigator.mediaDevices.enumerateDevices();this.videoDevices=n.filter(i=>i.kind==="videoinput"),this.videoDevices.length>0&&(this.selectedCameraId=this.videoDevices[0].deviceId)}catch(n){console.error("Error enumerating devices:",n)}else console.warn("Media Devices API not supported.")})}loadPatients(){return X(this,null,function*(){try{this.objPouchdbService.getAllPatients().subscribe(n=>{this.submittedPatients=n})}catch(n){console.error("Failed to load patients:",n)}})}get documents(){return this.patientForm.get("documents")}toggleSection(n){this.activeSection=this.activeSection===n?"":n}onCountryChange(n){let i=n.detail.value,a=this.countryList.find(s=>s.name===i);this.stateList=a?.states||[],this.districtList=[],this.patientForm.patchValue({state:"",district:""})}onStateChange(n){let i=n.detail.value,a=this.stateList.find(s=>s.name===i);this.districtList=a?.districts||[],this.patientForm.patchValue({district:""})}startCamera(n){return X(this,null,function*(){if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia){alert("Camera API not supported");return}try{let i=yield navigator.mediaDevices.getUserMedia({video:n?{deviceId:{exact:n}}:!0});this.mediaStream=i,this.video.nativeElement.srcObject=i,yield this.video.nativeElement.play()}catch(i){console.error("Error starting camera:",i)}})}switchCamera(n){this.selectedCameraId=n.target.value,this.selectedCameraId&&this.startCamera(this.selectedCameraId)}switchUploadCamera(n){this.selectedCameraId=n.target.value,this.selectedCameraId&&this.startUploadCamera(this.selectedCameraId)}startUploadCamera(n){return X(this,null,function*(){if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia){alert("Camera API not supported");return}try{let i=yield navigator.mediaDevices.getUserMedia({video:n?{deviceId:{exact:n}}:!0});this.mediaStream=i,this.uploadVideo&&this.uploadVideo.nativeElement&&(this.uploadVideo.nativeElement.srcObject=i,yield this.uploadVideo.nativeElement.play())}catch(i){console.error("Error starting upload camera:",i)}})}captureImage(){let n=this.video.nativeElement,i=this.canvas.nativeElement;i.width=n.videoWidth,i.height=n.videoHeight;let a=i.getContext("2d");if(!a){console.error("Failed to get canvas context.");return}a.drawImage(n,0,0,i.width,i.height);let s=i.toDataURL("image/png");this.documents.push(this.fb.group({fileName:`Capture-${Date.now()}.png`,fileType:"image/png",data:s,type:this.patientForm.value.imageType||"Front"})),this.patientForm.patchValue({patientImage:s}),this.photoPreviewUrl=s,this.stopCamera(),this.video&&this.video.nativeElement&&(this.video.nativeElement.srcObject=null)}stopCamera(){this.mediaStream&&(this.mediaStream.getTracks().forEach(n=>n.stop()),this.mediaStream=null)}onFileSelected(n){let i=n.target.files;!i||i.length===0||Array.from(i).forEach(a=>{let s=new FileReader;s.onload=()=>{this.documents.push(this.fb.group({fileName:a.name,fileType:a.type,data:s.result,type:this.patientForm.value.imageType||"Document"}))},s.readAsDataURL(a)})}removeDocument(n){this.documents.removeAt(n)}savePatients(){return X(this,null,function*(){try{this.patientForm.patchValue({patientImage:this.photoPreviewUrl,documents:this.documents.value});let n=this.patientForm.value;if(console.log("Saving patient data:",n),this.editIndex===null)n._rev||delete n._rev,(!n._id||n._id.trim()==="")&&delete n._id,yield this.objPouchdbService.addPatient(n),yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient saved successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present();else{if(!n._id||n._id.trim()==="")throw new Error("Invalid patient _id for update");let i=yield v3(this.objPouchdbService.getPatientById(n._id));n._rev=i._rev,yield this.objPouchdbService.updatePatient(n),this.editIndex=null,yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient edited successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present()}console.log(`Total patients in DB: ${this.submittedPatients.length}`),this.patientForm.reset(),this.photoPreviewUrl=null}catch(n){console.error("Failed to save patient:",n),yield(yield this.toastController.create({message:"Failed to save patient.",duration:3e3,color:"danger"})).present()}})}editPatients(n,i){this.editIndex=i,this.patientForm.patchValue(V0(H0({},n),{_id:n._id||"",_rev:n._rev||""})),this.photoPreviewUrl=n.profile?.imagepath||null}delete(n){return X(this,null,function*(){try{yield this.objPouchdbService.deletePatient(n),yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient deleted successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present()}catch(i){console.error("Failed to delete patient:",i),yield(yield this.toastController.create({message:"Failed to delete patient.",duration:3e3,color:"danger"})).present()}})}downloadFullPatientRecord(n){let i=JSON.stringify(n,null,2),a=new Blob([i],{type:"application/json"}),s=URL.createObjectURL(a),c=document.createElement("a"),d=new Date().toISOString().replace(/[:.]/g,"-");c.href=s,c.download=`patient-${d}.json`,document.body.appendChild(c),c.click(),document.body.removeChild(c),console.log(`\u2B07 Patient record downloaded: patient-${d}.json`)}onBrowseClick(){this.showUploadControls=!0,this.isCaptureMode=!1}onCaptureClick(){return X(this,null,function*(){this.showUploadControls=!0,this.isCaptureMode=!0;try{let n=yield navigator.mediaDevices.getUserMedia({video:!0});this.mediaStream=n,this.uploadVideo&&this.uploadVideo.nativeElement&&(this.uploadVideo.nativeElement.srcObject=n,yield this.uploadVideo.nativeElement.play())}catch(n){console.error("Error starting upload capture camera:",n)}})}captureUploadImage(){if(!this.uploadVideo||!this.canvas||!this.canvas.nativeElement){console.error("Upload video or canvas element not found.");return}let n=this.uploadVideo.nativeElement,i=this.canvas.nativeElement;i.width=n.videoWidth,i.height=n.videoHeight;let a=i.getContext("2d");if(!a){console.error("Failed to get canvas context.");return}a.drawImage(n,0,0,i.width,i.height);let s=i.toDataURL("image/png");this.documents.push(this.fb.group({fileName:`UploadCapture-${Date.now()}.png`,fileType:"image/png",data:s,type:this.patientForm.value.imageType||"select"})),this.patientForm.patchValue({patientImage:s}),this.photoPreviewUrl=s,this.stopUploadCamera()}stopUploadCamera(){this.mediaStream&&(this.mediaStream.getTracks().forEach(n=>n.stop()),this.mediaStream=null)}};o.\u0275fac=function(i){return new(i||o)(m(ko),m(E3),m(ln))},o.\u0275cmp=P({type:o,selectors:[["app-patient-entry"]],viewQuery:function(i,a){if(i&1&&(k0(Hc,5),k0(Vc,5),k0(Oc,5)),i&2){let s;Y1(s=e0())&&(a.video=s.first),Y1(s=e0())&&(a.canvas=s.first),Y1(s=e0())&&(a.uploadVideo=s.first)}},decls:37,vars:12,consts:[["video",""],["canvas",""],[2,"background","#fff","padding","12px","border-bottom","1px solid #ccc"],[1,"ion-padding"],[3,"ngSubmit","formGroup"],[1,"card"],[1,"card-header",2,"cursor","pointer",3,"click"],["class","card-content",4,"ngIf"],[1,"card-header",3,"click"],["type","submit",3,"disabled"],["class","card","style","margin-top:20px;",4,"ngIf"],[1,"card-content"],["type","hidden","formControlName","_id"],["type","hidden","formControlName","_rev"],[1,"gridp"],[1,"form-group"],["type","text","formControlName","first_name"],["type","text","formControlName","last_name"],["type","number","formControlName","age"],["type","text","formControlName","ageYears"],[1,"form-group","full-width"],["type","radio","value","Male","formControlName","gender"],["type","radio","value","Female","formControlName","gender"],["type","radio","value","Other","formControlName","gender"],["type","text","formControlName","address"],["type","date","formControlName","date_of_birth"],["type","radio","value","Single","formControlName","maritalstatus"],["type","radio","value","Married","formControlName","maritalstatus"],["type","radio","value","Separated","formControlName","maritalstatus"],["type","radio","value","Widow","formControlName","maritalstatus"],["type","number","formControlName","weight"],["type","number","formControlName","height"],["formControlName","country",3,"change"],[3,"value",4,"ngFor","ngForOf"],["class","form-group",4,"ngIf"],["type","text","formControlName","block"],["type","text","formControlName","village"],["type","text","formControlName","head_of_household_fname"],["type","text","formControlName","head_of_household_lname"],["type","tel","formControlName","mobile"],["type","email","formControlName","email"],["type","text","formControlName","uid"],[3,"value"],["formControlName","state",3,"change"],["formControlName","district"],[3,"change"],[1,"upload-buttons"],["type","button",3,"click"],[2,"text-align","center","margin","16px 0"],["autoplay","","muted","","playsinline",""],["hidden",""],["style","text-align:center;margin-top:16px;",4,"ngIf"],[2,"text-align","center","margin-top","16px"],[2,"max-width","300px","border-radius","8px",3,"src"],[4,"ngIf"],[4,"ngFor","ngForOf"],["formControlName","imageType"],["value","73"],["value","74"],["value","97"],["value","117"],["value","118"],["value","136"],["value","140"],["value","148"],["value","156"],["value","163"],["value","168"],["type","file","accept","image/*,application/pdf",3,"change"],[1,"card",2,"margin-top","20px"],[1,"card-header",2,"background","#0077b6","color","#fff"],[2,"width","100%","border-collapse","collapse"],[2,"background","#f4f4f4"],["style","width:45px;height:45px;border-radius:50%;",3,"src","click",4,"ngIf"],[2,"width","45px","height","45px","border-radius","50%",3,"click","src"],[3,"click",4,"ngFor","ngForOf"],[3,"click"]],template:function(i,a){i&1&&(H(0,"header",2)(1,"div")(2,"h2"),U(3,"Register Patient"),j()()(),H(4,"div",3)(5,"form",4),_1("ngSubmit",function(){return a.savePatients()}),H(6,"div",5)(7,"div",6),_1("click",function(){return a.toggleSection("section0")}),H(8,"h3"),U(9," Create ABHA Number "),H(10,"span"),U(11),j()()(),S1(12,Dc,37,0,"div",7),j(),H(13,"div",5)(14,"div",8),_1("click",function(){return a.toggleSection("section1")}),H(15,"h3"),U(16,"Additional Information "),H(17,"span"),U(18),j()()(),S1(19,Fc,65,3,"div",7),j(),H(20,"div",5)(21,"div",8),_1("click",function(){return a.toggleSection("captureImage")}),H(22,"h3"),U(23,"Capture Patient Image "),H(24,"span"),U(25),j()()(),S1(26,Uc,18,2,"div",7),j(),H(27,"div",5)(28,"div",8),_1("click",function(){return a.toggleSection("uploadDocument")}),H(29,"h3"),U(30,"Upload Documents "),H(31,"span"),U(32),j()()(),S1(33,Zc,13,3,"div",7),j(),H(34,"button",9),U(35),j()(),S1(36,el,36,2,"div",10),j()),i&2&&(s1(5),p1("formGroup",a.patientForm),s1(6),N1(a.activeSection==="section0"?"\u25B2":"\u25BC"),s1(),p1("ngIf",a.activeSection==="section0"),s1(6),N1(a.activeSection==="section1"?"\u25B2":"\u25BC"),s1(),p1("ngIf",a.activeSection==="section1"),s1(6),N1(a.activeSection==="captureImage"?"\u25B2":"\u25BC"),s1(),p1("ngIf",a.activeSection==="captureImage"),s1(6),N1(a.activeSection==="uploadDocument"?"\u25B2":"\u25BC"),s1(),p1("ngIf",a.activeSection==="uploadDocument"),s1(),p1("disabled",a.patientForm.invalid),s1(),g0(" ",a.editIndex===null?"Save Patient":"Update Patient"," "),s1(),p1("ngIf",a.submittedPatients.length>0))},dependencies:[T3,b3,H3,V3,C3,S3,j3,A3,_3,B3,L3,I3,dn,f2,k3,O0],styles:['@charset "UTF-8";body[_ngcontent-%COMP%]{font-family:Arial,sans-serif;background:#f8f9fa;margin:0;padding:0}header[_ngcontent-%COMP%]{background:#fff;padding:12px 20px;border-bottom:1px solid #ccc}header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:20px;color:#0077b6}.card[_ngcontent-%COMP%]{background:#fff;margin:15px 0;border-radius:10px;box-shadow:0 2px 6px #0000001a;overflow:hidden}.card-header[_ngcontent-%COMP%]{padding:12px 16px;background:#f4f4f4;cursor:pointer;display:flex;justify-content:space-between;align-items:center}.card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:16px;font-weight:600}.card-content[_ngcontent-%COMP%]{padding:16px}.gridp[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:15px}.full-width[_ngcontent-%COMP%]{grid-column:1/-1}.form-group[_ngcontent-%COMP%]{display:flex;flex-direction:column}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:500;margin-bottom:5px;font-size:14px}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{padding:8px;font-size:14px;border:1px solid #ccc;border-radius:6px;outline:none}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus{border-color:#0077b6}button[_ngcontent-%COMP%]{padding:8px 14px;font-size:14px;border:none;border-radius:6px;cursor:pointer;transition:.3s}button[type=submit][_ngcontent-%COMP%]{width:100%;background:#0077b6;color:#fff;font-weight:700;margin-top:10px}button[type=submit][_ngcontent-%COMP%]:disabled{background:#ccc;cursor:not-allowed}button[_ngcontent-%COMP%]:hover:not(:disabled){opacity:.9}.upload-buttons[_ngcontent-%COMP%]{display:flex;gap:10px;margin-top:10px}.upload-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:#e9ecef}table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse;font-size:14px}table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:8px;border:1px solid #ddd;text-align:center}table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:#f4f4f4;font-weight:600}td[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin:0 3px;background:#e9ecef}td[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background:#ccc}td[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:45px;height:45px;border-radius:50%;border:2px solid #0077b6;cursor:pointer}@media (max-width: 768px){.gridp[_ngcontent-%COMP%]{grid-template-columns:1fr}table[_ngcontent-%COMP%]{font-size:12px}table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:6px}}.table-wrapper[_ngcontent-%COMP%]{width:100%;overflow-x:auto;-webkit-overflow-scrolling:touch;border:1px solid #ddd;border-radius:6px;margin-top:10px}.table-wrapper[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse;min-width:900px}.table-wrapper[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:#f4f4f4;padding:8px;font-size:14px;text-align:left;white-space:nowrap}.table-wrapper[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:8px;font-size:14px;border-top:1px solid #eee;white-space:nowrap}.table-wrapper[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:4px 8px;font-size:13px;border:none;border-radius:4px;background:#0077b6;color:#fff;cursor:pointer}.table-wrapper[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:nth-child(2){background:#d9534f}.table-wrapper[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{opacity:.8}@media (max-width: 768px){.table-wrapper[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table-wrapper[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{font-size:12px;padding:6px}}']});let e=o;return e})();export{pi as a,dn as b,X9 as c};
