{"name": "@types/pouchdb-browser", "version": "6.1.5", "description": "TypeScript definitions for pouchdb-browser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-browser", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "spaulg", "url": "https://github.com/spaulg"}, {"name": "<PERSON>", "githubUsername": "geppy", "url": "https://github.com/geppy"}, {"name": "<PERSON><PERSON>", "githubUsername": "fredgalvao", "url": "https://github.com/fredgalvao"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pouchdb-browser"}, "scripts": {}, "dependencies": {"@types/pouchdb-adapter-http": "*", "@types/pouchdb-adapter-idb": "*", "@types/pouchdb-adapter-websql": "*", "@types/pouchdb-core": "*", "@types/pouchdb-mapreduce": "*", "@types/pouchdb-replication": "*"}, "typesPublisherContentHash": "1290befc67cfce608bbab121dc3a25b6d1bf2df6d93f12068ea5c1188ff8e511", "typeScriptVersion": "4.5"}