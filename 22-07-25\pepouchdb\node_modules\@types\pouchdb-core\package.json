{"name": "@types/pouchdb-core", "version": "7.0.15", "description": "TypeScript definitions for pouchdb-core", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-core", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "spaulg", "url": "https://github.com/spaulg"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "trubit", "url": "https://github.com/trubit"}, {"name": "<PERSON>", "githubUsername": "geppy", "url": "https://github.com/geppy"}, {"name": "<PERSON><PERSON>", "githubUsername": "fredgalvao", "url": "https://github.com/fredgalvao"}, {"name": "<PERSON>", "githubUsername": "TobiasBales", "url": "https://github.com/TobiasBales"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "tiangolo", "url": "https://github.com/tiangolo"}, {"name": "<PERSON>", "githubUsername": "kmoe", "url": "https://github.com/kmoe"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pouchdb-core"}, "scripts": {}, "dependencies": {"@types/debug": "*", "@types/pouchdb-find": "*"}, "typesPublisherContentHash": "5248ad62eece49a44c4388cf4a5881e6af14b5c6a953262d0cecce8fe4867f5b", "typeScriptVersion": "4.8"}