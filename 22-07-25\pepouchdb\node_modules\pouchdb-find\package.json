{"name": "pouchdb-find", "version": "9.0.0", "description": "Easy-to-use query language for PouchDB", "main": "lib/index.js", "keywords": ["pouch", "pouchdb", "plugin", "find", "mango", "query", "couch", "couchdb"], "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/pouchdb/pouchdb.git", "directory": "packages/node_modules/pouchdb-find"}, "module": "./lib/index.es.js", "browser": {"./lib/index.js": "./lib/index-browser.js", "./lib/index.es.js": "./lib/index-browser.es.js"}, "dependencies": {"pouchdb-abstract-mapreduce": "9.0.0", "pouchdb-collate": "9.0.0", "pouchdb-errors": "9.0.0", "pouchdb-fetch": "9.0.0", "pouchdb-md5": "9.0.0", "pouchdb-selector-core": "9.0.0", "pouchdb-utils": "9.0.0"}, "files": ["lib", "dist"]}