{"name": "@types/pouchdb-adapter-cordova-sqlite", "version": "1.0.4", "description": "TypeScript definitions for pouchdb-adapter-cordova-sqlite", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/pouchdb-adapter-cordova-sqlite", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "spaulg", "url": "https://github.com/spaulg"}, {"name": "<PERSON>", "githubUsername": "geppy", "url": "https://github.com/geppy"}, {"name": "<PERSON><PERSON>", "githubUsername": "fredgalvao", "url": "https://github.com/fredgalvao"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/coffeymatt"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/pouchdb-adapter-cordova-sqlite"}, "scripts": {}, "dependencies": {"@types/pouchdb-core": "*"}, "typesPublisherContentHash": "9edd91412df60146aa29fef25f68138f36c1084ea2b094f1e9a945e11396fed5", "typeScriptVersion": "4.5"}