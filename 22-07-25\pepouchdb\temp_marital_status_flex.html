<!-- <div style="display: flex; align-items: center; gap: 16px;">
  <ion-radio-group formControlName="maritalstatus" style="display: flex; flex-direction: row; align-items: center; gap: 8px;">
    <ion-item lines="none" style="min-width: auto; padding: 0;">
      <ion-label>Single</ion-label>
      <ion-radio slot="start" value="Single"></ion-radio>
    
    
      <ion-label>Married</ion-label>
      <ion-radio slot="start" value="Married"></ion-radio>
    
    
      <ion-label>Separated</ion-label>
      <ion-radio slot="start" value="Separated"></ion-radio>
    
    
      <ion-label>Widow</ion-label>
      <ion-radio slot="start" value="Widow"></ion-radio>
    </ion-item>
  </ion-radio-group>
</div> -->
